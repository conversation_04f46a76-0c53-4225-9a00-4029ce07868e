install_poetry:
	pip install poetry==1.8.3
	poetry config http-basic.azure-packaging $$POETRY_HTTP_BASIC_AZURE_USERNAME $$POETRY_HTTP_BASIC_AZURE_PASSWORD

install:
	poetry add quanthub-pipeline@latest
	poetry install

install_dev:
	poetry add quanthub-pipeline@latest
	poetry install --with dev

# install_all:
# 	poetry install --with dev,local

uninstall_all:
	pip uninstall -y -r <(pip freeze)

# check:
# 	black src src tests --check
# 	isort src tests --check-only --diff
# 	mypy --show-error-codes src/quanthub/structures

# mypy:
# 	mypy --show-error-codes src

# format:
# 	black src tests
# 	isort src tests

test:
	pytest ./tests --cov=se_data_pipeline --cov-report=xml --cov-report=term --cov-report=html

build:
	poetry build
