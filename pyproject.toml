[tool.poetry]
name = "se-data-pipeline"
version = "0.4.0"
description = "Sales Enablement data processing tools for ETL pipelines"
authors = [
    "<PERSON><PERSON><PERSON> <ole<PERSON>ii_verte<PERSON>@epam.com>",
    "<PERSON><PERSON> <<EMAIL>>"
]
readme = "README.md"
packages = [{include = "se_data_pipeline"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
pandas = "^2.1.0"
openpyxl = "^3.1.2"
pandera = "^0.18.0"
xlsxwriter = "^3.1.9"
unidecode = "^1.3.8"
langchain = ">=0.2.7,<0.3.0"
langchain-community = ">=0.2.7,<0.3.0"
sentence-transformers = "2.6.1"
transformers = "4.39.3"
openai = "^1.16.2"
langchain-openai = "^0.1.1"
lark = "^1.1.9"
qdrant-client = "1.11.1"
sklearn-pandas = "^2.2.0"
imbalanced-learn = "^0.12.4"
loguru = "^0.7.2"
scikit-learn = "^1.5.2"
quanthub-advanced-client = "0.1.17"
torch = [
    { version = "2.5.1", source = "PyPI", platform = "darwin" },
    { version = "2.5.1+cpu", source = "pytorch_cpu", platform = "linux" },
    { version = "2.5.1+cpu", source = "pytorch_cpu", platform = "win32" },
]
azure-core = "^1.32.0"
azure-identity = "^1.20.0"
azure-keyvault-secrets = "^4.9.0"
azure-storage-blob = "^12.24.1"
azure-storage-queue = "^12.12.0"

[tool.poetry.group.dev.dependencies]
black = "^24.2.0"
pytest = "^8.0.2"
pytest-cov = "^6.1.1"
isort = "^5.13.2"
pylint = "^3.1.0"
poetry = "^1.8.1"


[[tool.poetry.source]]
name = "azure-packaging"
url = "https://pkgs.dev.azure.com/iDataEPAM/iData/_packaging/python-feed/pypi/simple"
priority = "supplemental"

[[tool.poetry.source]]
name = "pytorch_cpu"
url = "https://download.pytorch.org/whl/cpu"
priority = "explicit"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
