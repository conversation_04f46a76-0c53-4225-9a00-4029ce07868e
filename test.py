from se_data_pipeline.component.glossary import CodeListManager
import pandas as pd

source_key_people = pd.read_csv("migration/source_datasets/SR-KEY_PEOPLE(11.0.0).csv")
# new_title_ids = pd.read_csv("migration/source_datasets/remaped_titles.csv")
# mapping = new_title_ids.set_index("JOB_TITLE_S")["NEW_JOB_TITLE_ID"].to_dict()
# test = source_key_people.copy()
# test["JOB_TITLE_ID"] = test["JOB_TITLE_S"].map(mapping)
print("done")
