docker build -t dev-se-data-pipeline .

docker run -it --name c-se-data-pipeline -v $(pwd):/app coreimfeiddev001.azurecr.io/base-images/quanthub-alpine-python-runner:2.1-prod /bin/bash
docker run -it --name c-se-data-pipeline -v $(pwd):/app dev-se-data-pipeline /bin/bash


docker exec -it c-se-data-pipeline /bin/bash

poetry shell
export $(grep -v '^#' /app/.env | xargs)


runbook
1. Deploy artifacts (codelists, dsd)
2. Create representation map for location identification
3. Deploy datasets
4. Create ingestion pipelines
5. Create transformations and run them for financials and top outsource metrics