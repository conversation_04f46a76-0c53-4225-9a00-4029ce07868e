from quanthub.utils.config import configure_quanthub
from quanthub.transformations.utils import init_local_config

configure_quanthub()

from se_data_pipeline.component.az import with_azure_storage, az_secrets
from se_data_pipeline.component.data_processor import DataProcessor
from se_data_pipeline.component.provider import SALES_RESEARCH
from se_data_pipeline.component.logger import logger


@with_azure_storage
def main(*args, **kwargs):
    """
    Main function to process data using the DataProcessor.

    Args:
        kwargs (dict): Keyword arguments including message, input_path, output_path, and logs_folder.
    """
    init_local_config(az_secrets.get_secret("WORKSPACE"))

    logger.info("STARTING DATA PROCESSING".center(50, "="))

    DataProcessor(
        output_provider=SALES_RESEARCH,
        input_path=kwargs["input_path"],
        output_path=kwargs["output_path"],
    ).process_data()

    logger.info("DATA PROCESSING FINISHED".center(50, "="))


if __name__ == "__main__":
    main(
        input_path="./input_data/",
        output_path="./output_data/",
    )
