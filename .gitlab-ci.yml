variables:
  TOOLS_IMAGE: "registry.deltixhub.com/deltix.docker/devops/tools:0.105-master"
  PYTHON_IMAGE: "dockerhub.deltixhub.com/python:3.10"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

stages:
  - test
  - prepare
  - build
  - publish
  - trigger
  - deploy

default:
  image: ${PYTHON_IMAGE}
  before_script:
    - python --version ; pip --version  # For debugging
    - pip install virtualenv
    - virtualenv venv
    - source venv/bin/activate

"Test Build":
  stage: test
  script:
    - make install_poetry
    - make install_dev
    # - make check
    - make test
    - make build
  cache: {}
    # key: ${CI_COMMIT_REF_SLUG}
    # paths:
    #   - .cache/pip
    #   - venv/
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov/
    expire_in: 1 week
  tags:
    - AWS
    - DockerExecutor
  rules:
    - if: "$CI_PIPELINE_SOURCE == 'merge_request_event' || ($CI_COMMIT_BRANCH =~ /^(integration-.*)|(master)$/ && $CI_COMMIT_MESSAGE !~ /^RobotBuild/ && $CI_COMMIT_MESSAGE !~ /^\\[snapshot\\].*/)"

"Prepare":
  image: ${TOOLS_IMAGE}
  stage: prepare
  before_script:
    - apk update
    - apk add --no-cache git-lfs
  script:
    - git tag -l | xargs git tag -d
    - git fetch --tags
    - version=$(grep '^version = ' pyproject.toml | cut -d '"' -f2) || version=0.1.0
    - ver=$(echo $version | cut -f-2 -d '.') || ver=0.1
    - minor_tag=$(echo $version | cut -f 3 -d '.' | cut -f 1 -d '-') || minor_tag=0
    - release_tag=${ver}.$((minor_tag+1))
    # update pyproject.toml version
    - sed -i "/^\[tool\.poetry\]/,/^\[/ s/version = \"[^\"]*\"/version = \"${release_tag}\"/" pyproject.toml
    - git remote set-url origin https://$GITLAB_ROBOT_NAME:$CI_GIT_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git
    - git config user.name $GITLAB_ROBOT_NAME
    - git config user.email ${GITLAB_ROBOT_EMAIL}
    - git diff --quiet && git diff --staged --quiet || git commit -am "RobotBuild ${release_tag}"
    - git tag ${release_tag}
    - git push origin HEAD:${CI_COMMIT_REF_NAME} --tags
  cache: { }
  dependencies: [ ]
  tags:
    - AWS
    - DockerExecutor
  rules:
    - if: "$CI_COMMIT_BRANCH =~ /^release-.*$/ && $CI_COMMIT_MESSAGE !~ /^RobotBuild/ && $CI_COMMIT_MESSAGE !~ /^\\[snapshot\\].*/"

"Build":
  stage: build
  script:
    - make install_poetry
    - make install_dev
    - make build
  artifacts:
    paths:
      - dist/*.whl
      - dist/*.tar.gz
    expire_in: 1 hour
  cache: {}
    # key: build-venv
    # paths:
    #   - .cache/pip
    #   - venv/
  rules:
    - if: "$CI_COMMIT_TAG"
  tags:
    - AWS
    - DockerExecutor

"Publish":
  stage: publish
  variables:
    TWINE_USERNAME: ${IDATA_ARTIFACTS_PUBLICATION_USER}
    TWINE_PASSWORD: ${IDATA_ARTIFACTS_PUBLICATION_PASS}
    TWINE_REPOSITORY_URL: ${IDATA_PYTHON_FEED_UPLOAD}
  script:
    - pip install twine==5.1.1
    - python -m twine upload dist/*
  cache: {}
    # key: publish-venv
    # paths:
    #   - .cache/pip
    #   - venv/
  rules:
    - if: "$CI_COMMIT_TAG"
  tags:
    - AWS
    - DockerExecutor
  needs:
    - "Build"

"Trigger":
  stage: trigger
  variables:
    PACKAGES: "se-data-pipeline==${CI_COMMIT_TAG} --extra-index-url ${TORCH_REPO_URL}"
    PYTHON_IMAGE: "coreimfeiddev001.azurecr.io/base-images/quanthub-alpine-python-runner:2.1-prod"
  trigger:
    project: Deltix/migapp/cloudcord/build-wheel-for-platform
  rules:
    - if: "$CI_COMMIT_TAG"
  needs:
    - "Publish"


"Build and Push Image to ACR":
  image: docker:28.1-dind
  stage: deploy
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    IMAGE_NAME: "se-data-pipeline"
  services:
    - name: docker:28.1-dind
  before_script:
    - docker login $ACR_NAME -u $ACR_CICD_CLIENT_ID -p $ACR_CICD_PASSWORD
  script:
    - |
      docker build \
        --build-arg PIP_EXTRA_INDEX_URL=$PIP_EXTRA_INDEX_URL \
        --build-arg PIPELINE_VERSION=$CI_COMMIT_TAG \
        -t $IMAGE_NAME:$CI_COMMIT_TAG .
    - docker tag $IMAGE_NAME:$CI_COMMIT_TAG $ACR_NAME/$IMAGE_NAME:$CI_COMMIT_TAG
    - docker tag $IMAGE_NAME:$CI_COMMIT_TAG $ACR_NAME/$IMAGE_NAME:latest
    - docker push $ACR_NAME/$IMAGE_NAME:$CI_COMMIT_TAG
    - docker push $ACR_NAME/$IMAGE_NAME:latest
  rules:
    - if: "$CI_COMMIT_TAG"
  tags:
    - AWS
    - DockerExecutor
  needs:
    - "Trigger"
