version: '3'
services:
  pipeline-proxy:
    image: coreimfeiddev001.azurecr.io/quanthub/pipeline-proxy:7.0
    environment:
      - DEBUG_OPTS=${DEBUG_OPTS}
      - SPRING_PROFILES_ACTIVE=${PIPELINE_PROXY_SPRING_PROFILES}
      - APP_LOG_LEVEL=${APP_LOG_LEVEL}
      - PIPELINE_PROXY_PORT=${PIPELINE_PROXY_PORT}
      - PIPELINE_PROXY_WEBSOCKET_MESSAGE_BUFFER=${PIPELINE_PROXY_WEBSOCKET_MESSAGE_BUFFER}
      - PIPELINE_PROXY_REWRITE_WORKSPACE=${PIPELINE_PROXY_REWRITE_WORKSPACE}
      - PIPELINE_PROXY_WORKSPACE_ID=${PIPELINE_PROXY_WORKSPACE_ID}
      - LOCAL_TENANT_ID=${LOCAL_TENANT_ID}
      - LOCAL_CLIENT_ID=${LOCAL_CLIENT_ID}
      - LOCAL_CLIENT_SECRET=${LOCAL_CLIENT_SECRET}
      - PIPELINE_PROXY_SPA_CLIENT_ID=${PIPELINE_PROXY_SPA_CLIENT_ID}
      - PIPELINE_PROXY_USER_SCOPE=${PIPELINE_PROXY_USER_SCOPE}
      - PIPELINE_PROXY_USER_NAME=${PIPELINE_PROXY_USER_NAME}
      - PIPELINE_PROXY_USER_PASSWORD=${PIPELINE_PROXY_USER_PASSWORD}
      - SdmxRegistryHost=${SdmxRegistryHost}
      - UserInfoHost=${UserInfoHost}
    ports:
      - '10000:10000'
      - target: 5006
        published: 10006
        protocol: tcp
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 512M
