FROM python:3.10-slim

ARG PIP_EXTRA_INDEX_URL
ARG PIPELINE_VERSION

WORKDIR /app

RUN apt-get update && \
    apt-get install -y --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

RUN mkdir /app/dist
COPY *dist/se_data_pipeline-${PIPELINE_VERSION}-py3-none-any.whl /app/dist/

# Try local installation first, if fails then install from repository
RUN if [ -f /app/dist/se_data_pipeline-${PIPELINE_VERSION}-py3-none-any.whl ]; then \
        pip install /app/dist/se_data_pipeline-${PIPELINE_VERSION}-py3-none-any.whl; \
    else \
        pip install se-data-pipeline==${PIPELINE_VERSION}; \
    fi

RUN rm -rf /app/dist

# Handler file and cached data
COPY handler.py handler.py
COPY index.pkl index.pkl

ENTRYPOINT ["python", "/app/handler.py"]
