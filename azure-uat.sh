#################################################
# Azure Resources Configuration
#################################################

RESOURCE_GROUP="epm-ussr"
LOCATION="westeurope"

KEY_VAULT_NAME="uat-ussr-kv"
IDENTITY_NAME="ReportProcessor"

ACR_NAME="ussracr"
ACCOUNT_REPORT_CONTAINER="uat-se-data-pipeline"
ACR_CICD_SP_NAME="acr-cicd-sp"

STORAGE_ACCOUNT="uatreportfiles"
ACCOUNT_REPORT_INPUT="account-report-input"
ACCOUNT_REPORT_OUTPUT="account-report-output"
ACCOUNT_REPORT_QUEUE_NAME="account-report-messages"

ACCOUNT_REPORT_SUB="UATAccountReportEvents"

LOG_ANALYTICS_WORKSPACE="uat-logs"
CONTAINER_ENV="uat-container-env"
ACCOUNT_REPORT_JOB="uat-account-report-job"

#################################################
# Network Configuration
#################################################
# IMPORTANT: The vnet should be added to the EPAM VPN through the
# "Configuring Azure-EPAM interconnection (VPN)" process described in the KB 

VNET_NAME="epm-ussr-container-job-vnet"
CONTAINER_JOB_SUBNET_NAME="container-job"
SECURITY_GROUP_NAME="epm-ussr-westeurope-sg"

#################################################
# Virtual Network and Subnet Creation
#################################################

# Create Virtual Network with Base Subnet
# az network vnet create \
#   --resource-group $RESOURCE_GROUP \
#   --name $VNET_NAME \
#   --location $LOCATION \
#   --address-prefixes 10.0.0.0/25 \
#   --subnet-name $CONTAINER_JOB_SUBNET_NAME \
#   --subnet-prefix 10.0.0.0/25

# Create Subnet for Container Jobs
# az network vnet subnet update \
#   --resource-group $RESOURCE_GROUP \
#   --vnet-name $VNET_NAME \
#   --name $CONTAINER_JOB_SUBNET_NAME \
#   --address-prefixes ********/24 \
#   --service-endpoints Microsoft.Storage Microsoft.KeyVault \
#   --delegations Microsoft.App/environments \
#   --disable-private-endpoint-network-policies true \
#   --disable-private-link-service-network-policies true \
#   --network-security-group $SECURITY_GROUP_NAME

# Disable default outbound access (Private Subnet mode)
# az network vnet subnet update \
#   --resource-group $RESOURCE_GROUP \
#   --vnet-name $VNET_NAME \
#   --name $CONTAINER_JOB_SUBNET_NAME \
#   --default-outbound false

#################################################
# Key Vault Creation and Configuration
#################################################

# Create Key Vault
az keyvault create \
  --name $KEY_VAULT_NAME \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku standard

# Configure Key Vault network rules
az keyvault network-rule add \
  --name $KEY_VAULT_NAME \
  --resource-group $RESOURCE_GROUP \
  --vnet-name $VNET_NAME \
  --subnet $CONTAINER_JOB_SUBNET_NAME

# Set default network access rule for Key Vault to "Deny"
az keyvault update \
  --name $KEY_VAULT_NAME \
  --resource-group $RESOURCE_GROUP \
  --default-action Deny

#################################################
# Log Analytics Workspace Creation
#################################################

az monitor log-analytics workspace create \
  --resource-group $RESOURCE_GROUP \
  --workspace-name $LOG_ANALYTICS_WORKSPACE \
  --location $LOCATION

#################################################
# Storage Account Creation and Configuration
#################################################

# Create Storage Account
az storage account create \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku Standard_GRS \
  --kind StorageV2 \
  --enable-hierarchical-namespace true \
  --allow-blob-public-access false \
  --min-tls-version TLS1_2

# Add network rules to allow access from specified subnets
az storage account network-rule add \
  --resource-group $RESOURCE_GROUP \
  --account-name $STORAGE_ACCOUNT \
  --vnet-name $VNET_NAME \
  --subnet $CONTAINER_JOB_SUBNET_NAME

# Set default network access rule for the Storage Account to "Deny"
az storage account update \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --default-action Deny

# Create Storage Containers
az storage container create \
  --name $ACCOUNT_REPORT_INPUT \
  --account-name $STORAGE_ACCOUNT \
  --auth-mode login

az storage container create \
  --name $ACCOUNT_REPORT_OUTPUT \
  --account-name $STORAGE_ACCOUNT \
  --auth-mode login

# Create Storage Queue
az storage queue create \
  --name $ACCOUNT_REPORT_QUEUE_NAME \
  --account-name $STORAGE_ACCOUNT \
  --auth-mode login

#################################################
# Managed Identity Creation and Role Assignments
#################################################

# Create User-Assigned Managed Identity
az identity create \
  --name $IDENTITY_NAME \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION

# Retrieve IDs
PRINCIPAL_ID=$(az identity show --name $IDENTITY_NAME --resource-group $RESOURCE_GROUP --query principalId --output tsv)
IDENTITY_ID=$(az identity show --name $IDENTITY_NAME --resource-group $RESOURCE_GROUP --query id --output tsv)
ACR_ID=$(az acr show --name $ACR_NAME --resource-group $RESOURCE_GROUP --query id --output tsv)
STORAGE_ID=$(az storage account show --name $STORAGE_ACCOUNT --resource-group $RESOURCE_GROUP --query id --output tsv)
QUEUE_ENDPOINT_ID=$(az storage account show --name $STORAGE_ACCOUNT --resource-group $RESOURCE_GROUP --query id --output tsv)/queueservices/default/queues/$ACCOUNT_REPORT_QUEUE_NAME
KEY_VAULT_ID=$(az keyvault show --name $KEY_VAULT_NAME --resource-group $RESOURCE_GROUP --query id --output tsv)
CONTAINER_JOB_SUBNET_ID=$(az network vnet subnet show --resource-group $RESOURCE_GROUP --vnet-name $VNET_NAME --name $CONTAINER_JOB_SUBNET_NAME --query id --output tsv)
LOG_ANALYTICS_WORKSPACE_ID=$(az monitor log-analytics workspace show --resource-group $RESOURCE_GROUP --workspace-name $LOG_ANALYTICS_WORKSPACE --query customerId --output tsv)
LOG_ANALYTICS_WORKSPACE_KEY=$(az monitor log-analytics workspace get-shared-keys --resource-group $RESOURCE_GROUP --workspace-name $LOG_ANALYTICS_WORKSPACE --query primarySharedKey --output tsv)

# Assign Roles
az role assignment create --assignee $PRINCIPAL_ID --role "AcrPull" --scope $ACR_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Storage Queue Data Contributor" --scope $STORAGE_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Storage Queue Data Reader" --scope $STORAGE_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Storage Blob Data Contributor" --scope $STORAGE_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Storage Blob Data Reader" --scope $STORAGE_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Key Vault Secrets User" --scope $KEY_VAULT_ID

# Verify all role assignments
az role assignment list --assignee $PRINCIPAL_ID --all --output table

#################################################
# CICD Service Principal Creation and Role Assignments
#################################################

# Create Service Principal
SP_CREATION=$(az ad sp create-for-rbac --name $ACR_CICD_SP_NAME --skip-assignment)
ACR_SP_ID=$(echo $SP_CREATION | jq -r '.appId')
ACR_SP_PASSWORD=$(echo $SP_CREATION | jq -r '.password')
ACR_SP_TENANT=$(echo $SP_CREATION | jq -r '.tenant')

# Assign Roles
az role assignment create --assignee $ACR_SP_ID --role "AcrPull" --scope $ACR_ID
az role assignment create --assignee $ACR_SP_ID --role "AcrPush" --scope $ACR_ID

# Verify role assignment
az role assignment list --assignee $ACR_SP_ID --scope $ACR_ID --output table

#################################################
# Event Subscription Creation
#################################################

az eventgrid event-subscription create \
  --source-resource-id $STORAGE_ID \
  --name $ACCOUNT_REPORT_SUB \
  --endpoint-type storagequeue \
  --endpoint $QUEUE_ENDPOINT_ID \
  --included-event-types Microsoft.Storage.DirectoryCreated \
  --subject-begins-with /blobServices/default/containers/$ACCOUNT_REPORT_INPUT

#################################################
# Container App Environment and Job Creation
#################################################

# Create Container App Environment
az containerapp env create \
  --name $CONTAINER_ENV \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --infrastructure-subnet-resource-id $CONTAINER_JOB_SUBNET_ID \
  --logs-workspace-id $LOG_ANALYTICS_WORKSPACE_ID \
  --logs-workspace-key $LOG_ANALYTICS_WORKSPACE_KEY

# preview parameter, set identity manually instead
# --scale-rule-identity $IDENTITY_ID \
# Create Container App Job
az containerapp job create \
  --name $ACCOUNT_REPORT_JOB \
  --resource-group $RESOURCE_GROUP \
  --environment $CONTAINER_ENV \
  --trigger-type "Event" \
  --replica-timeout "1800" \
  --min-executions "0" \
  --max-executions "3" \
  --replica-retry-limit "0" \
  --polling-interval "60" \
  --scale-rule-name "queue" \
  --scale-rule-type "azure-queue" \
  --scale-rule-metadata "accountName=$STORAGE_ACCOUNT" "queueName=$ACCOUNT_REPORT_QUEUE_NAME" "queueLength=1" \
  --image "$ACR_NAME.azurecr.io/$ACCOUNT_REPORT_CONTAINER" \
  --cpu "1" \
  --memory "2Gi" \
  --registry-server "$ACR_NAME.azurecr.io" \
  --mi-user-assigned $IDENTITY_ID \
  --registry-identity $IDENTITY_ID \
  --env-vars "AZURE_STORAGE_QUEUE_NAME=$ACCOUNT_REPORT_QUEUE_NAME"

# Assign roles for Container App Job
CONTAINER_JOB_ID=$(az containerapp job show --name $ACCOUNT_REPORT_JOB --resource-group $RESOURCE_GROUP --query id --output tsv)
az role assignment create --assignee $PRINCIPAL_ID --role "Container Apps Jobs Contributor" --scope $CONTAINER_JOB_ID
az role assignment create --assignee $PRINCIPAL_ID --role "Container Apps Jobs Reader" --scope $CONTAINER_JOB_ID

# Assign identity to Container App Job
az containerapp job identity assign \
  --name $ACCOUNT_REPORT_JOB \
  --resource-group $RESOURCE_GROUP \
  --user-assigned $IDENTITY_NAME
