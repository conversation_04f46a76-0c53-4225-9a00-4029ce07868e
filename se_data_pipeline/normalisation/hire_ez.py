from typing import Dict

import pandas as pd

from se_data_pipeline.component.base import Normaliser, NormalizationMixin
from se_data_pipeline.component.llm import LLMDataHandler


class HireEZNormaliser(Normaliser, NormalizationMixin):
    """Normalises HireEZ data."""

    def __init__(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        llm_data_handler: LLMDataHandler,
    ):
        """
        Initialises HireEZNormaliser.

        Args:
            sheet_df_map (Dict[str, pd.DataFrame]): A dictionary mapping sheet names to dataframes.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for text processing.
        """
        self.sheet_df_map = sheet_df_map.copy()
        self.llm_data_handler = llm_data_handler

    def normalise(self) -> Dict[str, pd.DataFrame]:
        """
        Normalises the data in the sheet_df_map.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to normalised dataframes.
        """
        self.set_column_names()
        self.normalise_column_names()
        self.rename_df_columns()
        return self.sheet_df_map

    def translate_text(self):
        """Translates and normalises text in specified columns using the LLM."""
        target_mapping = {
            ("Locations", "location"),
        }
        self._translate_text(target_mapping)

    def rename_df_columns(self):
        """Renames specific columns in specified dataframes."""
        column_names = {
            "#_of_professionals_over_last_year": "num_professionals_last_year",
            "#_of_professionals_in_this_year": "num_professionals_this_year",
            "%_of_total": "percent_of_total",
            "avg_salaryusd": "avg_salary_usd",
            "#_of_inflow": "number_inflow",
            "#_of_outflow": "number_outflow",
        }

        sheets = ("Location", "Talent move_by_locations", "Skills")
        sheets = {sheet: column_names for sheet in sheets}
        self._rename_df_column(sheets)
