import re
from typing import Dict

import pandas as pd

from se_data_pipeline.component.base import Normaliser, NormalizationMixin
from se_data_pipeline.component.constants import TOP_DATE, TOP_YEAR
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.models import manual_research_competitors as mrc
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import sales_research_competitors as src


class ManualResearchNormaliser(Normaliser, NormalizationMixin):
    """Normalises manual research data."""

    def __init__(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        llm_data_handler: LLMDataHandler,
    ):
        """
        Initializes ManualResearchNormaliser.

        Args:
            sheet_df_map (Dict[str, pd.DataFrame]): A dictionary mapping sheet names to dataframes.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for text processing.
        """
        self.sheet_df_map = sheet_df_map.copy()
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)

    def normalise(self) -> Dict[str, pd.DataFrame]:
        """
        Normalises the data in the sheet_df_map.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to normalised dataframes.
        """
        self.set_column_names()
        self.derive_table_status(
            management_sheets={
                mr.ManagementTeamHire.sheet_name: None,
                mr.ManagementTeamExit.sheet_name: None,
                mr.ManagementTeamPromo.sheet_name: None,
            }
        )
        self.replace_with_nan_values()
        self.apply_financial_to_int_conversion(
            fin_sheets={
                "General Overview",
                "Funding Rounds",
                "Financial Trend",
                "Categorized Revenue",
            }
        )
        self.fill_currency_code(
            financial_tables={
                "General Overview",
                "Funding Rounds",
                "Financial Trend",
                "Categorized Revenue",
            }
        )
        self.rename_df_columns()
        self.normalise_column_names()
        self.set_column_types()
        self.translate_text()
        self.normalise_date_cols()
        self.set_project_dates(sheets={"Internal Projects", "Outsourcing"})
        self.set_top_date()
        self.normalize_merging_logics()

        self.sheet_df_map[sr.DataAvailability.sheet_name] = self.status_table
        return self.sheet_df_map

    def set_table_status(self, category_name, status):
        """Sets the status of a specific table in the status table."""
        table_names = self.status_table[src.DataAvailability.table]
        if category_name == "Strategy, Risks and Initiatives":
            mask = table_names.apply(lambda x: "Risks and Challenges" in x or "Market Positioning" in x)
        else:
            mask = table_names.apply(lambda x: x == category_name)
        self.status_table.loc[mask, src.DataAvailability.status] = status
        return None

    def set_top_date(self):
        """Sets a default top date for specific columns if they are missing but other data is present."""
        sheets = [
            ("Funding Rounds", "year", TOP_YEAR),
            ("Partnerships", "date", TOP_DATE),
            ("Acquisitions", "date", TOP_DATE),
            ("Mergers", "date", TOP_DATE),
            ("Investments", "date", TOP_DATE),
            ("Additional Information", "date", TOP_DATE),
        ]
        self._set_top_date(sheets)

    def normalize_merging_logics(self):
        df = self.sheet_df_map.get("Merging Logics")
        df = df.dropna(how="all").reset_index(drop=True)

        mask = ~df.iloc[:, 0].str.contains(r"^\*|\b(Draup only|Manual only|Draup + Manual)\b", na=False)
        df = df[mask].reset_index(drop=True)

        df = df.dropna(subset=[df.columns[0]]).reset_index(drop=True)

        self.sheet_df_map["Merging Logics"] = df

    def normalise_date_cols(self):
        """Normalises date columns to datetime.date format."""
        format_mixed = {"format": "mixed"}
        date_sheets = {
            ("News", "date"): format_mixed,
            ("Partnerships", "date"): format_mixed,
            ("Acquisitions", "date"): format_mixed,
            ("Mergers", "date"): format_mixed,
            ("Investments", "date"): format_mixed,
            ("Additional Information", "date"): format_mixed,
            ("Management Team Hire", "hire_date"): format_mixed,
            ("Management Team Exit", "exit_date"): format_mixed,
            ("Management Team Promo", "promo_date"): format_mixed,
        }
        self._normalise_date_cols(date_sheets)

    def translate_text(self):
        """Translates and normalises text in specified columns using the LLM."""
        target_mapping = {
            ("Connections", "name"),
        }
        self._translate_text(target_mapping)

    def rename_df_columns(self):
        """Renames specific columns in specified dataframes."""
        column_names = {
            "1st level connections / Name": "first_level_connections",
            "# of Job Postings (Last 12 Months)": "num_job_postings",
            "1y growth": "one_year_growth",
        }
        sheets = ("Connections",)
        sheets = {sheet: column_names for sheet in sheets}
        self._rename_df_column(sheets)

    def set_column_types(self):
        """Sets column types for specified sheets and columns."""
        int_cols = (
            (mr.GeneralOverview.sheet_name, mr.GeneralOverview.revenue),
            (mr.GeneralOverview.sheet_name, mr.GeneralOverview.it_spend),
            (mr.CategorizedRevenue.sheet_name, mr.CategorizedRevenue.revenue),
        )
        for sheet, column in int_cols:
            self._convert_to_int64(sheet, column)
        return None


class ManualResearchCompetitorsNormaliser(Normaliser, NormalizationMixin):
    """Normalises manual research competitors data."""

    def __init__(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        llm_data_handler: LLMDataHandler,
    ):
        """
        Initializes ManualResearchCompetitorsNormaliser.

        Args:
            sheet_df_map (Dict[str, pd.DataFrame]): A dictionary mapping sheet names to dataframes.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for text processing.
        """
        self.sheet_df_map = sheet_df_map.copy()
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(src.DataAvailability.default_data)

    def normalise(self) -> Dict[str, pd.DataFrame]:
        """
        Normalises the data in the sheet_df_map.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to normalised dataframes.
        """
        self.set_column_names()
        # self.set_misc_headers()
        self.replace_with_nan_values()
        self.apply_financial_to_int_conversion(
            fin_sheets={
                "General Overview",
                "Fundings",
                "Financial Trend",
            }
        )
        self.pivot_dataframes()
        self.normalise_column_names()
        self.normalise_date_cols()
        self.set_top_date()
        self.sheet_df_map[src.DataAvailability.sheet_name] = self.status_table
        return self.sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the status of a specific table in the status table."""
        table_names = self.status_table[src.DataAvailability.table]
        if category_name == "Strategy, Risks and Initiatives":
            mask = table_names.apply(lambda x: "Risks and Challenges" in x or "Market Positioning" in x)
        else:
            mask = table_names.apply(lambda x: x == category_name)
        self.status_table.loc[mask, src.DataAvailability.status] = status
        return None

    def set_top_date(self):
        """Sets a default top date/year for specific columns if they are missing but other data is present."""
        sheets = [
            ("Fundings", "year", TOP_YEAR),
            ("Financial Trend", "year", TOP_YEAR),
            ("Categorized Revenue", "year", TOP_YEAR),
            ("Partnerships", "year", TOP_YEAR),
            ("Acquisitions", "year", TOP_YEAR),
            ("Investments", "year", TOP_YEAR),
            ("Mergers", "year", TOP_YEAR),
            ("Awards & Recognitions", "year", TOP_YEAR),
            ("Investments", "year", TOP_YEAR),
        ]
        self._set_top_date(sheets)

    def normalise_date_cols(self):
        """Normalises date columns to datetime.date format."""
        format_mixed = {"format": "mixed"}
        date_sheets = {
            ("Additional Information", "date"): format_mixed,
        }
        self._normalise_date_cols(date_sheets)

    def pivot_dataframes(self):
        pivot_tables = {
            mrc.Miscellaneous.sheet_name,
        }
        self._pivot_dataframes(pivot_tables)
