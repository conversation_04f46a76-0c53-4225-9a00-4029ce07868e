from typing import Dict

import pandas as pd

from se_data_pipeline.component.base import Normaliser, NormalizationMixin
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import sales_research as sr


class TalentInsightsNormaliser(Normaliser, NormalizationMixin):
    """Normalises talent insights data."""

    def __init__(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        llm_data_handler: LLMDataHandler,
    ):
        """
        Initialises TalentInsightsNormaliser.

        Args:
            sheet_df_map (Dict[str, pd.DataFrame]): A dictionary mapping sheet names to dataframes.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for text processing.
        """
        self.sheet_df_map = sheet_df_map.copy()
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)

    def normalise(self) -> Dict[str, pd.DataFrame]:
        """
        Normalises the data in the sheet_df_map.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to normalised dataframes.
        """
        self.set_column_names()
        self.normalise_column_names()
        self.rename_df_columns()
        self.translate_text()
        self.sheet_df_map[sr.DataAvailability.sheet_name] = self.status_table
        return self.sheet_df_map

    def translate_text(self):
        """Translates and normalises text in specified columns using the LLM."""
        target_mapping = {
            ("Locations", "location"),
        }
        self._translate_text(target_mapping)

    def rename_df_columns(self):
        """Renames specific columns in specified dataframes."""
        column_names = {
            "1y_growth": "one_year_growth",
            "1y_hires": "one_year_hires",
            "%_of_employees": "percent_of_employees",
            "your_%": "your_percent",
        }
        sheets = ("Locations", "Titles", "Skills")
        sheets = {sheet: column_names for sheet in sheets}
        self._rename_df_column(sheets)
