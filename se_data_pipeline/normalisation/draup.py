from typing import Dict

import pandas as pd

from se_data_pipeline.component.base import Normaliser, NormalizationMixin
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.component.utils import normalise_string
from se_data_pipeline.models import draup


class DraupNormaliser(Normaliser, NormalizationMixin):
    """Normalises Draup data."""

    def __init__(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        llm_data_handler: LLMDataHandler,
    ):
        """
        Initialises DraupNormaliser.

        Args:
            sheet_df_map (dict[str, pd.DataFrame]): A dictionary mapping sheet names to dataframes.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for text processing.
        """
        self.sheet_df_map = sheet_df_map.copy()
        self.llm_data_handler = llm_data_handler

    def normalise(self) -> Dict[str, pd.DataFrame]:
        """
        Normalises the data in the sheet_df_map.

        Returns:
            dict[str, pd.DataFrame]: A dictionary mapping sheet names to normalised dataframes.
        """
        self.replace_with_nan_values()
        self.drop_nan_rows()
        self.drop_rows_with_strings()
        self.rename_sheets()
        self.pivot_dataframes()
        self.truncate_tables()
        self.fill_currency_code(
            financial_tables={
                "Financials Trend",
                "Revenue by Business",
                "Revenue by Region",
            }
        )
        self.normalise_column_names()
        self.rename_df_columns()
        self.create_placeholder_columns()
        self.clean_descriptions()
        self.normalise_date_cols()
        self.apply_financial_to_int_conversion(
            fin_sheets={
                "Financials Trend",
                "Revenue by Business",
                "Revenue by Region",
            }
        )
        self.normalise_financial_year()
        self.convert_column_types()
        self.fill_na_defaults()
        self.translate_text()
        return self.sheet_df_map

    # def set_na_values(self):
    #     """Sets specific NA values for certain sheets."""
    #     sheets = {"Financials Trend": ["-"], "Engagement Details": ["n/a"], "Key Executives": ["n/a"]}
    #     for sheet_name, na_values in sheets.items():
    #         if sheet_name in self.sheet_df_map:
    #             self.sheet_df_map[sheet_name] = self.sheet_df_map[sheet_name].replace(na_values, pd.NA)
    #     return None

    def rename_sheets(self):
        """Renames sheets based on a predefined mapping."""
        rename_map = {
            "About": "About",
        }
        for old_name, new_name in rename_map.items():
            old_keys = [key for key in self.sheet_df_map.keys() if old_name.lower() in key.lower()]
            assert len(old_keys) < 2, f"Multiple matches found for {old_name}: {old_keys}"

            if old_keys:
                self.sheet_df_map[new_name] = self.sheet_df_map.pop(old_keys[0])
        return None

    def pivot_dataframes(self):
        """Pivots specified dataframes."""
        pivot_tables = {
            draup.About.sheet_name,
            draup.GeneralOverview.sheet_name,
            draup.FinancialsTrend.sheet_name,
        }

        self._pivot_dataframes(pivot_tables)

    def convert_column_types(self):
        """Converts specific columns to specified data types."""
        int_cols = (
            (draup.GeneralOverview.sheet_name, draup.GeneralOverview.total_workforce),
            (draup.FinancialsTrend.sheet_name, draup.FinancialsTrend.ebitda),
            (draup.FinancialsTrend.sheet_name, draup.FinancialsTrend.it_spend),
            (draup.FinancialsTrend.sheet_name, draup.FinancialsTrend.rd_spend),
            (draup.JobTitlesDemand.sheet_name, draup.JobTitlesDemand.num_job_postings),
            (draup.HiringMap.sheet_name, draup.HiringMap.num_job_postings),
            (draup.CoreSkillsDemand.sheet_name, draup.CoreSkillsDemand.num_job_postings),
            (draup.KeyExecutives.sheet_name, draup.KeyExecutives.id),
        )

        for sheet, column in int_cols:
            self._convert_to_int64(sheet, column)
        return None

    def normalise_financial_year(self):
        """Normalises financial year format."""
        fin_year = r"FY \d{4}"
        fin_trend = "Financials Trend"

        if fin_trend in self.sheet_df_map:
            df = self.sheet_df_map[fin_trend].copy()
            if not df.financial_metric.str.match(fin_year).all():
                raise ValueError(f"Financial metric does not match the pattern <{fin_year}>")
            df["financial_metric"] = df["financial_metric"].str.extract(r"(\d{4})").astype("Int64")
            self.sheet_df_map[fin_trend] = df
        return None

    def truncate_tables(self):
        """Truncates tables to remove header rows and adds extra columns."""
        table_headers = {
            draup.EngagementDetails.get_sheet_name(self.sheet_df_map.keys()): [
                "Customer",
                "New Addition(Yes/No)",
                # "EPAM Status",
                "Customer Verticals",
                "Workloads",
                "Customer Locations",
                "Customer HQ Location",
            ],
            "Account Priorities": [
                "Category",
                "Priority",
                "Description",
            ],
            "Business Intentions": [
                "Business Intention",
                "Digital Initiative",
                "Digital Initiative Description",
                "Development Stage",
                "Use Case",
                "Use Case Description",
                "Digitization Theme",
                "Use Case Cluster",
                "Technology Areas",
            ],
            "Signals": [
                "Company names",
                "Signal title",
                "Key highlights",
                "Signal category",
                "Tags",
                "URL",
                "Source",
                "Date",
            ],
            "Acquisitions Details": [
                "Announced Date",
                "Acquired",
                "Acquisition Primary Intent",
                "Location",
                "Acquistion Size",
                "universe_logo_url",
                "universe_account_id",
            ],
            "Investments Details": [
                "Date",
                "Organization Name",
                "Investment Type",
                "Amount",
                "Round",
                "universe_logo_url",
                "universe_account_id",
            ],
            "Solutions": [
                "Category",
                "Workload",
                "Subcategory",
                "Solution",
                "Developer Company",
                "Usage Intensity in Sub-Category Across the Org.",
                "Country Presence",
            ],
            draup.OutsourcingProviders.get_sheet_name(self.sheet_df_map.keys()): [
                # "Provider Name",
                "New Addition(Yes/No)",
                "Workloads",
                "Service Provider Partner Locations",
                "Relationship Highlights",
                "# of Active Workflows",
            ],
            "Job Postings": [
                "Job Title",
                "Location",
                "Workloads",
                "Business Functions",
                "Core Skills",
                "Benefits",
                "Description",
                "Posting Date",
            ],
            "Job Titles in Demand": [
                "Job Titles",
                "# of Job Postings for 12 months",
            ],
            "Hiring Map": [
                "Location",
                "# of Job Postings (Last 12 Months)",
            ],
            "Core Skills in Demand": [
                "Core Skills",
                "# of Job Postings for 12 months",
                "Trailing 4 quarters trend",
            ],
            "Competitor Information": [
                "Competitor Name",
                "Verticals",
            ],
            "Key Executives": [
                "ID",
                "Name",
                "Title",
                "Business Function",
                "Location",
                "Email Adress",
                "LinkedIn URL",
                "Draup URL",
            ],
            "Executive Movement Exits": [
                "Executive",
                "Position Before Exit",
                "Location Before Exit",
                "Exit Month",
                "Joining Position",
                "Joining Company",
                "Joining Location",
                "Joining Month",
            ],
            "Executive Movement Hires": [
                "Executive",
                "Position After Hire",
                "Location After Hire",
                "Hire Month",
                "Position Before Hire",
                "Location Before Hire",
                "Company Before Hire",
            ],
            "Executive Movement Promotions": [
                "Executive",
                "Position After Promotion",
                "Location After Promotion",
                "Promotion Month",
                "Position Before Promotion",
                "Location Before Promotion",
                "Joining/Last Promotion Month",
            ],
            "Revenue by Business": [
                "Business",
                "Revenue",
            ],
            "Revenue by Region": [
                "Region",
                "Revenue",
            ],
        }

        extra_cols = {
            "Revenue by Business": [
                "Financial Year",
            ],
            "Revenue by Region": [
                "Financial Year",
            ],
        }

        for sheet_name, headers in table_headers.items():
            if sheet_name not in self.sheet_df_map:
                continue
            df = self.sheet_df_map[sheet_name].copy()
            locate_header_rows = lambda row: all(
                any(normalise_string(header) in normalise_string(str(cell)) for cell in row) for header in headers
            )
            matches = df.apply(locate_header_rows, axis=1)
            matching_row_index = matches.idxmax() if matches.any() else None
            if matching_row_index is None:
                raise ValueError(f"Could not find the header row <{headers}> for {sheet_name}")
            df.columns = df.iloc[matching_row_index].to_list()
            df = df.loc[matching_row_index + 1 :].reset_index(drop=True)

            # adding extra columns by pivoting the defined named rows
            add_cols = extra_cols.get(sheet_name)
            if add_cols:
                extra_df = self.sheet_df_map[sheet_name].copy()
                extra_data = extra_df[
                    extra_df[extra_df.columns[0]].str.contains("|".join(add_cols), case=False, na=False)
                ]
                extra_data = extra_data.dropna(axis=1).pivot_table(columns=0, aggfunc="first").reset_index(drop=True)
                df = pd.concat([df, extra_data], axis=1)
                df[add_cols] = df[add_cols].convert_dtypes().ffill()

            self.sheet_df_map[sheet_name] = df
        return None

    def normalise_date_cols(self):
        """Normalises date columns to datetime.date format."""
        format_mixed = {"format": "mixed"}
        date_sheets = {
            ("Signals", "date"): format_mixed,
            ("Job Postings", "posting_date"): format_mixed,
            ("Executive Movement Exits", "exit_month"): format_mixed,
            ("Executive Movement Exits", "joining_month"): format_mixed,
            ("Executive Movement Hires", "hire_month"): format_mixed,
            ("Executive Movement Promotions", "promotion_month"): format_mixed,
        }
        self._normalise_date_cols(date_sheets)

    def rename_df_columns(self):
        """Renames specific columns in specified dataframes."""
        company_name = self.sheet_df_map["About"].account.iloc[0]
        company_loc = normalise_string(f"{company_name} Locations")
        sheets = {
            draup.OutsourcingProviders.get_sheet_name(self.sheet_df_map.keys()): {
                "service_provider_name": "provider_name",
                company_loc: "company_locations",
                "new_additionyes_no": "new_addition",
                "service_provider_partner_locations": "service_partner_locations",
                "#_of_active_workflows": "num_active_workflows",
            },
            draup.EngagementDetails.get_sheet_name(self.sheet_df_map.keys()): {
                company_loc: "company_locations",
                "new_additionyes_no": "new_addition",
                "#_of_active_workflows": "number_of_active_workflows",
            },
            "Job Titles in Demand": {
                "#_of_job_postings_for_12_months": "num_job_postings",
                "trailing_4_quarters_trend": "trailing_trend",
            },
            "Hiring Map": {
                "#_of_job_postings_last_12_months": "num_job_postings",
            },
            "Core Skills in Demand": {
                "#_of_job_postings_for_12_months": "num_job_postings",
                "trailing_4_quarters_trend": "trailing_trend",
            },
            "Solutions": {
                "usage_intensity_in_sub_category_across_the_org_": "usage_intensity",
            },
        }
        self._rename_df_column(sheets)

    def _replace_symbols(self, df, column_name, replace_map):
        """Replaces specific symbols in a given column."""
        df = df.copy()
        for target, replacement in replace_map.items():
            replace_func = lambda x: x.replace(target, replacement).strip() if not pd.isnull(x) else x
            df[column_name] = df[column_name].apply(replace_func)
        return df

    def clean_descriptions(self):
        """Cleans descriptions by replacing specific symbols."""
        target_mapping = {
            ("Signals", "key_highlights"): {"*": "\n"},
            ("Outsourcing Providers", "relationship_highlights"): {"* ": ""},
        }
        for (sheet_name, column_name), replace_map in target_mapping.items():
            if sheet_name in self.sheet_df_map:
                self.sheet_df_map[sheet_name] = self._replace_symbols(
                    self.sheet_df_map[sheet_name], column_name, replace_map
                )
        return None

    def fill_na_defaults(self):
        """Fills NA values with default values for specific columns."""
        target_mapping = {
            ("Executive Movement Exits", "joining_location"): "-",
            ("Executive Movement Exits", "joining_position"): "-",
            ("Executive Movement Exits", "position_before_exit"): "-",
            ("Executive Movement Exits", "joining_company"): "-",
            ("Executive Movement Hires", "position_after_hire"): "-",
            ("Executive Movement Hires", "location_after_hire"): "-",
            ("Executive Movement Hires", "position_before_hire"): "-",
            ("Executive Movement Hires", "company_before_hire"): "-",
            ("Executive Movement Promotions", "location_after_promotion"): "-",
            ("Executive Movement Promotions", "position_after_promotion"): "-",
            ("Executive Movement Promotions", "position_before_promotion"): "-",
            ("Job Postings", "core_skills"): "-",
            ("Job Postings", "location"): "-",
            ("Key Executives", "name"): "-",
            ("Key Executives", "title"): "-",
            ("Key Executives", "location"): "-",
            ("Key Executives", "linkedin_url"): "-",
            ("Financials Trend", "ebitda"): 0,
            ("Financials Trend", "it_spend"): 0,
            ("Financials Trend", "revenue"): 0,
        }
        for (sheet_name, column_name), fill_value in target_mapping.items():
            if sheet_name in self.sheet_df_map:
                self.sheet_df_map[sheet_name][column_name] = self.sheet_df_map[sheet_name][column_name].fillna(
                    fill_value
                )
        return None

    def translate_text(self):
        """Translates and normalises text in specified columns using the LLM."""
        target_mapping = {
            ("Key Executives", "name"),
            ("Executive Movement Exits", "executive"),
            ("Executive Movement Hires", "executive"),
            ("Executive Movement Promotions", "executive"),
        }
        self._translate_text(target_mapping)

    def create_placeholder_columns(self):
        """Creates placeholder columns for missing fields based on the data model."""
        sheet_models = {
            "Financials Trend": draup.FinancialsTrend,
        }
        missing_cols = {}
        for sheet_name, model in sheet_models.items():
            if sheet_name not in self.sheet_df_map:
                continue
            df_cols = set(self.sheet_df_map[sheet_name].columns)
            model_cols = model.__annotations__
            missing_cols[sheet_name] = {key: value for key, value in model_cols.items() if key not in df_cols}

        for sheet_name, model_cols in missing_cols.items():
            if not model_cols:
                continue
            for column_name in model_cols:
                # column should be defined as nullable field in the model
                self.sheet_df_map[sheet_name][column_name] = pd.NA
        return None
