from datetime import datetime as dt
from enum import Enum


class ProviderNames(Enum):
    """
    Enum representing provider names for data processing tasks.
    """

    DRAUP = "Draup"
    DRAUP_COMPETITORS = "Draup Competitors"
    SALES_RESEARCH = "Sales Research"
    SALES_RESEARCH_COMPETITORS = "Sales Research Competitors"
    TALENT_INSIGHTS = "Talent Insights"
    MANUAL_RESEARCH = "Manual Research"
    MANUAL_RESEARCH_COMPETITORS = "Manual Research Competitors"
    HIRE_EZ = "HireEZ"
    HIRE_EZ_COMPETITORS = "HireEZ Competitors"
    TALENT_DEMAND = "Talent Demand"
    TALENT_DEMAND_COMPETITORS = "Talent Demand Competitors"


# ProviderPredictor cache files
PREDICTOR_FILES = ("bnb_model.pkl", "label_encoder.pkl")
PACKAGE_CACHE = "se_data_pipeline.component.cached"

# composing Instruction tags
PRIORITY = "priority"
MERGE = "merge"

# Data normalisation mapping
CHAR_TRANSLATION = str.maketrans(
    {
        " ": "_",
        "(": "",
        ")": "",
        "&": "",
        "/": "_",
        "-": "_",
        ",": "_",
        ".": "_",
    }
)

# Constants for the date deriving
DATETIME = dt.now().strftime("%Y-%m-%dT%H:%M:%S")
DATE = dt.now().date()
YEAR = dt.now().year
TOP_DATE = dt.strptime("01/01/9999", "%m/%d/%Y").date()
TOP_YEAR = 9999

# Constants for the status of the data
RESEARCHED = "Researched"
NOT_RESEARCHED = "Not Researched"
NO_INFO = "No Information Available"
STATUS_SCORE = {
    None: 0,
    NOT_RESEARCHED: 1,
    RESEARCHED: 2,
    NO_INFO: 3,
}

# Values expected in the Draup reports
COMPANY_TYPES = {"PUBLIC", "PRIVATE"}
DRAUP_NOTES = (
    (
        "Note: This document is solely for the use of Draup active customers and Draup Personnel only. "
        "No part of it may be quoted, circulated, or reproduced for distribution outside the client "
        "organization without prior written approval from Draup"
    ),
    (
        "Note: The key executives displayed are personnel above manager level within the organization. "
        "All information is sourced from publicly available sources and complies with local privacy and data prote"
    ),
)

# OpenAI constants
OPENAI_CONTENT_FILTER_MSG = "Azure has not provided the response due to a content filter being triggered"

# Default value for the location ID in the QH codelist
DEFAULT_LOC_ID = "NOT_AVAILABLE"
DEFAULT_JOB_TITLE_ID = "OTHER"

# Regex patterns
CURRENCY_CODE = r"\(in (\w+)\)|\((\w+)\)"
FIN_AMOUNT = r"^-?[\d,]+\.?\d*\s+(?:Mn|Bn|M|B)$"

# Constants for the data processing
FIN_MULTIPLIER = {
    "M": 1_000_000,
    "B": 1_000_000_000,
    "Mn": 1_000_000,
    "Bn": 1_000_000_000,
}


class FinalReportType(Enum):
    ACCOUNT = "account"
    COMPETITORS = "competitors"


MERGING_LOGICS_SHEETS = {
    "Draup Report Link",
    "Financial Trend",
    "Categorized Revenue",
    "Featured Clients",
    "Tech Stack",
    "Outsourcing",
    "IT Job Postings",
    "Competitors",
    "Key People",
    "Management Team Hire",
    "Management Team Exit",
    "Management Team Promo",
}


class MergingLogicType(Enum):
    DRAUP_AND_MANUAL = "Draup + Manual"
    DRAUP_ONLY = "Draup only"
    MANUAL_ONLY = "Manual only"
