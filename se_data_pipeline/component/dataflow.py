import typing as t

import pandas as pd
from pydantic import Field
from quanthub.transformations.base import (
    DatasetType,
    Transformation,
    ParameterMetadata,
    TransformationInput,
    TransformationOutput,
)


class FinancialsMappingTransformationInput(TransformationInput):
    financials_raw_df: t.Annotated[
        DatasetType,
        Field(description="The raw financials dataframe"),
        ParameterMetadata(urn="SE_A:FINANCIALS_RAW(0+.0.0)"),
    ]
    financials_by_company_df: t.Annotated[
        DatasetType,
        Field(description="Financial metrics by company"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY(0+.0.0)"),
    ]
    financials_by_company_vertical_df: t.Annotated[
        DatasetType,
        Field(description="Financials by company and business vertical"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY_AND_BUSINESS_VERTICAL(0+.0.0)"),
    ]
    financials_by_company_and_location_df: t.Annotated[
        DatasetType,
        Field(description="Financials by company and location"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY_AND_LOCATION(0+.0.0)"),
    ]


class FinancialsMappingTransformationOutput(TransformationOutput):
    financials_by_company_df: t.Annotated[
        pd.DataFrame,
        Field(description="Financial metrics by company"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY(0+.0.0)"),
    ]
    financials_by_company_vertical_df: t.Annotated[
        pd.DataFrame,
        Field(description="Financials by company and business vertical"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY_AND_BUSINESS_VERTICAL(0+.0.0)"),
    ]
    financials_by_company_and_location_df: t.Annotated[
        pd.DataFrame,
        Field(description="Financials by company and location"),
        ParameterMetadata(urn="SE_A:FINANCIALS_BY_COMPANY_AND_LOCATION(0+.0.0)"),
    ]


class TopOutsourcingMetricsTransformationInput(TransformationInput):
    top_outsourcing_metrics_raw_df: t.Annotated[
        DatasetType,
        Field(description="The raw top outsourcing metrics dataframe"),
        ParameterMetadata(urn="SE_A:TOP_OUTSOURCING_METRICS_RAW(0+.0.0)"),
    ]
    top_outsourcing_metrics_providers_df: t.Annotated[
        DatasetType,
        Field(description="The raw top outsourcing metrics dataframe"),
        ParameterMetadata(urn="SE_A:TOP_OUTSOURCING_METRICS_PROVIDERS(0+.0.0)"),
    ]
    top_outsourcing_metrics_locations_df: t.Annotated[
        DatasetType,
        Field(description="The raw top outsourcing metrics dataframe"),
        ParameterMetadata(urn="SE_A:TOP_OUTSOURCING_METRICS_LOCATIONS(0+.0.0)"),
    ]


class TopOutsourcingMetricsTransformationOutput(TransformationOutput):
    top_outsourcing_metrics_providers_df: t.Annotated[
        pd.DataFrame,
        Field(description="The raw top outsourcing metrics dataframe"),
        ParameterMetadata(urn="SE_A:TOP_OUTSOURCING_METRICS_PROVIDERS(0+.0.0)"),
    ]
    top_outsourcing_metrics_locations_df: t.Annotated[
        pd.DataFrame,
        Field(description="The raw top outsourcing metrics dataframe"),
        ParameterMetadata(urn="SE_A:TOP_OUTSOURCING_METRICS_LOCATIONS(0+.0.0)"),
    ]


class FinancialsTransformation(
    Transformation[FinancialsMappingTransformationInput, FinancialsMappingTransformationOutput]
):

    @classmethod
    def get_input_model(cls) -> t.Type[TransformationInput]:
        return FinancialsMappingTransformationInput

    @classmethod
    def get_output_model(cls) -> t.Type[TransformationOutput]:
        return FinancialsMappingTransformationOutput

    def run(self, transformation_input: FinancialsMappingTransformationInput) -> FinancialsMappingTransformationOutput:
        financials_raw_df = transformation_input.financials_raw_df.copy()
        company_cols = transformation_input.financials_by_company_df.copy().columns
        company_vertical_cols = transformation_input.financials_by_company_vertical_df.copy().columns
        company_and_location_cols = transformation_input.financials_by_company_and_location_df.copy().columns

        financials_by_company_df = financials_raw_df.loc[
            financials_raw_df["FINANCIAL_METRICS"] == "YEARLY_TOTAL"
        ].reset_index(drop=True)
        financials_by_company_df = financials_by_company_df[company_cols]

        financials_by_company_vertical_df = financials_raw_df.loc[
            financials_raw_df["FINANCIAL_METRICS"] == "REVENUE_BY_BUSINESS"
        ].reset_index(drop=True)
        financials_by_company_vertical_df = financials_by_company_vertical_df[company_vertical_cols]

        financials_by_company_and_location_df = financials_raw_df.loc[
            financials_raw_df["FINANCIAL_METRICS"] == "REVENUE_BY_REGION"
        ].reset_index(drop=True)
        financials_by_company_and_location_df = financials_by_company_and_location_df[company_and_location_cols]

        return FinancialsMappingTransformationOutput(
            financials_by_company_df=financials_by_company_df,
            financials_by_company_vertical_df=financials_by_company_vertical_df,
            financials_by_company_and_location_df=financials_by_company_and_location_df,
        )


class TopOutsourcingMetricsTransformation(
    Transformation[TopOutsourcingMetricsTransformationInput, TopOutsourcingMetricsTransformationOutput]
):

    @classmethod
    def get_input_model(cls) -> t.Type[TransformationInput]:
        return TopOutsourcingMetricsTransformationInput

    @classmethod
    def get_output_model(cls) -> t.Type[TransformationOutput]:
        return TopOutsourcingMetricsTransformationOutput

    def run(
        self, transformation_input: TopOutsourcingMetricsTransformationInput
    ) -> TopOutsourcingMetricsTransformationOutput:
        metrics_raw_df = transformation_input.top_outsourcing_metrics_raw_df.copy()
        providers_cols = transformation_input.top_outsourcing_metrics_providers_df.copy().columns
        locations_cols = transformation_input.top_outsourcing_metrics_locations_df.copy().columns

        metrics_providers_df = metrics_raw_df.loc[metrics_raw_df["TOP_METRICS_BREAKDOWN"] == "PROVIDER"].reset_index(
            drop=True
        )
        metrics_providers_df = metrics_providers_df[providers_cols]

        metrics_locations_df = metrics_raw_df.loc[metrics_raw_df["TOP_METRICS_BREAKDOWN"] == "LOCATION"].reset_index(
            drop=True
        )
        metrics_locations_df = metrics_locations_df[locations_cols]

        return TopOutsourcingMetricsTransformationOutput(
            top_outsourcing_metrics_providers_df=metrics_providers_df,
            top_outsourcing_metrics_locations_df=metrics_locations_df,
        )
