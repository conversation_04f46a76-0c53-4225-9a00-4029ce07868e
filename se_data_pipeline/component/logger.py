import os
import sys

from loguru import logger
from se_data_pipeline.component.utils import get_env_variable

# Define logging directory
LOG_DIR = os.path.join(os.getcwd(), "logs")
DEBUG_LOG = os.path.join(LOG_DIR, "debug.log")
os.makedirs(LOG_DIR, exist_ok=True)

# Define formats
LOG_FORMAT = "{time:YYYY-MM-DD at HH:mm:ss} | {level} | {message}"
ERROR_LOG_FORMAT = "{time:YYYY-MM-DD at HH:mm:ss} | {level} | {message} | {exception}"

# Remove default logger
logger.remove()

# Add console handler
logger.add(
    sys.stdout,
    level="DEBUG",
    format="{time} {level} {message}",
    # enqueue - threa
    enqueue=True,
    colorize=True,
)

# Add rotating debug log handler
logger.add(
    DEBUG_LOG,
    level="DEBUG",
    rotation="1 MB",
    retention="7 days",
    compression="zip",
    format=LOG_FORMAT,
    enqueue=True,
    catch=False,
)


def init_local_debug():
    error_log_path = os.path.join(LOG_DIR, "error_{time}.log")
    logger.add(
        error_log_path,
        level="ERROR",
        rotation="1 MB",
        retention="7 days",
        compression="zip",
        format=ERROR_LOG_FORMAT,
        backtrace=True,
        # diagnose - includes variable values in stack traces
        diagnose=True,
        enqueue=True,
        catch=False,
    )


if get_env_variable("RUNTIME_ENVIRONMENT") == "local":
    init_local_debug()
