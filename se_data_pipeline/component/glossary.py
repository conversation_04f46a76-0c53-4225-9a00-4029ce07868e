import re
from typing import Dict, Optional, List

import numpy as np
import pandas as pd
from unidecode import unidecode

from se_data_pipeline.component.az import az_secrets
from se_data_pipeline.component.logger import logger
from se_data_pipeline.component.constants import ProviderNames
from se_data_pipeline.component.utils import init_data_bridge
from quanthub.structures.glossary import Glossary
from quanthub.structures.glossary_matcher import MatchedTerm


class CodeListManager:
    def __init__(self, provider_name: ProviderNames, sheet_df_map: Optional[Dict[str, pd.DataFrame]] = None):
        self._sheet_df_map = sheet_df_map
        self._config = ReportConfig()
        self.codelist_data_source = self._config.get_codelist_data_map(provider_name)
        self.workspace = az_secrets.get_secret("WORKSPACE")
        self.data_bridge = init_data_bridge()

        self._financial_metrics_names = self._read_glossary(self._config.FINANCIAL_METRICS)
        self._event_categories = self._read_glossary(self._config.EVENTS_HIERARCHY)
        self._event_subcategories = self._read_glossary(self._config.EVENTS_HIERARCHY)
        self._swot_categories = self._read_glossary(self._config.EVENTS_HIERARCHY)

    @property
    def sheet_df_map(self) -> Optional[Dict[str, pd.DataFrame]]:
        return self._sheet_df_map

    @sheet_df_map.setter
    def sheet_df_map(self, sheet_df_map: Dict[str, pd.DataFrame]) -> None:
        self._sheet_df_map = sheet_df_map

    @property
    def financial_metrics_names(self) -> List[str]:
        if isinstance(self._financial_metrics_names, Glossary):
            self._financial_metrics_names = self._financial_metrics_names.df.name.to_list()
        return self._financial_metrics_names

    @property
    def event_categories(self) -> List[str]:
        if isinstance(self._event_categories, Glossary):
            categories = self._event_categories.df["__parent_id"].isna()
            default = self._event_categories.df["name"] == "-"
            df = self._event_categories.df.loc[categories | default]
            self._event_categories = df.name.to_list()
        return self._event_categories

    @property
    def event_subcategories(self) -> List[str]:
        if isinstance(self._event_subcategories, Glossary):
            subcategory_ids = ("STRATEGY_RISKS_AND_INITIATIVES", "PARTNERSHIPS_AND_ACQUISITIONS")
            subcategories = self._event_subcategories.df["__parent_id"].isin(subcategory_ids)
            default = self._event_subcategories.df["name"] == "-"
            df = self._event_subcategories.df.loc[subcategories | default]
            self._event_subcategories = df.name.to_list()
        return self._event_subcategories

    @property
    def swot_categories(self) -> List[str]:
        if isinstance(self._swot_categories, Glossary):
            categories = self._swot_categories.df["__parent_id"] == "SWOT"
            default = self._swot_categories.df["name"] == "-"
            df = self._swot_categories.df.loc[categories | default]
            self._swot_categories = df.name.to_list()
        return self._swot_categories

    def _read_glossary(self, glossary_id: str) -> Glossary:
        glossary = self.data_bridge.read_glossary(self.workspace, glossary_id)
        if glossary is None:
            raise RuntimeError(f"Glossary <{glossary_id}> is not found.")
        return glossary

    @staticmethod
    def _handle_special_symbols(s):
        """
        Handles special symbols in strings.
        """
        sim_map = {"#": " sharp ", "+": " plus "}
        for old, new in sim_map.items():
            s = s.replace(old, new)
        return s

    @staticmethod
    @np.vectorize(otypes=[str])
    def _generate_ids(s):
        """Transforms strings to a specific format suitable for IDs.

        The transformation process normalizes strings by converting non-ASCII characters,
        handling special symbols, removing non-alphanumeric characters, converting to
        uppercase, replacing whitespace with underscores, and truncating if needed.

        :param s: Input string to transform
        :type s: str or any type convertible to str
        :return: Formatted ID string
        :rtype: str
        """
        if not s:
            return ""

        # Convert to string and normalize non-ASCII characters
        s = str(s)
        s = unidecode(s)
        s = CodeListManager._handle_special_symbols(s)
        # Remove non-alphanumeric characters, convert to uppercase, replace spaces with underscores
        s = re.sub(r"[\W_]+", " ", s).strip().upper().replace(" ", "_")

        # Truncate if too long
        if len(s) >= 127:
            s = s[:127]

        return s

    def _aggregate_values(
        self,
        data_layout: Dict[str, list],
        df_map: Dict[str, pd.DataFrame],
    ) -> list:
        """
        Collects data for a specific workspace.
        """
        data_frames = []
        for sheet_name, columns in data_layout.items():
            df = df_map[sheet_name]
            data_frames.append(df[columns].melt(value_name="values")["values"])
        return pd.concat(data_frames, ignore_index=True).tolist()

    def _collect_terms(self, df_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Collects and transforms data from DataFrames.
        """
        glossary_terms = {}
        for glossary_id, data_layout in self.codelist_data_source.items():
            values = self._aggregate_values(data_layout, df_map)
            ids = self._generate_ids(values)
            glossary_terms[glossary_id] = pd.DataFrame(
                {
                    "id": ids,
                    "name": values,
                }
            )
            glossary_terms[glossary_id] = glossary_terms[glossary_id].replace({"": None, "-": None}).dropna(how="all")
        return glossary_terms

    def _update_codelist_terms(
        self,
        terms: pd.DataFrame,
        glossary: Glossary,
        cl: str,
    ) -> None:
        """
        Adds the transformed data to QuantHub for a specific workspace.
        """
        if terms.empty:
            logger.info(f"No new items for codelist, skipping: <{cl}>")
            return None

        try:
            glossary.add_terms_from_pandas(terms)
            self.data_bridge.write_glossary(self.workspace, glossary, draft=False)
            logger.debug(f"Received new items for codelist: <{cl}> {terms.name.unique().tolist()}")
            logger.info(f"Successfully updated codelist: <{cl}>")
        except Exception:
            logger.info(f"Failed to update codelist: <{cl}>")
            raise
        return None

    def _update_source_data(self, rename_map: Dict[str, str], cl: str) -> None:
        """
        Updates the source data with the values from QuantHub to avoid ID collisions and duplicate names.
        """
        synth_financials = {
            self._config.FINANCIAL_METRICS_BUSINESS_CATEGORY,
            self._config.FINANCIAL_METRICS_REGION_CATEGORY,
        }
        for sheet_name, columns in self.codelist_data_source[cl].items():
            if cl in synth_financials:
                columns = ["Category of Financial Metrics"]
            if cl == self._config.COMPANY_NAMES and sheet_name == "Outsourcing Top Metrics":
                columns = ["Metrics"]
            self._sheet_df_map[sheet_name][columns] = self._sheet_df_map[sheet_name][columns].replace(rename_map)
        logger.debug(f"Replaced source data with QuantHub values for codelist: <{cl}>: {rename_map}.")
        return None

    def _validate_terms(
        self,
        terms: pd.DataFrame,
        glossary: Glossary,
        cl: str,
    ) -> pd.DataFrame:
        """
        Filters the transformed data against the existing data in QuantHub. Removes terms that already exist in QuantHub.
        Updates the transformed DataFrame with the Quanthub values if ids are the same.
        """
        # updating the transformed data with QuantHub values if ids are the same, using id as a matching criteria
        # TODO: fix QH codelist ids, they are mixed case
        id_exists = terms.loc[terms.id.isin(glossary.df.id.str.upper())].copy()
        qh_names = glossary.df.set_index("id")["name"]
        id_exists["qh_name"] = id_exists["id"].map(qh_names)
        id_exists["name_mismatch"] = id_exists["name"] != id_exists["qh_name"]
        # putting QH names into source data and terms for updating QH
        if id_exists.name_mismatch.any():
            rename_map = id_exists.loc[id_exists.name_mismatch == True].set_index("name")["qh_name"].to_dict()
            self._update_source_data(rename_map, cl)
            terms["name"] = terms["name"].replace(rename_map)

        # taking new term ids for updating QuantHub, using name as a matching criteria
        validated_terms = terms.loc[~terms.name.isin(glossary.df.name)].copy()
        return validated_terms

    def _split_mixed_data_columns(self) -> Dict[str, pd.DataFrame]:
        """
        Creates temporary synthetic columns for splitting the mixed data. The values are pushed into different QuantHub codelists.
        """
        df_map = self._sheet_df_map.copy()
        if "Financials" in df_map:
            df = df_map["Financials"].copy()
            df["Business Category"] = df.loc[
                (df["Financial Metrics"] == "Revenue by Business"), "Category of Financial Metrics"
            ]
            df["Special Location"] = df.loc[
                (df["Financial Metrics"] == "Revenue by Region") & (df["Location ID"] == "NOT_AVAILABLE"),
                "Category of Financial Metrics",
            ]
            df["Business Category"] = df["Business Category"].fillna("-")
            df["Special Location"] = df["Special Location"].fillna("-")
            df_map["Financials"] = df
        if "Outsourcing Top Metrics" in df_map:
            df = df_map["Outsourcing Top Metrics"].copy()
            df["Company Name"] = df.loc[(df["Top Metrics Breakdown"] == "Provider"), "Metrics"]
            df["Company Name"] = df["Company Name"].fillna("-")
            df_map["Outsourcing Top Metrics"] = df
        return df_map

    def update(self) -> None:
        """
        Updates the code lists by collecting data and adding it to QuantHub.
        """
        split_df_map = self._split_mixed_data_columns()
        glossary_terms = self._collect_terms(split_df_map)

        for glossary_id, terms in glossary_terms.items():
            if glossary_id in self._config.static_glossaries:
                logger.debug(f"Skipping update for static codelist: <{glossary_id}>")
                continue
            glossary = self.data_bridge.read_glossary(self.workspace, glossary_id)
            if glossary is None:
                raise RuntimeError(f"Glossary <{glossary_id}> is not found.")
            validated_terms = self._validate_terms(terms, glossary, glossary_id)
            self._update_codelist_terms(validated_terms, glossary, glossary_id)
        return None


class LocationGlossary:
    def __init__(self) -> None:
        """Initialize LocationGlossary by retrieving the locations glossary and setting up mappings."""
        self._lg = self._retrieve_locations_glossary()
        self._df = self._lg.df
        self._city_to_parent = self._get_id_mapping("city", "__parent_id")
        self._region_to_country = self._get_id_mapping("region", "__parent_id")
        self._region_to_name = self._get_id_mapping("region", "name")
        self._country_to_name = self._get_id_mapping("country", "name")
        self._concat_location_names()

    @property
    def glossary(self) -> Glossary:
        """Return the glossary object."""
        return self._lg

    @property
    def df(self) -> pd.DataFrame:
        """Return the dataframe of the glossary."""
        return self._df

    @property
    def version(self) -> str:
        """Return the version of the glossary."""
        return self._lg.urn().version

    def _get_id_mapping(self, type, key) -> Dict[str, str]:
        """Create a mapping from IDs to the specified key for a given type."""
        return self._lg.df[self._lg.df["type"] == type].set_index("id")[key].to_dict()

    def _retrieve_locations_glossary(self) -> Glossary:
        """Retrieve the locations glossary using environment variables."""
        workspace = az_secrets.get_secret("WORKSPACE")
        locations_glossary_id = az_secrets.get_secret("LOCATIONS-GLOSSARY-ID")
        data_bridge = init_data_bridge()
        locations_glossary = data_bridge.read_glossary(workspace, locations_glossary_id)
        if locations_glossary is None:
            raise RuntimeError("Locations glossary is not found.")
        return locations_glossary

    def _format_city(self, city):
        """Format a city with its region and country names."""
        city_parent_id = city["__parent_id"]
        if city_parent_id in self._region_to_country:
            country_id = self._region_to_country[city_parent_id]
            region_name = self._region_to_name[city_parent_id]
            country_name = self._country_to_name[country_id]
        elif city_parent_id in self._country_to_name:
            region_name = ""
            country_name = self._country_to_name[city_parent_id]
        values = [x for x in (city["name"], region_name, country_name) if x]
        return ", ".join(values)

    def _format_region(self, region):
        """Format a region with its country name."""
        country_id = region["__parent_id"]
        country_name = self._country_to_name[country_id]
        return f"{region['name']}, {country_name}"

    def _format_country(self, country):
        """Return the country name."""
        return f"{country['name']}"

    def _format_special(self, special):
        """Return the special term name."""
        return f"{special['name']}"

    def _concat_location_names(self):
        """Concatenate formatted location names into a new column in the dataframe."""
        format_funcs = {
            "city": self._format_city,
            "region": self._format_region,
            "country": self._format_country,
            "special": self._format_special,
        }

        df = self._df.copy()
        df["concat_name"] = df.apply(lambda row: format_funcs[row["type"]](row), axis=1)
        self._df = df


class TitleGlossary:
    def __init__(self) -> None:
        """Initialize TitleGlossary by retrieving the titles glossary."""
        self._tg = self._retrieve_titles_glossary()
        self._df = self._tg.df

    @property
    def glossary(self) -> Glossary:
        """Return the glossary object."""
        return self._tg

    def _retrieve_titles_glossary(self) -> Glossary:
        """Retrieve the titles glossary using environment variables."""
        workspace = az_secrets.get_secret("WORKSPACE")
        titles_glossary_id = az_secrets.get_secret("JOB-TITLE-LEVEL")
        data_bridge = init_data_bridge()
        titles_glossary = data_bridge.read_glossary(workspace, titles_glossary_id)
        if titles_glossary is None:
            raise RuntimeError("Job Title Levels glossary is not found.")
        return titles_glossary


class GlossaryIDMatcher:
    def _find_matching_terms(
        self,
        item: str,
        terms_df: pd.DataFrame,
        cache: dict,
        match_column: str,
        **kwargs,
    ) -> List[MatchedTerm]:
        item_id = cache.get(item)
        match_dict = {"id": None}
        if item_id:
            match_records = terms_df.loc[terms_df["id"] == item_id].to_dict(orient="records")
            if len(match_records) != 1:
                raise ValueError(
                    f"{self.id_type} ID <{item_id}> for item <{item}> was set by OpenAI and not found in glossary."
                )
            match_dict = match_records[0]

        return [
            MatchedTerm(
                terms=[match_dict],
                score=0,
                match_column=match_column,
                match_value=item,
            )
        ]

    def match_terms(
        self,
        glossary,
        items: List[str],
        cache: dict,
        match_column: str,
        match_attributes: List[str] = None,
        **kwargs,
    ) -> Dict[str, set]:
        if match_attributes is None:
            match_attributes = ["id", "name"]
        if "id" not in match_attributes:
            match_attributes.append("id")

        result = {}
        df = glossary.df[match_attributes]

        for item in items:
            matches = self._find_matching_terms(item, df, cache, match_column, **kwargs)
            matched_ids = {term["id"] for mat in matches for term in mat.terms if term.get("id") is not None}
            if matched_ids:
                result[item] = matched_ids

        return result

    def _get_ids(
        self, items: List[str], glossary, cache_dict: dict, cache_method, match_column: str, **kwargs
    ) -> Dict[str, str]:
        """
        Generalized function to retrieve IDs by matching items (locations/titles) against the glossary and cache.

        Args:
            items (list): List of items (e.g., location names or job titles).
            glossary: The glossary object containing terms.
            cache_dict: Cache dictionary for the items.
            cache_method: Method to update/populate the cache.
            match_column (str): The glossary match column.

        Returns:
            dict: A mapping from each item to its matched ID.
        """
        cache_method(items, **kwargs)
        result = self.match_terms(glossary, items, cache_dict, match_column, **kwargs)
        return {item: ids.pop() for item, ids in result.items()}


class ReportConfig:
    def __init__(self):
        self.PROBLEM_STATEMENT_DRIVER = az_secrets.get_secret("PROBLEM-STATEMENT-DRIVER")
        self.EVENTS_HIERARCHY = az_secrets.get_secret("EVENTS-HIERARCHY")
        self.BUSINESS_UNIT = az_secrets.get_secret("BUSINESS-UNIT")
        self.PRODUCTS_AND_SERVICES = az_secrets.get_secret("PRODUCTS-AND-SERVICES")
        self.CUSTOMER_CODE = az_secrets.get_secret("CUSTOMER-CODE")
        self.SKILLS = az_secrets.get_secret("SKILLS")
        self.STATUS_OF_THE_RESEARCH = az_secrets.get_secret("STATUS-OF-THE-RESEARCH")
        self.PROJECT_TYPE = az_secrets.get_secret("PROJECT-TYPE")
        self.COMPANY_NAMES = az_secrets.get_secret("COMPANY-NAMES")
        self.PROJECT_CATEGORY = az_secrets.get_secret("PROJECT-CATEGORY")
        self.FINANCIAL_METRICS = az_secrets.get_secret("FINANCIAL-METRICS")
        self.TECH_STACK_CATEGORY = az_secrets.get_secret("TECH-STACK-CATEGORY")
        self.JOB_TITLES = az_secrets.get_secret("JOB-TITLES")
        self.TECHNOLOGY_TOOLS = az_secrets.get_secret("TECHNOLOGY-TOOLS")
        self.FINANCIAL_METRICS_BUSINESS_CATEGORY = az_secrets.get_secret("FINANCIAL-METRICS-BUSINESS-CATEGORY")
        self.FINANCIAL_METRICS_REGION_CATEGORY = az_secrets.get_secret("FINANCIAL-METRICS-REGION-CATEGORY")
        # TODO CATEGORY_OF_FINANCIAL_METRICS_SR legacy competitors category, replace with new category
        self.CATEGORY_OF_FINANCIAL_METRICS_SR = ""
        self.INDUSTRIES = az_secrets.get_secret("INDUSTRIES")
        self.TARGET_ACCOUNT_LIST = az_secrets.get_secret("TARGET-ACCOUNT-LIST")
        self.TRANSACTION_NAME = az_secrets.get_secret("TRANSACTION-NAME")
        self.GBU = az_secrets.get_secret("GBU")
        self.INDUSTRY_FUNCTION = az_secrets.get_secret("INDUSTRY-FUNCTION")
        self.COMPETITOR_ACCOUNT = az_secrets.get_secret("COMPETITOR-ACCOUNT")
        self.OFFERINGS_BY_CATEGORY_HIERARCHY = az_secrets.get_secret("OFFERINGS-BY-CATEGORY-HIERARCHY")
        # TODO SOLUTIONS_AND_CAPABILITIES_CSR legacy competitors category, replace with new category
        self.SOLUTIONS_AND_CAPABILITIES_CSR = az_secrets.get_secret("SOLUTIONS-AND-CAPABILITIES-CSR")
        self.CURRENT_ENGAGEMENT_STATUS = az_secrets.get_secret("CURRENT-ENGAGEMENT-STATUS")
        self.TABS = az_secrets.get_secret("TABS")

    def get_codelist_data_map(self, provider_name: ProviderNames):
        data_maps = {
            ProviderNames.SALES_RESEARCH: self.account,
            ProviderNames.SALES_RESEARCH_COMPETITORS: self.competitors,
        }

        report_layout = data_maps.get(provider_name)
        if report_layout is None:
            raise ValueError(
                f"Unable to update Quanthub codelists. Provider name <{provider_name}> not found in data maps."
            )
        return report_layout

    @property
    def static_glossaries(self):
        """Static codelists are not updated by the automated pipeline.

        :return: a set of codelist identifiers
        :rtype: set[str]
        """
        return {
            self.CURRENT_ENGAGEMENT_STATUS,
            self.FINANCIAL_METRICS,
            self.EVENTS_HIERARCHY,
            self.PROJECT_CATEGORY,
        }

    @property
    def account(self):
        # codelist: {tab: [columns]}
        return {
            self.PROBLEM_STATEMENT_DRIVER: {
                "Priorities": ["Priority / Driver"],
            },
            self.EVENTS_HIERARCHY: {
                "Events": ["Subcategory"],
            },
            self.BUSINESS_UNIT: {
                "Business Structure": ["Business Unit"],
            },
            self.PRODUCTS_AND_SERVICES: {
                "Business Structure": ["Products and Services"],
            },
            self.CUSTOMER_CODE: {
                "Current Engagements": ["Customer code"],
            },
            self.SKILLS: {
                "IT Workforce Skills": ["Skills"],
                "IT Job Postings": ["Skills"],
            },
            self.STATUS_OF_THE_RESEARCH: {
                "Data Availability": ["Status of the research"],
            },
            self.PROJECT_TYPE: {
                "Projects": ["Project Type"],
            },
            self.COMPANY_NAMES: {
                "General Overview": ["Account"],
                "Funding Rounds": ["Investors"],
                "Current Engagements": ["Company name"],
                "Events": ["Company name"],
                "Projects": ["Company Name"],
                "Competitors": ["Competitor Name"],
                "Management Team Changes": ["Previous Company", "Joining Company"],
                "Featured Clients": ["Company Name"],
                "Company Entities": ["Subsidiary Name"],
                # synthetic column
                "Outsourcing Top Metrics": ["Company Name"],
            },
            self.PROJECT_CATEGORY: {
                "Projects": ["Project Category"],
            },
            self.FINANCIAL_METRICS: {
                "Financials": ["Financial Metrics"],
            },
            self.TECH_STACK_CATEGORY: {
                "Tech Stack": ["Category"],
            },
            self.TECHNOLOGY_TOOLS: {
                "Tech Stack": ["Technology Tools"],
                "Projects": ["Technology Tools"],
            },
            self.CURRENT_ENGAGEMENT_STATUS: {
                "Current Engagements": ["Status"],
            },
            # synthetic column
            self.FINANCIAL_METRICS_BUSINESS_CATEGORY: {
                "Financials": ["Business Category"],
            },
            # synthetic column
            self.FINANCIAL_METRICS_REGION_CATEGORY: {
                "Financials": ["Special Location"],
            },
            self.INDUSTRIES: {
                "General Overview": ["Industry"],
                "Featured Clients": ["Industry"],
            },
            self.TARGET_ACCOUNT_LIST: {
                "General Overview": ["Account"],
            },
            self.TRANSACTION_NAME: {"Funding Rounds": ["Transaction Name"]},
            self.GBU: {"Current Engagements": ["GBU"]},
            self.JOB_TITLES: {
                "IT Job Postings": ["Job Title"],
                "Job Titles in Demand": ["Job Titles"],
                "Key People": ["Job Title"],
                "Management Team Changes": ["Job Title", "Previous Position"],
            },
        }

    @property
    def competitors(self):
        return {
            self.COMPANY_NAMES: {
                "General Overview": ["Account"],
                "Funding Rounds": ["Investors"],
                "Relationships": ["Company name"],
                "Clients": ["Company Name"],
            },
            self.SKILLS: {
                "Job Postings": ["Skills"],
                "Workforce by Skills": ["Skills"],
            },
            self.COMPETITOR_ACCOUNT: {"General Overview": ["Account"]},
            self.INDUSTRIES: {"General Overview": ["Industry"]},
            self.TRANSACTION_NAME: {"Funding Rounds": ["Transaction Name"]},
            self.FINANCIAL_METRICS: {"Financials": ["Financial Metrics"]},
            self.CATEGORY_OF_FINANCIAL_METRICS_SR: {
                "Financials": ["Category of Financial Metrics"],
            },
            self.EVENTS_HIERARCHY: {"Strategy": ["Category"], "Relationships": ["Category"]},
            self.OFFERINGS_BY_CATEGORY_HIERARCHY: {
                "Offerings": ["Offerings (static)", "Offerings by Category"],
            },
            self.PRODUCTS_AND_SERVICES: {
                "Offerings": ["Products and Services"],
            },
            self.SOLUTIONS_AND_CAPABILITIES_CSR: {
                "Offerings": ["Solutions & Capabilities"],
            },
            self.CURRENT_ENGAGEMENT_STATUS: {
                "Clients": ["EPAM Status"],
            },
            self.TABS: {
                "Data Availability": ["Tabs Information"],
            },
            self.STATUS_OF_THE_RESEARCH: {
                "Data Availability": ["Status of the research"],
            },
        }
