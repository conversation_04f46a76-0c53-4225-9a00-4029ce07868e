from typing import List, Tuple, Set
import pandas as pd

from sklearn.naive_bayes import BernoulliNB
from sklearn.preprocessing import LabelEncoder

from se_data_pipeline.component.logger import logger
from se_data_pipeline.component.utils import load_from_pkl


class ProviderPredictor:
    """
    Class for predicting providers.

    This class handles loading models, preprocessing data and making predictions
    """

    def __init__(
        self,
        model_file: str,
        label_encoder_file: str,
        confidence_threshold: float = 0.97,
    ):
        """
        Initialize the ProviderPredictor with necessary file paths and parameters.

        Args:
            model_file: Path to the serialized model
            label_encoder_file: Path to the serialized label encoder
            confidence_threshold: Minimum confidence required for predictions (default: 0.70)
        """
        self.confidence_threshold = confidence_threshold

        # Load required components
        self.model: BernoulliNB = load_from_pkl(model_file)
        self.label_encoder: LabelEncoder = load_from_pkl(label_encoder_file)
        self.feature_columns: Set[str] = self._extract_features()

    def _extract_features(self) -> Set[str]:
        """
        Extract unique feature names from the training data.

        Returns:
            Set of unique feature names
        """
        return set(self.model.feature_names_in_)

    def get_excel_sheet_names(self, excel_file_path: str) -> List[str]:
        """
        Retrieve standardized sheet names from an Excel file.

        Args:
            excel_file_path: Path to the Excel file

        Returns:
            List of standardized sheet names
        """
        excel_file = pd.ExcelFile(excel_file_path)
        sheet_names = excel_file.sheet_names

        # Standardize "About" sheet names
        return ["About" if name.startswith("About") else name for name in sheet_names]

    def predict_provider(self, file_path: str) -> str:
        """
        Main prediction method that processes an Excel file and predicts the provider.

        Args:
            file_path: Path to the Excel file for prediction

        Returns:
            Predicted provider name

        Raises:
            ValueError: If prediction confidence is below threshold
        """
        # Load and preprocess data
        features_matrix, sheet_names = self._preprocess_file(file_path)

        # Make prediction
        predicted_provider = self._predict(features_matrix)

        return predicted_provider

    def _preprocess_file(self, file_path: str) -> Tuple[pd.DataFrame, List[str]]:
        """
        Load and preprocess an Excel file for prediction.

        Args:
            file_path: Path to the Excel file

        Returns:
            Tuple containing feature matrix and list of sheet names
        """
        # Get sheet names
        sheet_names = self.get_excel_sheet_names(file_path)

        # Create features matrix with all features set to 0
        features_matrix = pd.DataFrame(0, index=[0], columns=sorted(self.feature_columns))

        # Set features to 1 if they exist in the sheet names
        for sheet_name in sheet_names:
            if sheet_name in features_matrix.columns:
                features_matrix.at[0, sheet_name] = 1

        return features_matrix, sheet_names

    def _predict(self, features_matrix: pd.DataFrame) -> str:
        """
        Make a prediction based on preprocessed features.

        Args:
            features_matrix: DataFrame containing preprocessed features

        Returns:
            Predicted provider name

        Raises:
            ValueError: If prediction confidence is below threshold
        """
        logger.info("Making prediction")

        # Get prediction probabilities
        probabilities = self.model.predict_proba(features_matrix)
        max_probability = probabilities.max()
        predicted_label_index = probabilities.argmax()

        # Convert numerical prediction to label
        predicted_label = self.label_encoder.inverse_transform([predicted_label_index])[0]

        logger.info(f"Predicted provider: {predicted_label} with confidence {max_probability:.4f}")

        # Check confidence threshold
        if max_probability > self.confidence_threshold:
            return predicted_label
        else:
            error_message = (
                f"Predicted label: '{predicted_label}'. "
                f"Prediction confidence ({max_probability*100:.4f}%.) "
                f"is below the required threshold"
                "Prediction rejected."
            )
            logger.error(error_message)
            raise ValueError(error_message)
