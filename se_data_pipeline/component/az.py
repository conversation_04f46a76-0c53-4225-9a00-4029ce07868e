import base64
import io
import json
import os
from datetime import datetime
from functools import wraps
import traceback
from typing import Union

import pandas as pd
from azure.identity import ClientSecretCredential, DefaultAzureCredential, ManagedIdentityCredential
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobServiceClient
from azure.storage.queue import QueueServiceClient

from se_data_pipeline.component.logger import logger, DEBUG_LOG
from se_data_pipeline.component.notification import MSTeamsNotification
from se_data_pipeline.component.utils import get_env_variable


class AZSecrets:
    def __init__(self):
        self.environment = get_env_variable("RUNTIME_ENVIRONMENT")
        self._credential = None
        self._key_vault = None

    @property
    def credential(self) -> Union[ClientSecretCredential, ManagedIdentityCredential, DefaultAzureCredential]:
        if self._credential is None:
            self._credential = self._get_credential()
        return self._credential

    @property
    def key_vault(self) -> SecretClient:
        if self._key_vault is None:
            self._key_vault = self._init_key_vault_client()
        return self._key_vault

    def _get_credential(self) -> Union[ClientSecretCredential, ManagedIdentityCredential, DefaultAzureCredential]:
        if self.environment in ("qa", "uat", "prod", "autotest"):
            logger.info(f"{self.environment} - Using ManagedIdentityCredential")
            return ManagedIdentityCredential(client_id=get_env_variable("IDENTITY_CLIENT_ID"))

        elif self.environment == "local":
            try:
                client_id = get_env_variable("SE_AZURE_CLIENT_ID")
                client_secret = get_env_variable("SE_AZURE_CLIENT_SECRET")
                tenant_id = get_env_variable("SE_AZURE_TENANT_ID")

                logger.info("Using ClientSecretCredential with registered application")
                return ClientSecretCredential(tenant_id=tenant_id, client_id=client_id, client_secret=client_secret)

            except ValueError:
                logger.warning(
                    "Failed to use ClientSecretCredential, falling back to DefaultAzureCredential for dev environment"
                )
                return DefaultAzureCredential(exclude_managed_identity_credential=False)
        raise ValueError(f"Unknown environment: {self.environment}")

    def _init_key_vault_client(self) -> SecretClient:
        key_vault_name = get_env_variable("KEY_VAULT_NAME")
        kv_uri = f"https://{key_vault_name}.vault.azure.net"
        return SecretClient(vault_url=kv_uri, credential=self.credential)

    def get_secret(self, secret_name: str) -> str:
        try:
            secret = self.key_vault.get_secret(secret_name)
            if secret is None:
                raise ValueError(f"Secret {secret_name} not found in Key Vault")
            return secret.value
        except Exception:
            logger.error(f"Failed to get secret {secret_name} from Key Vault.")
            raise


az_secrets = AZSecrets()


class AZStorage:
    def __init__(self):
        self._secrets = az_secrets
        self.input_container = self._init_storage_container_client("INPUT-CONTAINER-NAME")
        self.output_container = self._init_storage_container_client("OUTPUT-CONTAINER-NAME")
        self.logs_container = self._init_storage_container_client("LOGS-CONTAINER-NAME")
        logger.info("Azure Storage clients initialized.")

        self.queue = self._init_queue_client("QUEUE-NAME")
        logger.info("Azure Queue client initialized.")

        self.message = self.queue.receive_message(visibility_timeout=1500)
        self.msg_content = self._read_message()
        logger.info(f"Queue message taken into processing: {self.msg_content}")

    def _init_storage_container_client(self, secret_name: str):
        storage_account_name = self._secrets.get_secret("STORAGE-ACCOUNT-NAME")
        blob_service_client = BlobServiceClient(
            account_url=f"https://{storage_account_name}.blob.core.windows.net",
            credential=self._secrets.credential,
        )
        container_name = self._secrets.get_secret(secret_name)
        return blob_service_client.get_container_client(container_name)

    def _init_queue_client(self, secret_name: str):
        storage_account_name = self._secrets.get_secret("STORAGE-ACCOUNT-NAME")
        queue_service_client = QueueServiceClient(
            account_url=f"https://{storage_account_name}.queue.core.windows.net",
            credential=self._secrets.credential,
        )
        queue_name = self._secrets.get_secret(secret_name)
        return queue_service_client.get_queue_client(queue_name)

    def _read_message(self):
        if self.message:
            encoded_content = self.message.content
            decoded_bytes = base64.b64decode(encoded_content)
            json_str = decoded_bytes.decode("utf-8")
            json_message = json.loads(json_str)
            return json_message
        else:
            raise RuntimeError("No messages found in the queue. Abort processing.")

    @property
    def folder_name(self) -> str:
        # taking company name from 6th element of the path
        subject_path = self.msg_content["subject"]
        folder_name = subject_path.split("/")[6]
        return folder_name

    def download_blobs_as_excel(self, input_path=None):
        if not input_path:
            raise ValueError("Blob download input path not provided.")

        save_dir = os.path.join(input_path, self.folder_name)
        os.makedirs(save_dir, exist_ok=True)

        blobs_list = list(self.input_container.list_blobs(name_starts_with=self.folder_name))
        blobs_list = [blob for blob in blobs_list if blob.name.endswith(".xlsx")]
        logger.debug(f"Found {len(blobs_list)} blobs in {self.input_container.container_name}/{self.folder_name}")

        if not blobs_list:
            logger.info("No blobs found in the specified folder.")
            return

        for blob in blobs_list:
            blob_client = self.input_container.get_blob_client(blob.name)
            blob_data = blob_client.download_blob().readall()

            try:
                with io.BytesIO(blob_data) as excel_io:
                    # Read all sheets from the Excel file
                    xls = pd.ExcelFile(excel_io, engine="openpyxl")
                    sheet_names = xls.sheet_names

                    # Create a new Excel file with all sheets
                    blob_filename = os.path.basename(blob.name)
                    save_path = os.path.join(save_dir, blob_filename)

                    with pd.ExcelWriter(save_path, engine="openpyxl") as writer:
                        for sheet_name in sheet_names:
                            df = pd.read_excel(excel_io, sheet_name=sheet_name, engine="openpyxl")
                            df.to_excel(writer, sheet_name=sheet_name, index=False)

                    logger.debug(f"Saved: {save_path} with sheets: {sheet_names}")

            except Exception:
                logger.error(f"Error while processing the <{blob.name}> blob.")
                raise

    def upload_blobs(self, output_path=None):
        if not output_path:
            raise ValueError("Blob upload output path not provided.")

        file_dir = os.path.join(output_path)

        if not os.path.exists(file_dir):
            logger.info(f"Directory not found: {file_dir}")
            return

        excel_files = [f for f in os.listdir(file_dir) if f.endswith(".xlsx")]

        if not excel_files:
            logger.info(f"No Excel files found in {file_dir}. Nothing to upload.")
            return

        for file_name in excel_files:
            local_file_path = os.path.join(file_dir, file_name)

            try:
                output_blob_client = self.output_container.get_blob_client(file_name)

                with open(local_file_path, "rb") as data:
                    output_blob_client.upload_blob(
                        data,
                        overwrite=True,
                        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    )

                logger.info(f"Uploaded: {local_file_path} to {self.output_container.container_name}/{file_name}")

            except Exception:
                logger.info(f"Failed to upload the <{file_name}> file.")
                raise

        logger.info("Upload process completed.")

    def save_logs(self):
        if os.path.isfile(DEBUG_LOG):
            try:
                debug_log_blob_name = f"{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}_{self.folder_name}_debug.log"
                debug_blob_client = self.logs_container.get_blob_client(debug_log_blob_name)

                with open(DEBUG_LOG, "rb") as log_data:
                    debug_blob_client.upload_blob(
                        log_data,
                        overwrite=True,
                        content_type="text/plain",
                    )

                logger.info(f"Uploaded log to Azure: {debug_log_blob_name}")

            except Exception:
                logger.info("Failed to upload the debug log.")
                raise

    def clean_up_input_storage(self):
        """
        Removes all blobs in the specified input folder and the folder itself.

        This method deletes all input blobs within the designated folder from the input
        container, followed by removing the folder blob. It tracks and logs the number
        of blobs deleted during the operation.
        """
        blobs_removed = 0
        for blob in self.input_container.list_blobs(name_starts_with=f"{self.folder_name}/"):
            blob_client = self.input_container.get_blob_client(blob.name)
            blob_client.delete_blob()
            blobs_removed += 1

        folder_blob_client = self.input_container.get_blob_client(self.folder_name)
        folder_blob_client.delete_blob()

        logger.info(f"Deleted {blobs_removed} blobs from {self.input_container.container_name}/{self.folder_name}")

    def delete_message(self):
        if self.message:
            try:
                logger.info("Deleting the message from the queue.")
                self.queue.delete_message(self.message.id, self.message.pop_receipt)
                logger.info("Message deleted from the queue successfully.")
            except Exception:
                logger.info(f"Failed to delete the message from the queue: <{self.message.id}>.")
                raise


def with_azure_storage(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f"{'STARTING AZURE PROCESSING':=^50}")
        try:
            storage = AZStorage()
            teams_notification = MSTeamsNotification(webhook_url=get_env_variable("WEBHOOK_CONNECTOR"))
            storage.download_blobs_as_excel(kwargs["input_path"])
            func(**kwargs)
            storage.upload_blobs(kwargs["output_path"])
            storage.clean_up_input_storage()
        except Exception as e:
            logger.error(f"{'AZURE PROCESSING FAILED':=^50}")
            logger.error(f"AZURE PROCESSING FAILED: {e}\n{traceback.format_exc()}")
            storage.save_logs()
            raise
        finally:
            logger.info(f"{'AZURE PROCESSING FINISHED':=^50}")
            storage.delete_message()
            teams_notification.send_message(storage.folder_name)

    return wrapper
