import json
import os
import time

import requests

from typing import List

from se_data_pipeline.component.logger import logger, DEBUG_LOG, LOG_DIR
from se_data_pipeline.component.utils import get_env_variable


class AdaptiveCard:
    def __init__(self, company_name: str):
        self.company_name = company_name
        self.card_body = []
        self._payload = {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                        "version": "1.5",
                        "body": self.card_body,
                    },
                }
            ],
        }

    @property
    def payload(self) -> dict:
        return self._payload

    @staticmethod
    def create_header_layout(icon: str, title: str, color: str, style: str) -> dict:
        return {
            "type": "Container",
            "bleed": True,
            "style": style,
            "items": [
                {
                    "type": "ColumnSet",
                    "columns": [
                        {
                            "type": "Column",
                            "width": "auto",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": icon,
                                    "size": "extraLarge",
                                }
                            ],
                        },
                        {
                            "type": "Column",
                            "width": "stretch",
                            "items": [
                                {
                                    "type": "TextBlock",
                                    "text": title,
                                    "weight": "bolder",
                                    "size": "extraLarge",
                                    "color": color,
                                }
                            ],
                        },
                    ],
                }
            ],
        }

    @staticmethod
    def create_text_block(text: str, **kwargs) -> dict:
        return {
            "type": "TextBlock",
            "text": text,
            "wrap": True,
            "size": kwargs.get("size", "medium"),
            "spacing": kwargs.get("spacing", "medium"),
            "color": kwargs.get("color", None),
            "weight": kwargs.get("weight", None),
            "isSubtle": kwargs.get("is_subtle", False),
        }

    def create_company_info(self):
        return self.create_text_block(f"Processing company: **{self.company_name}**", size="large")

    def create_success_layout(self) -> List[dict]:
        header = self.create_header_layout(icon="✅", title="Processing Successful", color="good", style="good")
        company_info = self.create_company_info()
        message = self.create_text_block(
            "**_Your request has been processed successfully. "
            "If you need any further assistance, please contact the SE team._**",
            color="good",
        )
        return [header, company_info, message]

    def create_failure_layout(self, suggestions: str) -> List[dict]:
        header = self.create_header_layout(
            icon="⚠️",
            title="Processing Alert",
            color="attention",
            style="attention",
        )
        company_info = self.create_company_info()
        suggestion_header = self.create_text_block(
            "**Suggestions to self-fix (AI generated):**",
            size="large",
            weight="bolder",
        )
        error_summary = self.create_text_block(
            "**Summary of the Error:**",
            size="medium",
            weight="bolder",
        )
        suggestions_block = self.create_text_block(suggestions, spacing="small")
        signoff = self.create_text_block(
            "**_If you are not satisfied with the above suggestions, " "please contact the SE team._**",
            color="attention",
        )

        return [header, company_info, suggestion_header, error_summary, suggestions_block, signoff]

    def set_success_state(self):
        layout = self.create_success_layout()
        self.card_body.extend(layout)

    def set_failure_state(self, suggestions: str):
        layout = self.create_failure_layout(suggestions)
        self.card_body.extend(layout)


class MSTeamsNotification:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
        self._llm_handler = None

    @property
    def llm_handler(self):
        if self._llm_handler is None:
            try:
                from se_data_pipeline.component.llm import LLMDataHandler

                self._llm_handler = LLMDataHandler()
            except Exception:
                self._llm_handler = None
                raise
        return self._llm_handler

    def read_error_log(self):
        time.sleep(3)
        if not os.path.isdir(LOG_DIR):
            raise ValueError("Invalid log folder path.")

        if not os.path.isfile(DEBUG_LOG):
            logger.warning("debug.log file does not exist.")
            return ""

        try:
            with open(DEBUG_LOG, "r", encoding="utf-8") as file:
                lines = file.readlines()

            # Find the first line that contains "ERROR"
            start_index = next((i for i, line in enumerate(lines) if "ERROR" in line), None)

            if start_index is not None:
                # Return everything from the first ERROR to the end of the log
                error_block = lines[start_index:]
                return "".join(error_block).strip()
            else:
                return ""

        except Exception:
            logger.error("Failed to read the debug log.")
            raise

    def get_suggestions(self, message: str) -> str:

        # Check if LLM is available
        if self.llm_handler.llm is None:
            return ""

        try:
            # Attempt to generate AI suggestions
            suggestions = self.llm_handler.llm.analyze_logs(message)
            return suggestions if suggestions else ""

        except Exception as e:
            # Log the error and return default suggestion
            logger.warning(f"Failed to generate suggestions using LLM: {e}")
            return ""

    def send_message(
        self,
        company_name: str,
        failure_suggestions: str = "No suggestions were generated. Please contact the developer team to resolve the issue.",
    ) -> bool:
        error_msg = self.read_error_log()
        card = AdaptiveCard(company_name)

        if not error_msg:
            card.set_success_state()
        else:
            suggestions = self.get_suggestions(error_msg) or failure_suggestions
            card.set_failure_state(suggestions)

        response = requests.post(
            self.webhook_url,
            data=json.dumps(card.payload),
            headers={"Content-Type": "application/json"},
            timeout=120,
        )

        if response.status_code == 200:
            logger.info("Message sent successfully!")
            return True

        logger.error(f"Failed to send message. Status code: {response.status_code}")
        logger.error(f"Response: {response.text}")
        return False
