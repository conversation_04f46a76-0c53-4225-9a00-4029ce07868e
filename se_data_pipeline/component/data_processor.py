import itertools
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd
from pandera.errors import SchemaError

from se_data_pipeline.component.constants import PREDICTOR_FILES
from se_data_pipeline.component.glossary import CodeListManager
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.component.logger import logger
from se_data_pipeline.component.predictor import ProviderPredictor
from se_data_pipeline.component.provider import DataProvider, ProviderNames
from se_data_pipeline.component.utils import get_cached_file_path
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.models import sales_research as sr


class DataFile:
    """
    Represents a data file and provides methods for reading, processing, and exporting data.
    """

    def __init__(self) -> None:
        """
        Initializes a DataFile object with placeholders for various attributes.
        """
        logger.debug("Initializing DataFile object")
        self.file_path: Optional[Path]
        self.file_name: str
        self.company_name: str
        self._provider: DataProvider
        self._original_df: Dict[str, pd.DataFrame]
        self._normalised_df: Dict[str, pd.DataFrame]
        self._transformed_df: Dict[str, pd.DataFrame]

    @property
    def target_account(self) -> str:
        """Retrieves the target account name based on the transformed data."""
        account_sheets = {
            ProviderNames.MANUAL_RESEARCH: self._transformed_df.get(mr.GeneralOverview.sheet_name),
            ProviderNames.SALES_RESEARCH: self._transformed_df.get(sr.GeneralOverview.sheet_name),
        }
        account_columns = {
            ProviderNames.MANUAL_RESEARCH: mr.GeneralOverview.account,
            ProviderNames.SALES_RESEARCH: sr.GeneralOverview.account,
        }
        account_sheet = account_sheets.get(self.provider.name)
        account_column = account_columns.get(self.provider.name)
        if account_sheet is None:
            return self.company_name
        return account_sheet[account_column].iloc[0]

    @classmethod
    def from_excel(cls, file_path: Path) -> "DataFile":
        """Creates a DataFile instance from an Excel file."""
        logger.info(f"Creating DataFile from Excel file: {file_path}")
        data_file = cls()
        data_file.file_path = file_path
        data_file.file_name = file_path.stem
        data_file.company_name = file_path.parent.name
        try:
            data_file._original_df = data_file.read_excel_as_df(file_path)
            logger.debug(f"Successfully read Excel file: {file_path}")
        except Exception:
            logger.error(f"Failed to read Excel file: <{file_path}>.")
            raise
        return data_file

    @classmethod
    def from_df_map(
        cls,
        sheet_df_map: Dict[str, pd.DataFrame],
        provider: DataProvider,
        file_name: str,
        company_name: str,
    ) -> "DataFile":
        """Creates a DataFile instance from a mapping of sheet names to DataFrames."""
        data_file = cls()
        data_file.file_name = file_name
        data_file.company_name = company_name
        data_file.transformed_df = sheet_df_map
        data_file.set_provider(provider)
        data_file._validate_df_model(data_file.transformed_df, data_file.provider)
        return data_file

    def read_excel_as_df(self, file_path) -> Dict[str, pd.DataFrame]:
        """Reads an Excel file into a dictionary of DataFrames."""
        logger.debug(f"Reading Excel file: {file_path}")
        try:
            sheet_df_map = pd.read_excel(
                file_path,
                engine="openpyxl",
                header=None,
                sheet_name=None,
                thousands=",",
                keep_default_na=False,
                na_values=[""],
            )
            if not isinstance(sheet_df_map, dict):
                raise ValueError(f"Error reading excel file: {file_path}")
            logger.debug(f"Successfully read {len(sheet_df_map)} sheets from {file_path}")
            return sheet_df_map
        except Exception:
            logger.error(f"Error reading the Excel file: {file_path}.")
            raise

    def export_to_excel(self, output_path: Path) -> None:
        """Exports the transformed data to an Excel file at the specified path."""
        name_prefix = self.provider.name.value
        name_prefix = "_".join(i.upper() for i in name_prefix.split(" "))
        logger.info(f"Exporting DataFile to Excel with prefix '{name_prefix}' at: {output_path}")
        try:
            # export_dir = output_path / self.company_name
            output_path.mkdir(parents=True, exist_ok=True)
            export_path = output_path / f"{name_prefix}_({self.target_account}).xlsx"
            with pd.ExcelWriter(export_path, engine="xlsxwriter") as writer:
                for sheet, df in self._transformed_df.items():
                    df.to_excel(writer, sheet_name=sheet, index=False)
            logger.debug(f"Export successful to {export_path}")
        except Exception:
            logger.error(f"Error exporting DataFile to Excel.")
            raise

    def set_provider(self, provider: DataProvider) -> None:
        """Sets the data provider for the DataFile instance."""
        if not isinstance(provider, DataProvider):
            raise ValueError(f"Provider must be an instance of DataProvider, got {type(provider)}")
        self.provider = provider

    @property
    def original_df(self) -> Dict[str, pd.DataFrame]:
        """Gets the original data as a dictionary of DataFrames."""
        return self._original_df

    @original_df.setter
    def original_df(self, value: Dict[str, pd.DataFrame]) -> None:
        """Sets the original data as a dictionary of DataFrames."""
        self._original_df = value

    @property
    def normalised_df(self) -> Dict[str, pd.DataFrame]:
        """Gets the normalised data as a dictionary of DataFrames."""
        return self._normalised_df

    @normalised_df.setter
    def normalised_df(self, value: Dict[str, pd.DataFrame]) -> None:
        """Sets the normalised data as a dictionary of DataFrames."""
        self._normalised_df = value

    @property
    def transformed_df(self) -> Dict[str, pd.DataFrame]:
        """Gets the transformed data as a dictionary of DataFrames."""
        return self._transformed_df

    @transformed_df.setter
    def transformed_df(self, value: Dict[str, pd.DataFrame]) -> None:
        """Sets the transformed data as a dictionary of DataFrames."""
        self._transformed_df = value

    @property
    def provider(self) -> DataProvider:
        """Gets the data provider associated with the DataFile."""
        return self._provider

    @provider.setter
    def provider(self, value: DataProvider) -> None:
        """Sets the data provider associated with the DataFile."""
        self._provider = value

    def normalise(self, llm_data_handler: LLMDataHandler):
        """Normalizes the original data using the provider's normaliser."""
        logger.debug(f"Normalizing the <{self.provider.name.value}> provider data")
        try:
            data_normaliser = self.provider.normaliser(self.original_df, llm_data_handler)
            self.normalised_df = data_normaliser.normalise()
            self._validate_df_model(self.normalised_df, self.provider)
            logger.debug(f"Finished normalizing the <{self.provider.name.value}> provider data")
        except Exception:
            logger.error(f"Failed to normalize the <{self.provider.name.value}> provider data.")
            raise

    def transform(self, output_provider: DataProvider, manual_merge_rules, llm_data_handler: LLMDataHandler):
        """Transforms the normalized data using the provider's transformer."""
        logger.debug(f"Transforming the <{self.provider.name.value}> provider data")
        try:
            data_transformer = self.provider.transformations[output_provider.name]
            data_transformer = data_transformer(self.normalised_df, manual_merge_rules, llm_data_handler)
            self.transformed_df = data_transformer.transform()
            self._validate_df_model(self.transformed_df, output_provider)
            logger.debug(f"Finished transforming the <{self.provider.name.value}> provider data")
        except Exception:
            logger.error(f"Failed to transform the <{self.provider.name.value}> provider data.")
            raise

    def _validate_df_model(self, sheet_df_map, provider) -> None:
        """Validates the data using the provider's model."""
        logger.debug(f"Validating the <{self.provider.name.value}> provider data")
        for model in provider.model:
            sheet_name = model.sheet_name
            if sheet_name not in sheet_df_map:
                logger.warning(
                    f"The <{sheet_name}> sheet not found in the <{self.provider.name.value}> file structure, skipping."
                )
                continue
            try:
                logger.debug(f"Validating the <{sheet_name}> sheet with the <{provider.name.value}.{model}> model.")
                sheet_df_map[sheet_name] = model.validate(sheet_df_map[sheet_name])
            except SchemaError:
                error_message = (
                    f"Failed the <{sheet_name}> sheet validation against the <{provider.name.value}.{model}> model."
                )
                logger.error(error_message)
                raise

        logger.debug(f"Validated <{self.provider.name.value}> provider data")


class DataProcessor:
    """
    A class to manage data processing tasks, including reading, normalizing, transforming,
    composing, and exporting data files.

    Attributes:
        output_provider (DataProvider): Provides transformation functions for output data.
        input_path (Path): Path to the directory containing input files.
        output_path (Path): Path to the directory where output files will be saved.
        files (Dict[Path, Dict[ProviderNames, DataFile]]): Dictionary to store data files by directory and provider.
        llm_data_handler (LLMDataHandler): Handles tasks related to language model processing.
    """

    def __init__(
        self,
        output_provider: DataProvider,
        input_path: str,
        output_path: str,
    ) -> None:
        """
        Initialize the DataProcessor with paths and required providers.

        Args:
            output_provider (DataProvider): Object responsible for transformations.
            input_path (str): Path to the input directory containing source files.
            output_path (str): Path to the output directory where processed files will be saved.
        """
        self.output_provider = output_provider
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.files: Dict[Path, Dict[ProviderNames, DataFile]]
        self.llm_data_handler = LLMDataHandler()

    def process_data(self):
        """
        Execute the entire data processing pipeline, including:
        - File batching
        - Normalization
        - Transformation
        - Output composition
        - Code list updates
        - Data export

        Logs progress and errors at each stage.
        """
        logger.info("Starting data processing")
        try:
            self.files = self.create_file_batches()
            logger.debug("File batches created successfully")
            self.normalise_data()
            logger.debug("Data normalization completed")
            self.transform_data()
            logger.debug("Data transformation completed")
            self.compose_output_data()
            logger.debug("Output data composition completed")
            self.update_code_lists()
            logger.debug("Code list updates completed")
            self.llm_data_handler.update_cache()
            logger.debug("Cache updates completed")
            self.export_data()
            logger.info("Data processing completed successfully")
        except Exception:
            logger.error(f"Failed in the data processing stage.")
            raise

    @property
    def data_files(self):
        """
        Generator for iterating over all data files across providers and directories.

        Yields:
            DataFile: An individual data file object.
        """
        provider_files = (provider_files for provider_files in self.files.values())
        return (data_file for provider_files in provider_files for data_file in provider_files.values())

    def create_file_batches(self) -> Dict[Path, Dict[str, DataFile]]:
        """
        Organize input files into batches categorized by directory and provider.

        Returns:
            Dict[Path, Dict[str, DataFile]]: A dictionary mapping directories to provider-specific DataFile objects.

        Raises:
            RuntimeError: If file reading or batch creation encounters an error.
        """
        logger.info("Creating file batches from input directory")
        file_extensions = {".xlsx"}
        source_dirs = [path for path in self.input_path.glob("*") if path.is_dir()]
        files = {}
        for directory in source_dirs:
            paths = (Path(directory).glob(f"*{ext}") for ext in file_extensions)
            paths = list(itertools.chain(*paths))
            files[directory] = self._read_company_files(paths)
        logger.debug(f"Created file batches for {len(files)} directories")
        return files

    def _read_company_files(self, file_paths: List[Path]) -> Dict[ProviderNames, DataFile]:
        """
        Read and parse input files, categorizing them by their provider.

        Args:
            file_paths (List[Path]): List of file paths to process.

        Returns:
            Dict[ProviderNames, DataFile]: A dictionary mapping provider names to DataFile objects.

        Raises:
            RuntimeError: If duplicate files are found for the same provider.
        """
        logger.info("Reading company files")
        provider_files = {}
        try:
            data_files = (DataFile.from_excel(file_path) for file_path in file_paths)
            for data_file, file_path in zip(data_files, file_paths):
                logger.debug(f"Processing file: {file_path}")
                self._identify_data_provider(file_path, data_file)
                provider_name = data_file.provider.name
                logger.debug(f"Identified provider: {provider_name} for file: {file_path}")
                if provider_name in provider_files:
                    error_message = f"Multiple model files found for [{provider_name}] in <{file_path.parent}>"
                    logger.error(error_message)
                    raise RuntimeError(error_message)
                provider_files[provider_name] = data_file

            logger.debug(f"Successfully read files for {len(provider_files)} providers")
            return provider_files

        except Exception:
            logger.error(f"Error reading company files.")
            raise

    def _identify_data_provider(self, data_path, data_file):
        """
        Predict the provider for a given data file and update the file metadata.

        Args:
            data_path (Path): Path to the data file.
            data_file (DataFile): The DataFile object to update.
        """
        model_file_path, label_encoder_path = [get_cached_file_path(file_name) for file_name in PREDICTOR_FILES]

        predictor = ProviderPredictor(
            model_file=str(model_file_path),
            label_encoder_file=str(label_encoder_path),
        )

        predicted = predictor.predict_provider(str(data_path))
        for provider in DataProvider.get_all_instances():
            if provider.name == ProviderNames[predicted]:
                data_file.set_provider(provider)

    def normalise_data(self) -> None:
        """
        Normalize all data files using the LLM data handler.
        """
        for data_file in self.data_files:
            data_file.normalise(self.llm_data_handler)

    def transform_data(self) -> None:
        """
        Transform all data files using the output provider and LLM data handler.
        """
        manual_datafile = [
            data_file._normalised_df
            for data_file in self.data_files
            if data_file.provider.name.name == ProviderNames.MANUAL_RESEARCH.name
        ]
        if manual_datafile:
            manual_merge_rules = manual_datafile[0].get("Merging Logics")
        else:
            manual_merge_rules = None

        for data_file in self.data_files:
            data_file.transform(self.output_provider, manual_merge_rules, self.llm_data_handler)

    def compose_output_data(self) -> None:
        """
        Compose transformed data into the final output format for each directory.

        Raises:
            RuntimeError: If an error occurs during output composition.
        """
        logger.info("Composing output data for all directories")
        output_target = self.output_provider.name
        logger.debug(f"Output target: {output_target}")
        composer = self.output_provider.transformations[output_target]
        logger.debug("Composer function retrieved from output provider transformations")
        try:
            for path, provider_data in self.files.items():
                logger.info(f"Processing directory: {path}")
                data_files = list(provider_data.values())
                logger.debug(f"Collected {len(data_files)} data files for directory: {path}")

                composer_data = composer(data_files)
                logger.debug(f"Composer transformation applied for directory: {path}")

                sr_data_file = DataFile.from_df_map(
                    composer_data.transform(),
                    self.output_provider,
                    composer_data.file_name,
                    composer_data.company_name,
                )
                logger.debug(f"DataFile created for output: {sr_data_file.file_name}")

                self.files[path][self.output_provider.name] = sr_data_file
                logger.info(f"Output data composed for directory: {path}")

        except Exception:
            logger.error("Error in the processed data composing stage.")
            raise

    def update_code_lists(self):
        """
        Update code lists using the transformed data and a CodeListsUpdater instance.
        This method updated the transformed data values to avoid ID collisions.
        """
        logger.info("Updating Quanthub Codelists")
        for provider_files in self.files.values():
            sr_data_file = provider_files[self.output_provider.name]
            qh_codelist = CodeListManager(self.output_provider.name, sheet_df_map=sr_data_file.transformed_df)
            qh_codelist.update()
        logger.info("Quanthub Codelists updated successfully")
        return None

    def export_data(self) -> None:
        """
        Export the processed data to the output directory in Excel format.
        """
        logger.info("Starting data export")
        try:
            for provider_files in self.files.values():
                sr_data_file = provider_files[self.output_provider.name]
                logger.debug(f"Exporting file: {sr_data_file.file_name} to {self.output_path}")
                sr_data_file.export_to_excel(self.output_path)
                logger.info(f"Successfully exported: {sr_data_file.file_name}")
        except Exception:
            logger.error("Error in the processed data export stage.")
            raise
