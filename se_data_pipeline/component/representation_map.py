from typing import Dict, List, Optional

import pandas as pd

from se_data_pipeline.component.logger import logger
from se_data_pipeline.component.az import az_secrets
from se_data_pipeline.component.utils import init_data_bridge

from quanthub.structures.representation_map import RepresentationMap


class RepresentationMapManager:
    def __init__(self, repr_map_key: str, default_id_value=None):
        self.repr_map_key = repr_map_key
        self.workspace = az_secrets.get_secret("WORKSPACE")
        self.repr_map_name = az_secrets.get_secret(repr_map_key)
        self.data_bridge = init_data_bridge()
        self.default_id_value = default_id_value

        self.repr_map: Optional[RepresentationMap] = self.data_bridge.read_representation_map(
            self.workspace, self.repr_map_name
        )

        self._cached_ids = self._get_approved_ids()

    @property
    def cached_ids(self):
        return self._cached_ids

    def _get_approved_ids(self) -> Dict[str, str]:
        """Return a {source_1: target_1} dict for APPROVED mappings."""
        approved = self.repr_map.df.loc[self.repr_map.df["target_2"] == "APPROVED", ["source_1", "target_1"]]
        return approved.set_index("source_1")["target_1"].to_dict()

    def get_id(self, key: str) -> Optional[str]:
        """Return the ID for a given key (location or title)."""
        return self._cached_ids.get(key)

    def select_new_only(self, df_new: pd.DataFrame) -> pd.DataFrame:
        """Return only those rows from df_new whose source_1 is not already in the map."""
        return df_new[~df_new["source_1"].isin(self.repr_map.df.source_1)]

    def save_ids(self, raw_keys: List[str], id_values: List[str]) -> None:
        """Save new mapping rows, only for unseen keys. Mark as PENDING by default."""
        # Keys: either location names or titles etc.
        df_to_extend = pd.DataFrame(
            {
                "source_1": raw_keys,
                "target_1": id_values,
                "target_2": "PENDING",
            }
        )
        df_to_extend = self.select_new_only(df_to_extend)
        if not df_to_extend.empty:
            df = df_to_extend.fillna(self.default_id_value)
            logger.info(f"New {self.repr_map_key} entries: {df['source_1'].tolist()}")
            new_dict = df.set_index("source_1")["target_1"].to_dict()
            self._cached_ids.update(new_dict)
            self.repr_map.extend_from_df(df)
        else:
            logger.info(f"No new entries for {self.repr_map_key}.")

    def set_id(self, key: str, value: str) -> None:
        """Set/replace the id for key and save."""
        self._cached_ids[key] = value
        self.save_ids([key], [value])

    def update_ids(self, key_value_dict: Dict[str, Optional[str]]) -> None:
        """Update multiple ids."""
        self._cached_ids.update(key_value_dict)
        self.save_ids(list(key_value_dict.keys()), list(key_value_dict.values()))

    def write_to_quanthub(self) -> None:
        """Persist the map to the backend (QuantHub)."""
        self.data_bridge.write_representation_map(
            workspace=self.workspace,
            repr_map=self.repr_map,
            draft=False,
        )
        logger.info(f"{self.repr_map_key} map updated successfully.")
