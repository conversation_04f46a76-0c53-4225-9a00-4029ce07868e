import os
import pickle
import re
from typing import Any, Optional

from quanthub.structures import QuantHubClientFactory
from quanthub.structures.databridge import QuantHubDataBridge
from importlib import resources

from se_data_pipeline.component.constants import CHAR_TRANSLATION, PACKAGE_CACHE


def normalise_strings(strings):
    """Normalize each string in a list."""
    return [normalise_string(x) for x in strings]


def normalise_string(string: str | None) -> str | None:
    """
    Normalize a string: lowercase, strip, replace characters, collapse underscores.
    Return None if input is None.
    """
    if string is None:
        # called from table aggregation, some location ids may be None if not in glossary
        return None
    text = string.lower().strip().translate(CHAR_TRANSLATION)
    text = re.sub(r"__+", "_", text)
    return text


def get_env_variable(var_name: str):
    """Get an environment variable or raise an error if not set."""
    var_value = os.getenv(var_name)
    if var_value is None:
        raise ValueError(f"{var_name} environment variable is not set.")
    return var_value


def init_data_bridge() -> QuantHubDataBridge:
    tenant_id = get_env_variable("LOCAL_TENANT_ID")
    client_id = get_env_variable("LOCAL_CLIENT_ID")
    client_secret = get_env_variable("LOCAL_CLIENT_SECRET")
    username = get_env_variable("PIPELINE_PROXY_USER_NAME")
    password = get_env_variable("PIPELINE_PROXY_USER_PASSWORD")
    scopes = get_env_variable("PIPELINE_PROXY_USER_SCOPE").split(",")
    host = get_env_variable("SdmxRegistryHost")

    client_factory = QuantHubClientFactory(
        tenant_id=tenant_id,
        client_id=client_id,
        client_secret=client_secret,
        username=username,
        password=password,
        scopes=scopes,
        host=host,
    )

    return client_factory.create_data_bridge()


def load_from_pkl(file_path: str) -> Optional[Any]:
    """Load data from a pickle file if it exists."""
    if os.path.exists(file_path):
        with open(file_path, "rb") as f:
            return pickle.load(f)
    return None


def save_to_pkl(data: Any, file_path: str) -> None:
    """Save data to a pickle file."""
    with open(file_path, "wb") as f:
        pickle.dump(data, f)


def get_cached_file_path(file_name: str) -> str:
    """
    Returns the full file path for a cached file stored in the "se_data_pipeline.component.cached" package.

    Args:
        file_name (str): The name of the file (e.g., "base_data.pkl").

    Returns:
        str: The string path to the file.
    """
    return str(resources.path(PACKAGE_CACHE, file_name))
