import json
import asyncio
from importlib import resources
from typing import Callable, Dict, List, Optional

import pandas as pd
from langchain.pydantic_v1 import SecretStr
from langchain_community.document_loaders.dataframe import DataFrameLoader
from langchain_community.embeddings.huggingface import HuggingFaceBgeEmbeddings
from langchain_community.vectorstores.qdrant import Qdrant
from langchain_core.documents.base import Document
from langchain_core.messages.base import BaseMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.prompts.chat import HumanMessagePromptTemplate, SystemMessagePromptTemplate
from langchain_openai import AzureChatOpenAI
from qdrant_client.http import models as rest

from se_data_pipeline.component.az import az_secrets
from se_data_pipeline.component.constants import DEFAULT_LOC_ID, OPENAI_CONTENT_FILTER_MSG, DEFAULT_JOB_TITLE_ID
from se_data_pipeline.component.glossary import LocationGlossary, TitleGlossary, GlossaryIDMatcher
from se_data_pipeline.component.logger import logger
from se_data_pipeline.component.representation_map import RepresentationMapManager
from se_data_pipeline.component.utils import load_from_pkl, save_to_pkl


class OpenAIPrompts:
    _SYSTEM_MATCH_LOCATION_ID = """
    You are an AI assistant that helps to map input text values resembling location names to a glossary of locations. 
    You will be given a location name and a set of documents to take decision. 
    When taking a decision, follow the guidelines below: 
    - Use the available documents for context to answer the question at the end. 
    - Given documents have metadata with location names and types. 
    - You can return metadata id or "NO MATCH" text only. 
    - Take metadata 'type' value into consideration. 
    - Document 'page_content' field contains the location name in format: 'city_name, region_name, country_name'. 
    - The input location name may consist of any of components: city name, region name and a country name. 
    - Estimate the input location name and its type. 
    - Compare the input location name to the given documents. 
    - For cities, take into account the region and country names when doing comparison to documents.
    - You may map 'greater area' locations to corresponding cities or regions. 
    - Do not approximate cities to regions or countries.
    - If there are documents that closely resemble the input location name, return the metadata id of the most probable match. 
    - If there is no document closely related to input location, output "NO_MATCH". 
    - Do not speculate, do not try to make up an answer. 
    - Return a single requested result only, without any additional data or comments.
    - Return result shouldn't be wrapped in quotes.
    """
    _TASK_MATCH_LOCATION_ID = """
    Return the metadata id of the match for the given location name. 
    If there is no document closely related to the input location, output "NO_MATCH". 

    Available documents: {context} 
    Input location: {location} 
    Output:
    """

    _SYSTEM_TRANSLATE_NAME = """
    You are an expert translator that helps to translate names of people, location names, job titles from different languages into English. 
    You will be given a text with with non-ASCII characters, it could be in a foreign language. 
    When doing translation, follow the guidelines below: 
    - Translate the input into English, evaluate whether the input is a person name, location name or a business term. Respect the acronym capitalization.
    - Output the translation in ASCII character set. Remove newlines and tabs from the output.
    - Do not make up answers.
    - Return a single requested result only, without any additional data or comments.
    """
    _TASK_TRANSLATE_NAME = """
    Return the translated and normalized name into English. 

    Input name: {name} 
    Output:
    """

    _SYSTEM_CATEGORIZE_TITLE = """
    You are a specialized assistant that categorizes job titles into hierarchical levels within organizational structures.

    Task:
    Given a list of job titles, categorize each one according to the hierarchy below and return list of category names (as strings), in the same order as the input list.

    Categorization Guidelines:

    1. Category Selection
    - For each title, select the single most appropriate category from the hierarchy.
    - If a title fits multiple categories, select the highest-ranking one.
    - If no category fits, use OTHER.
    - Do not invent or infer categories not on the list.

    2. Hierarchy (Highest to Lowest)
    - BOARD_MEMBER: Board members, chairpersons, trustees, governors.
    - C_LEVEL: Chief executive positions (CEO, CFO, CTO, etc.).
    - VP_SVP: Vice President and Senior Vice President roles, including Corporate Vice President (CVP).
    - DIRECTOR_SENIOR_DIRECTOR_HEAD_OF: Directors, Senior Directors, and Heads of departments or functions. "Director" and "Head" are equal.
    - MANAGER: Team managers and supervisors of specific functions or groups.
    - OTHER: Titles not fitting above categories.

    3. Special Classification Rules
    - For multiple titles separated by commas, classify based on the highest-ranking title.
    - Keywords like "Executive," "Chief," or "Managing" do not automatically mean C_LEVEL.
    - "Head" does not override higher-ranking designations in the same title.
    - Ignore "ex," "former," or "retired"—classify only the current/active role.

    4. Distinctions
    - Only classify based on actual scope, not title inflation.

    Output Requirements:
    - Return only a list of category names (as strings), in the same order as the input list.
    - Do not return explanations, extra text, or formatting outside of the list in the comment.

    Example:

    Input:
    [ "Vice President, CFO", "Executive Vice President Nike", " ", "Group Managing Director", "Engineering Lead", "Key Account & Access Manager Rare Diseasey", "Director - Head of Digital, Data & IT Development GBS", 
    "Chief Diversity Officer, Head People Development and Culture",
    "VP, IT & ex-CIO, ABC TV Network Group", "Retired Managing Director", "Chairperson Public Sector Solutions", "Chief Cloud Architect in Data Management & Analytics"]

    Output:
    ["C_LEVEL", "VP_SVP", "OTHER", "DIRECTOR_SENIOR_DIRECTOR_HEAD_OF", "OTHER", "MANAGER", "DIRECTOR_SENIOR_DIRECTOR_HEAD_OF", "C_LEVEL", "VP_SVP", "OTHER", "BOARD_MEMBER", "OTHER"]
    
    """

    _TASK_CATEGORIZE_TITLE = """
    Choose the most appropriate category for the given job titles.

    Input job title: {job_title}
    Categories: {categories} 
    Output:
    """

    _SYSTEM_ANALYZE_LOGS = """
    You are an AI assistant specializing in analyzing log files from a data processing pipeline.  
    Your task is to identify the stage where an error occurred and provide guidance accordingly.  

    Error Classification:  
    1. Identify the affected stage in the pipeline: Load, Validation, Normalization, Transformation, Composing, Exporting.  
    2. If the error occurs in the Validation stage:  
    - Explain the cause in simple, non-technical terms.  
    - Provide step-by-step instructions for a non-technical user (who primarily works with Excel files) to resolve the issue.  
    - Start with a brief, easy-to-understand summary of the issue.  
    - Keep explanations concise and actionable.  
    3. If the error occurs in any other stage:  
    - Advise the user to contact the developer team.
    - Provide a clear, concise explanation of the issue specifically for developers, including where the issue occurred and how it might be resolved.
    - Do not speculate or create solutions beyond log analysis.  

    General Guidelines:  
    - Keep responses brief and clear.  
    - The response should not exceed 100 words.  
    - Do not generate hypothetical answers—stick to facts from the log data.  
    """
    _TASK_ANALYZE_LOGS = """
    Task Description:
    Analyze the following code error logs and provide solution.

    Error Log:
    {logs}
    Output of analysis:

    """

    _SYSTEM_CAPITALIZE_STR = """
    You are a specialized assistant that formats incorrectly capitalized strings.

    Task:
    Create a properly capitalized string from the provided value.

    Guidelines:
    - Provided titles might have capitalization issues
    - Properly capitalize the provided value to create a readable value, do not change the value otherwise, respect the acronyms
    - Do not make up values

    Output Requirements:
    - Return only capitalized value
    - No quotes, explanations, or additional text
    """

    _TASK_CAPITALIZE_STR = """
    Analyze the provided string and provide a properly capitalized value.

    Input string: {string}
    Output:
    """

    @classmethod
    def translate_name(cls, name: str) -> str:
        """Generate a translation for the provided name."""
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(cls._SYSTEM_TRANSLATE_NAME),
                HumanMessagePromptTemplate.from_template(cls._TASK_TRANSLATE_NAME),
            ]
        )
        return prompt.format(name=name)

    @classmethod
    def match_location_id(cls, location: str, docs: List[Document]):
        """Match a location with its corresponding ID using provided documents."""
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(cls._SYSTEM_MATCH_LOCATION_ID),
                HumanMessagePromptTemplate.from_template(cls._TASK_MATCH_LOCATION_ID),
            ]
        )
        return prompt.format(context=docs, location=location)

    @classmethod
    def categorize_title(cls, title: str, categories: List[str]) -> str:
        """Categorize a job title into one of the provided categories."""
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(cls._SYSTEM_CATEGORIZE_TITLE),
                HumanMessagePromptTemplate.from_template(cls._TASK_CATEGORIZE_TITLE),
            ]
        )
        return prompt.format(job_title=title, categories=categories)

    @classmethod
    def analyze_logs(cls, logs: str) -> str:
        """Categorize a job title into one of the provided categories."""
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(cls._SYSTEM_ANALYZE_LOGS),
                HumanMessagePromptTemplate.from_template(cls._TASK_ANALYZE_LOGS),
            ]
        )
        return prompt.format(logs=logs)

    @classmethod
    def capitalize_string(cls, string: str) -> str:
        """Categorize a job title into one of the provided categories."""
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(cls._SYSTEM_CAPITALIZE_STR),
                HumanMessagePromptTemplate.from_template(cls._TASK_CAPITALIZE_STR),
            ]
        )
        return prompt.format(string=string)


class OpenAIChat:

    def __init__(self, model="gpt-4o-2024-11-20") -> None:
        """Initialize OpenAIChat with the specified model and API credentials."""
        api_key = SecretStr(az_secrets.get_secret("AZURE-OPENAI-API-KEY"))
        url = az_secrets.get_secret("AZURE-OPENAI-ENDPOINT")
        api_version = az_secrets.get_secret("OPENAI-API-VERSION")

        self.llm = AzureChatOpenAI(
            model=model,
            api_key=api_key,
            azure_endpoint=url,
            api_version=api_version,
        )

    def translate_name(self, name: str):
        """Translate the given name using the LLM."""
        prompt = OpenAIPrompts.translate_name(name)
        return self.llm.invoke(prompt).content

    def capitalize_string(self, string: str):
        """Select the most readable title from the list using the LLM."""
        prompt = OpenAIPrompts.capitalize_string(string)
        return self.llm.invoke(prompt).content

    def analyze_logs(self, log: str):
        """Analyze the given log using the LLM."""
        prompt = OpenAIPrompts.analyze_logs(log)
        return self.llm.invoke(prompt).content

    async def match_location_ids(
        self,
        locations: List[str],
        docs: List[List[Document]],
        timeout=200,
        delay=0,
    ) -> List[BaseMessage]:
        """Match locations with IDs asynchronously."""
        prompts = [OpenAIPrompts.match_location_id(location=loc, docs=docs) for loc, docs in zip(locations, docs)]
        responses = []

        for prompt in prompts:
            try:
                response = await asyncio.wait_for(self.llm.abatch([prompt], return_exceptions=True), timeout)
                responses.extend(response)
            except asyncio.TimeoutError:
                print(f"Timeout error for location: {prompt}")
                raise
            await asyncio.sleep(delay)

        return responses

    async def match_title_level_ids(
        self,
        titles: List[str],
        categories: List[str],
        timeout=200,
        delay=0,
        max_attempts=10,
    ) -> List[BaseMessage]:
        """Match job titles with categories asynchronously."""
        prompt = OpenAIPrompts.categorize_title(titles, categories)
        responses = []

        for attempt in range(max_attempts):
            try:
                response = await asyncio.wait_for(self.llm.abatch([prompt], return_exceptions=True), timeout)

                if isinstance(response[0], Exception):
                    logger.warning(f"[Attempt {attempt + 1}] LLM Error: {response[0]}")
                    await asyncio.sleep(delay)
                    continue

                raw_content = response[0].content

                parsed = json.loads(raw_content)

                if len(parsed) == len(titles):
                    responses.extend(response)
                    break
                else:
                    logger.warning(f"[Attempt {attempt + 1}] Length mismatch: {len(parsed)} vs {len(titles)}")
                    await asyncio.sleep(delay)
                    continue

            except (asyncio.TimeoutError, json.JSONDecodeError) as e:
                logger.warning(f"[Attempt {attempt + 1}] parse Error: {e}")
                await asyncio.sleep(delay)
                continue
        else:
            logger.error("All attempts to match titles with categories failed.")
            raise RuntimeError("Failed to match titles with categories after 15 attempts.")

        await asyncio.sleep(delay)
        return responses


class LocationIndex:
    def __init__(self, df: pd.DataFrame, glossary_version: str) -> None:
        """Initialize the LocationIndex with a DataFrame and glossary version."""
        self._index_file = "./index.pkl"
        package = "se_data_pipeline.component.cached"
        with resources.path(package, "bge-small-en") as model_path:
            self.embedding = HuggingFaceBgeEmbeddings(
                model_name=str(model_path),
                model_kwargs={"device": "cpu"},
                encode_kwargs={"normalize_embeddings": True},
            )
        self.index: Qdrant = self.load(df, glossary_version)

    def load(
        self,
        df: pd.DataFrame,
        glossary_version: str,
        page_content_column="concat_name",
        load_cached=True,
    ) -> Qdrant:
        """Load or build the index from the DataFrame, using cached data if available."""
        # load cached index if exists
        index = load_from_pkl(self._index_file) if load_cached else None
        if index:
            if index["version"] == glossary_version:
                logger.info(f"Successfully loaded cached location embedding version {index.get('version')}.")
                return index["data"]

        logger.warning("Failed to load cached location embedding. Building from glossary.")
        # build index from glossary df
        docs = DataFrameLoader(df, page_content_column=page_content_column).load()
        index = Qdrant.from_documents(
            documents=docs,
            embedding=self.embedding,
            location=":memory:",
            collection_name="glossary_locations",
        )

        # cache index
        save_to_pkl(
            {
                "version": glossary_version,
                "data": index,
            },
            self._index_file,
        )

        return index

    def find_similar_with_scores(self, item, **kwargs):
        """Find similar items with scores from the index."""
        return self.index.similarity_search_with_score(item, **kwargs)

    def find_similar(self, item, **kwargs):
        """Find similar items from the index and return simplified metadata."""
        results = self.index.similarity_search(item, **kwargs)
        return [{"id": doc.metadata["id"], "name": doc.metadata["name"]} for doc in results]


class LLMDataHandler:
    def __init__(self) -> None:
        """
        Initializes various components such as location and job title glossaries,
        indexing, LLM (OpenAIChat), and cache (LocationIDCache).
        """
        self._locations = None
        self._job_title_levels = None
        self._index = None
        self._llm = None
        self._ids_cache = None

    @property
    def llm(self):
        """
        Returns the LLM instance used for matching location and title IDs.
        """
        if self._llm is None:
            self._llm = OpenAIChat()
        return self._llm

    @property
    def location_glossary(self):
        """
        Returns the location glossary used for matching location terms.
        """
        if self._locations is None:
            self._locations = LocationGlossary()
        return self._locations.glossary

    @property
    def title_glossary(self):
        """
        Returns the title glossary used for matching job title terms.
        """
        if self._job_title_levels is None:
            self._job_title_levels = TitleGlossary()
        return self._job_title_levels.glossary

    @property
    def cache(self):
        """
        Returns the cache used for storing location IDs.
        """
        if self._ids_cache is None:
            self._ids_cache = EntityIDCache()
        return self._ids_cache

    @property
    def index(self):
        """
        Returns the index used for finding similar locations.
        """
        if self._index is None:
            if self._locations is None:
                self._locations = LocationGlossary()
            self._index = LocationIndex(self._locations.df, self._locations.version)
        return self._index

    def match_location_ids(self, locs, docs):
        """
        Uses an LLM to match location IDs based on location names and documents.

        Args:
            locs (list): List of location names.
            docs (list): List of documents related to the locations.

        Returns:
            list: LLM responses containing matched location IDs.
        """
        loop = asyncio.get_event_loop()
        responses = loop.run_until_complete(self.llm.match_location_ids(locs, docs))
        return responses

    def match_title_level_ids(self, titles):
        """
        Matches job title level IDs using the LLM and glossary of titles.

        Args:
            titles (list): List of job titles.

        Returns:
            list: LLM responses containing matched title level IDs.
        """
        loop = asyncio.get_event_loop()
        categories = self.title_glossary.df["id"].unique().tolist()
        responses = loop.run_until_complete(self.llm.match_title_level_ids(titles, categories))
        return responses

    def _batch_iter_locations(self, input_list, batch_size=10):
        """
        Iterates over locations in batches for efficient processing.

        Args:
            input_list (list): List of location and document pairs.
            batch_size (int): Size of each batch.

        Yields:
            tuple: Batches of location and document pairs.
        """
        input_size = len(input_list)
        for idx in range(0, input_size, batch_size):
            logger.info(f"Identifying Location ID: {idx + 1}/{input_size}")
            locs, docs = zip(*input_list[idx : idx + batch_size])
            yield locs, docs

    def _batch_iter_job_titles(self, input_list, batch_size=10):
        """
        Iterates over job titles in batches for efficient processing.

        Args:
            input_list (list): List of job titles.
            batch_size (int): Size of each batch.

        Yields:
            list: Batches of job titles.
        """
        input_size = len(input_list)
        for idx in range(0, input_size, batch_size):
            logger.info(f"Identifying Job Title ID: {idx + 1}/{input_size}")
            job_titles = input_list[idx : idx + batch_size]
            yield job_titles

    def _find_similar_parent_id(self, location_name: str, threshold=0.8):
        """
        Finds and returns a parent location ID based on similarity to the provided location name.

        Args:
            location_name (str): Name of the location to find a similar parent for.
            threshold (float): Similarity score threshold.

        Returns:
            str: Parent location ID if found, otherwise NOT_AVAILABLE.
        """
        parent_name = location_name.split(",")
        if len(parent_name) == 1:
            return DEFAULT_LOC_ID
        parent_name = ",".join(parent_name[1:])
        qdrant_filter = rest.Filter(
            must=[
                rest.FieldCondition(
                    key="metadata.type",
                    match=rest.MatchAny(any=["region", "country"]),
                ),
            ]
        )
        docs = self.index.find_similar_with_scores(parent_name, k=5, filter=qdrant_filter)
        result = [doc for doc, score in docs if score > threshold]
        id_code = result[0].metadata["id"] if result else None
        return id_code

    def _translate_location_responses(self, responses, locations, docs):
        """
        Translates responses from the LLM to location IDs or finds similar parent IDs if no match.

        Args:
            responses (list): LLM responses containing matched IDs or errors.
            locations (list): List of location names.
            docs (list): List of documents related to the locations.

        Returns:
            list: List of matched location IDs.
        """
        ids = []
        for idx, response in enumerate(responses):
            location = locations[idx]
            if isinstance(response, Exception):
                value_error = isinstance(response, ValueError)
                if value_error and str(response) == OPENAI_CONTENT_FILTER_MSG:
                    id_code = self._find_similar_parent_id(location)
                    ids.append(id_code)
                else:
                    raise response
            elif response.content == "NO_MATCH":
                id_code = self._find_similar_parent_id(location)
                ids.append(id_code)
            else:
                ids.append(response.content)

        return ids

    def _translate_title_responses(self, responses, titles):
        """
        Translates responses from the LLM to job title IDs.

        Args:
            responses (list): LLM responses containing matched IDs.
            titles (list): List of job titles.

        Returns:
            list: List of matched job title IDs.
        """
        ids = []
        for response in responses:
            if isinstance(response, Exception):
                raise response
            ids.extend(json.loads(response.content))
        return ids

    def _cache_locations(self, locations: List[str], **kwargs) -> None:
        """
        Caches location IDs by querying the index and matching terms with the LLM.

        Args:
            locations (list): List of location names.
            kwargs: Optional arguments such as a formatter function for location names.
        """
        formatter: Optional[Callable] = kwargs.get("formatter")
        location_docs = []
        for target_loc in locations:
            location_id = self.cache.get_location_id(target_loc)
            if not location_id:
                formatted_loc = formatter(target_loc) if formatter else target_loc
                docs = self.index.find_similar(formatted_loc, k=5)
                location_docs.append((target_loc, docs))

        for locs, docs in self._batch_iter_locations(location_docs, batch_size=15):
            responses = self.match_location_ids(locs, docs)
            ids = self._translate_location_responses(responses, locs, docs)
            matched_ids = {loc: id for loc, id in zip(locs, ids)}
            self.cache.update_location_ids(matched_ids)

    def _cache_job_titles(self, job_titles: List[str], **kwargs) -> None:
        title_ids = []
        formatter: Optional[Callable] = kwargs.get("formatter")

        for title in job_titles:
            title_id = self.cache.get_title_id(title)
            if not title_id:
                formatted_title = formatter(title) if formatter else title
                title_ids.append(formatted_title)

        for title_batch in self._batch_iter_job_titles(title_ids, batch_size=25):
            responses = self.match_title_level_ids(title_batch)
            ids = self._translate_title_responses(responses, title_batch)
            matched_ids = {title: id for title, id in zip(title_batch, ids)}
            self.cache.update_title_ids(matched_ids)

    def get_location_ids(self, locations: List[str], **kwargs) -> Dict[str, str]:
        return GlossaryIDMatcher()._get_ids(
            items=locations,
            glossary=self.location_glossary,
            cache_dict=self.cache._location_ids,
            cache_method=self._cache_locations,
            match_column="concat_name",
            **kwargs,
        )

    def get_title_ids(self, titles: List[str], **kwargs) -> Dict[str, str]:
        return GlossaryIDMatcher()._get_ids(
            items=titles,
            glossary=self.title_glossary,
            cache_dict=self.cache._title_ids,
            cache_method=self._cache_job_titles,
            match_column="id",
            **kwargs,
        )

    def replace_location_to_ids(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        target_column: str,
        formatter_func: Optional[Callable] = None,
    ) -> Dict[str, pd.DataFrame]:
        """
        Replaces location names in a DataFrame with their corresponding location IDs.

        Args:
            sheet_df_map (dict): A dictionary mapping sheet names to DataFrames.
            target_column (str): The column containing location names.
            formatter_func (Optional[Callable]): A function to format location names before matching.

        Returns:
            dict: A dictionary with updated DataFrames containing location IDs.
        """
        location_names = set()
        for df in sheet_df_map.values():
            locations = set(df[target_column].dropna().unique())
            location_names.update(locations)

        location_names = list(location_names)
        location_to_id = self.get_location_ids(location_names, formatter=formatter_func)

        for df in sheet_df_map.values():
            df[target_column] = df[target_column].replace(location_to_id)

        return sheet_df_map

    def replace_job_title_to_ids(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        target_column: str,
    ) -> Dict[str, pd.DataFrame]:
        titles = set()
        for df in sheet_df_map.values():
            titles.update(df[target_column].dropna().unique())

        titles = list(titles)
        title_to_id = self.get_title_ids(titles)

        for df in sheet_df_map.values():
            df[target_column] = df[target_column].replace(title_to_id)

        return sheet_df_map

    def update_cache(self) -> None:
        """
        Update the QuantHub cache with the current state of the representation map.

        This method writes the updated representation map back to the QuantHub data bridge,
        ensuring that the latest changes are saved and available for future operations.
        """
        self.cache.update_qh_cache()

    def set(self, location: str, location_id: str) -> None:
        """Set the location ID for a given location and save it."""
        self._location_ids[location] = location_id
        self.save(self._location_ids)

    def update(self, location_ids: Dict[str, Optional[str]]) -> None:
        """Update the location IDs with a new dictionary and save the changes."""
        self._location_ids.update(location_ids)
        self.save(self._location_ids)


class EntityIDCache:
    """
    Use two RepresentationMapManager instances to serve location and title mappings.
    """

    def __init__(self):
        self.location_mgr = RepresentationMapManager(repr_map_key="LOCATION-CACHE", default_id_value=DEFAULT_LOC_ID)
        self.title_mgr = RepresentationMapManager(
            repr_map_key="TITLE-LEVEL-CACHE", default_id_value=DEFAULT_JOB_TITLE_ID
        )
        logger.info("EntityIDCache initialized using RepresentationMapManager.")

    @property
    def _location_ids(self):
        return self.location_mgr.cached_ids

    @property
    def _title_ids(self):
        return self.title_mgr.cached_ids

    def get_location_id(self, location: str) -> Optional[str]:
        return self.location_mgr.get_id(location)

    def get_title_id(self, title: str) -> Optional[str]:
        return self.title_mgr.get_id(title)

    def save_location_ids(self, raw_locations: List[str], loc_ids: List[str]) -> None:
        self.location_mgr.save_ids(raw_locations, loc_ids)

    def save_title_ids(self, raw_titles: List[str], title_ids: List[str]) -> None:
        self.title_mgr.save_ids(raw_titles, title_ids)

    def set_location(self, location: str, location_id: str) -> None:
        self.location_mgr.set_id(location, location_id)

    def set_title(self, title: str, title_id: str) -> None:
        self.title_mgr.set_id(title, title_id)

    def update_location_ids(self, location_ids: Dict[str, Optional[str]]) -> None:
        self.location_mgr.update_ids(location_ids)

    def update_title_ids(self, title_ids: Dict[str, Optional[str]]) -> None:
        self.title_mgr.update_ids(title_ids)

    def update_qh_cache(self) -> None:
        self.location_mgr.write_to_quanthub()
        self.title_mgr.write_to_quanthub()
