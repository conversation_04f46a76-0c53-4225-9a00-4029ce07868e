from typing import <PERSON><PERSON><PERSON>, Dict, List, Tuple, Type

from pydantic import BaseModel

from se_data_pipeline.component.base import CustomDF<PERSON>, Normaliser, Transformer
from se_data_pipeline.component.constants import ProviderNames
from se_data_pipeline.models import (
    draup,
    hire_ez,
    manual_research,
    manual_research_competitors,
    sales_research,
    sales_research_competitors,
    talent_demand,
    talent_insights,
)
from se_data_pipeline.normalisation import (
    DraupNormaliser,
    HireEZNormaliser,
    ManualResearchCompetitorsNormaliser,
    ManualResearchNormaliser,
    TalentDemandNormaliser,
    TalentInsightsNormaliser,
)
from se_data_pipeline.transformation import (
    DraupCompetitorsTransformer,
    DraupTransformer,
    HireEZCompetitorsTransformer,
    HireEZTransformer,
    ManualResearchCompetitorsTransformer,
    ManualResearchTransformer,
    SalesResearchCompetitorsComposer,
    SalesResearchComposer,
    TalentDemandCompetitorsTransformer,
    TalentDemandTransformer,
    TalentInsightsTransformer,
)


class DataProvider(BaseModel):
    """
    Represents a provider, including its name, model, normalizer, and transformations.
    """

    name: ProviderNames
    model: Tuple[Type[CustomDFM], ...]
    normaliser: Type[Normaliser]
    transformations: Dict[ProviderNames, Type[Transformer]]

    instances: ClassVar[List["DataProvider"]] = []

    def __init__(self, **data):
        """
        Initialize a DataProvider and add it to the instances list.
        """
        super().__init__(**data)
        self.__class__.instances.append(self)

    @classmethod
    def get_all_instances(cls):
        """
        Retrieve all instances of DataProvider.
        """
        return cls.instances

    @classmethod
    def get_all_data_groups(cls, key):
        """
        Retrieve all data groups associated with DataProvider instances.
        """
        groups = {
            ProviderNames.SALES_RESEARCH.name: SALES_RESEARCH_GROUPS,
            ProviderNames.SALES_RESEARCH_COMPETITORS.name: SALES_RESEARCH_COMPETITORS_GROUPS,
        }
        return groups.get(key)


DRAUP = DataProvider(
    name=ProviderNames.DRAUP,
    model=draup.MODELS,
    normaliser=DraupNormaliser,
    transformations={
        ProviderNames.SALES_RESEARCH: DraupTransformer,
        ProviderNames.SALES_RESEARCH_COMPETITORS: DraupCompetitorsTransformer,
    },
)

SALES_RESEARCH = DataProvider(
    name=ProviderNames.SALES_RESEARCH,
    model=sales_research.MODELS,
    normaliser=Normaliser,
    transformations={ProviderNames.SALES_RESEARCH: SalesResearchComposer},
)

SALES_RESEARCH_COMPETITORS = DataProvider(
    name=ProviderNames.SALES_RESEARCH_COMPETITORS,
    model=sales_research_competitors.MODELS,
    normaliser=Normaliser,
    transformations={ProviderNames.SALES_RESEARCH_COMPETITORS: SalesResearchCompetitorsComposer},
)

TALENT_INSIGHTS = DataProvider(
    name=ProviderNames.TALENT_INSIGHTS,
    model=talent_insights.MODELS,
    normaliser=TalentInsightsNormaliser,
    transformations={ProviderNames.SALES_RESEARCH: TalentInsightsTransformer},
)

MANUAL_RESEARCH = DataProvider(
    name=ProviderNames.MANUAL_RESEARCH,
    model=manual_research.MODELS,
    normaliser=ManualResearchNormaliser,
    transformations={ProviderNames.SALES_RESEARCH: ManualResearchTransformer},
)

MANUAL_RESEARCH_COMPETITORS = DataProvider(
    name=ProviderNames.MANUAL_RESEARCH_COMPETITORS,
    model=manual_research_competitors.MODELS,
    normaliser=ManualResearchCompetitorsNormaliser,
    transformations={ProviderNames.SALES_RESEARCH_COMPETITORS: ManualResearchCompetitorsTransformer},
)


HIRE_EZ = DataProvider(
    name=ProviderNames.HIRE_EZ,
    model=hire_ez.MODELS,
    normaliser=HireEZNormaliser,
    transformations={
        ProviderNames.SALES_RESEARCH: HireEZTransformer,
        ProviderNames.SALES_RESEARCH_COMPETITORS: HireEZCompetitorsTransformer,
    },
)

TALENT_DEMAND = DataProvider(
    name=ProviderNames.TALENT_DEMAND,
    model=talent_demand.MODELS,
    normaliser=TalentDemandNormaliser,
    transformations={
        ProviderNames.SALES_RESEARCH: TalentDemandTransformer,
        ProviderNames.SALES_RESEARCH_COMPETITORS: TalentDemandCompetitorsTransformer,
    },
)

SALES_RESEARCH_GROUPS = (
    [MANUAL_RESEARCH],
    [MANUAL_RESEARCH, DRAUP, HIRE_EZ],
    [MANUAL_RESEARCH, TALENT_DEMAND],
    [HIRE_EZ, MANUAL_RESEARCH],
    [HIRE_EZ, MANUAL_RESEARCH, TALENT_DEMAND],
    [DRAUP, MANUAL_RESEARCH],
    [DRAUP, HIRE_EZ],
    [DRAUP],
)

SALES_RESEARCH_COMPETITORS_GROUPS = (
    [DRAUP, MANUAL_RESEARCH_COMPETITORS, HIRE_EZ],
    [DRAUP],
    [HIRE_EZ, MANUAL_RESEARCH_COMPETITORS],
)
