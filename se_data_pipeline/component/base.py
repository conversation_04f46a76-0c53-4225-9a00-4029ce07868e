import re
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Set, Tuple, Iterable

import numpy as np
import pandas as pd
from pandera import DataFrameModel, check_types, dataframe_check
from unidecode import unidecode
from se_data_pipeline.component.logger import logger

from se_data_pipeline.component.constants import (
    CURRENCY_CODE,
    DRAUP_NOTES,
    FIN_AMOUNT,
    FIN_MULTIPLIER,
    NO_INFO,
    NOT_RESEARCHED,
    OPENAI_CONTENT_FILTER_MSG,
)
from se_data_pipeline.component.utils import normalise_strings, normalise_string


class Normaliser(ABC):
    """Abstract base class for data normalisation."""

    @abstractmethod
    def normalise(self) -> Dict[str, pd.DataFrame]:
        """Abstract method to normalise data."""
        pass


class Transformer(ABC):
    """Abstract base class for data transformation."""

    @abstractmethod
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Abstract method to transform data."""
        pass

    # TODO Move method to the mixin class
    def aggregate_tables(self, sheet_df_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Aggregate data tables for workforce locations and skills."""
        iwl = "IT Workforce Locations"
        iwl_group = "Location ID"
        # 1y growth and ratio are aggregated by sum, since data is expected from Talent Insights only
        iwl_agg_params = {
            "Location": lambda x: ";".join(x),
            "Location ID": "first",
            "Employees": "sum",
            "1y growth, %": "sum",
            "Departures": "sum",
            "Hires": "sum",
            "Ratio": "sum",
            "Net change": "sum",
            "# of Job Postings for 12 months": "sum",
            "Data as of": "first",
        }

        iws = "IT Workforce Skills"
        iws_group = "Skills"
        iws_agg_params = {
            "Skills": "first",
            "Employees": "sum",
            "1y growth": "sum",
            "# of Job Postings for 12 months": "sum",
            "Data as of": "first",
        }

        sheet_df_map = self.aggregate_table(sheet_df_map, iwl, iwl_group, iwl_agg_params)
        sheet_df_map = self.aggregate_table(sheet_df_map, iws, iws_group, iws_agg_params)
        return sheet_df_map

    def aggregate_table(
        self,
        sheet_df_map: Dict[str, pd.DataFrame],
        sheet: str,
        group: str,
        agg_params: dict,
    ) -> Dict[str, pd.DataFrame]:
        """Aggregate a single data table based on grouping and aggregation parameters."""
        if sheet in sheet_df_map:
            temp = "normalised"
            df = sheet_df_map[sheet].copy()
            df[temp] = normalise_strings(df[group])
            df = df.groupby(temp).agg(agg_params).reset_index()
            df = df.drop(columns=[temp])
            sheet_df_map[sheet] = df.copy()
        return sheet_df_map


class NormalizationMixin:
    def set_column_names(self):
        """Sets column names from the first row of each dataframe."""
        for sheet_name, df in self.sheet_df_map.items():
            if sheet_name == "Miscellaneous":
                continue
            df.columns = df.iloc[0]
            self.sheet_df_map[sheet_name] = df[1:].copy()
        return None

    def derive_table_status(self, management_sheets=None):
        """Derives and sets the status of each table based on its content."""
        if management_sheets is None:
            management_sheets = {}
        for sheet, df in self.sheet_df_map.items():
            status = None
            if df.empty:
                status = NOT_RESEARCHED
            elif df.isin({"n/a", "-"}).all().all():
                self.sheet_df_map[sheet] = df.iloc[0:0]
                status = NO_INFO
            if sheet in management_sheets:
                management_sheets[sheet] = status
            self.set_table_status(sheet, status)

        if all(status == NO_INFO for status in management_sheets.values()):
            self.set_table_status("Management Team Changes", NO_INFO)

    def replace_with_nan_values(self):
        """Replaces 'n/a' and '-' with NaN values in all dataframes."""
        for sheet, df in self.sheet_df_map.items():
            df.replace(["n/a", "-"], np.nan, inplace=True)
            df.infer_objects(copy=False)

    def fill_currency_code(self, financial_tables=None):
        """Fills in currency code for financial tables."""
        if financial_tables is None:
            financial_tables = {}
        for sheet_name in financial_tables:
            if sheet_name not in self.sheet_df_map:
                continue
            df = self.sheet_df_map[sheet_name].copy()
            currency_code = self._extract_currency_code(df)
            if currency_code:
                df = self._remove_currency_code(df)
                df["currency_code"] = currency_code
                self.sheet_df_map[sheet_name] = df
            else:
                df["currency_code"] = "USD"
                self.sheet_df_map[sheet_name] = df
        return None

    def _convert_to_int64(self, sheet_name: str, column_name: str) -> None:
        sheet_exists = sheet_name in self.sheet_df_map
        column_exists = column_name in self.sheet_df_map[sheet_name] if sheet_exists else False
        if sheet_exists and column_exists:
            int64_col = (
                pd.to_numeric(
                    self.sheet_df_map[sheet_name][column_name],
                    errors="raise",
                )
                .round()
                .astype("Int64")
            )
            self.sheet_df_map[sheet_name][column_name] = int64_col
        return None

    def apply_financial_to_int_conversion(self, fin_sheets=None):
        """Converts financial values (e.g., '10M', '1B') to integers."""
        if fin_sheets is None:
            fin_sheets = {}
        conversion = lambda x: (
            self._convert_financial_to_int(x) if isinstance(x, str) and re.match(FIN_AMOUNT, x) else x
        )
        for sheet in fin_sheets:
            if sheet in self.sheet_df_map:
                self.sheet_df_map[sheet] = self.sheet_df_map[sheet].map(conversion)

    def normalise_column_names(self):
        """Normalises column names to lowercase and snake_case."""
        for sheet_name in self.sheet_df_map:
            normalised_cols = [
                normalise_string(col) if isinstance(col, str) else col for col in self.sheet_df_map[sheet_name].columns
            ]
            if normalised_cols:
                self.sheet_df_map[sheet_name].columns = normalised_cols
        return None

    def set_project_dates(self, sheets=None):
        if sheets is None:
            sheets = {}
        for sheet in sheets:
            if sheet in self.sheet_df_map and not self.sheet_df_map[sheet].empty:
                df = self.sheet_df_map[sheet].copy()
                df["date"] = df["date"].astype(str)
                df["start_date"], df["end_date"] = zip(*df["date"].map(self._handle_date_format))
                self.sheet_df_map[sheet] = df
        return None

    def _handle_date_format(self, date_str):
        """Handles various date formats and extracts start and end dates."""
        # "March 2022 - April 2023"
        match = re.match(r"(\w+ \d{4})\s*[-–]\s*(\w+ \d{4})", date_str)
        if match:
            start_date = pd.to_datetime(match.group(1)).date()
            end_date = pd.to_datetime(match.group(2)).date()
            return start_date, end_date

        # "March 2022 - Present"
        match = re.match(r"(\w+ \d{4})\s*[-–]\s*Present", date_str)
        if match:
            start_date = pd.to_datetime(match.group(1)).date()
            end_date = None
            return start_date, end_date

        # "2021"
        match = re.match(r"\d{4}", date_str)
        if match:
            start_date = None
            end_date = pd.to_datetime("01-01-" + match.group(0)).date()
            return start_date, end_date

        # "01.05.2023"
        match = re.match(r"\d{2}.\d{2}.\d{4}", date_str)
        if match:
            start_date = None
            end_date = pd.to_datetime(match.group(0), dayfirst=True).date()
            return start_date, end_date

        # No matches
        return None, None

    def _set_top_date(self, sheets):
        for sheet_name, column, date in sheets:
            if sheet_name not in self.sheet_df_map:
                continue
            df = self.sheet_df_map[sheet_name]
            df.replace(["n/a", "N/A", "-"], np.nan, inplace=True)
            df.infer_objects(copy=False)
            columns_to_check = [x for x in df.columns if x != column]
            condition = df[column].isna() & df[columns_to_check].notna().any(axis=1)
            df.loc[condition, column] = date
        return None

    def drop_nan_rows(self):
        for sheet_name, df in self.sheet_df_map.items():
            self.sheet_df_map[sheet_name] = df.dropna(subset=[df.columns[0]])
        return None

    def drop_rows_with_strings(self):
        junk_strings = DRAUP_NOTES
        pattern = "|".join(junk_strings)
        for sheet_name, df in self.sheet_df_map.items():
            df = df[~df[df.columns[0]].astype(str).str.contains(pattern, na=False, regex=True)]
            self.sheet_df_map[sheet_name] = df.reset_index(drop=True)
        return None

    def _normalise_date_cols(self, date_sheets):
        for (sheet_name, date_col), params in date_sheets.items():
            if sheet_name in self.sheet_df_map:
                self.sheet_df_map[sheet_name][date_col] = pd.to_datetime(
                    self.sheet_df_map[sheet_name][date_col], **params
                ).dt.date
        return None

    def _translate_text(self, target_mapping: Iterable[Tuple[str, str]]):
        for sheet_name, column_name in target_mapping:
            if sheet_name in self.sheet_df_map:
                non_ascii_values = self.filter_non_ascii(self.sheet_df_map[sheet_name], column_name)
                if not non_ascii_values:
                    continue
                replacement_map = {value: self._translate_and_normalise_text(value) for value in non_ascii_values}
                self.sheet_df_map[sheet_name][column_name] = self.sheet_df_map[sheet_name][column_name].replace(
                    replacement_map
                )
        return None

    def filter_non_ascii(self, df, column_name) -> List[str]:
        """Filters rows with non-ASCII characters in the specified column."""
        df[column_name] = df[column_name].fillna("-")
        filtered = df[df[column_name].apply(lambda x: not x.isascii())]
        return filtered[column_name].unique().tolist()

    def _translate_and_normalise_text(self, value):
        """Translates and normalises text values using LLM and unicode conversion."""
        if pd.isna(value):
            return value
        try:
            translated = self.llm_data_handler.llm.translate_name(value)
            logger.debug(f"Translated: {value} -> {translated}")
        except ValueError as e:
            if str(e) == OPENAI_CONTENT_FILTER_MSG:
                translated = value
            else:
                raise e
        normalised = self._unicode_to_ascii(translated)
        return normalised

    def _unicode_to_ascii(self, value: str) -> str:
        """
        Converts an unidecode string to nearest ascii characters and replaces some
        special characters with their counterparts.
        """
        if pd.isna(value) or value.isascii():
            return value
        renaming_dict = {"@": "a"}
        renaming_str = str.maketrans(renaming_dict)
        return unidecode(value).translate(renaming_str)

    def _pivot_dataframes(self, pivot_tables):
        for sheet_name in pivot_tables:
            if sheet_name in self.sheet_df_map:
                self.sheet_df_map[sheet_name] = (
                    self.sheet_df_map[sheet_name].pivot_table(columns=0, aggfunc="first").reset_index(drop=True)
                )
        return None

    def _rename_df_column(self, sheets):
        for sheet_name, rename_map in sheets.items():
            if sheet_name in self.sheet_df_map:
                self.sheet_df_map[sheet_name].rename(columns=rename_map, inplace=True)
        return None

    def _extract_currency_code(self, df):
        """Extracts currency code from column names."""
        currency_codes = [re.search(CURRENCY_CODE, column_name) for column_name in df.columns]
        currency_codes = {next(group for group in match.groups() if group) for match in currency_codes if match}
        if len(currency_codes) > 1:
            raise ValueError(f"Multiple currency codes found in a sheet: {currency_codes}")
        return currency_codes.pop() if currency_codes else None

    def _remove_currency_code(self, df):
        """Removes currency code from column names."""
        remove_code = lambda x: re.sub(CURRENCY_CODE, "", x).strip()
        df.columns = [remove_code(column) for column in df.columns]
        return df

    def _convert_financial_to_int(self, value: str) -> int:
        """Converts a financial string value to an integer."""
        number, multiplier = value.replace(",", "").split()
        number = float(number)
        if multiplier in FIN_MULTIPLIER:
            return int(number * FIN_MULTIPLIER[multiplier])
        raise ValueError(f"Unknown magnitude: {multiplier}")


class TransformationMixin:
    @check_types
    def _expand_column_values(self, df: pd.DataFrame, column_delimiter: Dict[str, str]) -> pd.DataFrame:
        """Expands column values based on delimiters."""
        if df is None:
            return None
        data = df.copy()

        for column_name, delimiter in column_delimiter.items():
            data[column_name] = data[column_name].apply(
                lambda x: re.split(f"{delimiter}(?![^(]*\))", x) if isinstance(x, str) else x
            )
            data = data.explode(column_name).reset_index(drop=True)
            data[column_name] = data[column_name].str.strip()
        return data

    def _replace_location_ids(self, sheet_df_map: Dict[str, pd.DataFrame], sheets: Set[str]) -> Dict[str, pd.DataFrame]:
        """Replaces location names with location IDs."""
        location_id_map = {k: v for k, v in sheet_df_map.items() if k in sheets}
        location_id_map = self.llm_data_handler.replace_location_to_ids(
            location_id_map,
            target_column="Location ID",
            formatter_func=None,
        )

        sheet_df_map.update(location_id_map)
        return sheet_df_map

    def _replace_job_title_ids(
        self, sheet_df_map: Dict[str, pd.DataFrame], sheets: Set[str]
    ) -> Dict[str, pd.DataFrame]:
        """Replaces job titles with job title IDs."""
        job_title_id_map = {k: v for k, v in sheet_df_map.items() if k in sheets}
        job_title_id_map = self.llm_data_handler.replace_job_title_to_ids(
            job_title_id_map,
            target_column="Job Title ID",
        )

        sheet_df_map.update(job_title_id_map)

        return sheet_df_map

    def _remove_patterns(self, text):
        """Removes "Greater" and "Area" patterns from a given text.

        Args:
            text (str): The input text.

        Returns:
            str: The cleaned text.
        """
        text = re.sub(r"\bGreater\b|\bArea\b", "", text, flags=re.IGNORECASE)
        return text.replace("  ", " ").replace(" ,", ",")


class classproperty:
    """Decorator for creating class-level properties."""

    def __init__(self, fget):
        self.fget = fget

    def __get__(self, obj, owner):
        return self.fget(owner)


# pylint: disable=no-self-argument
class CustomDFM(DataFrameModel):
    """Custom DataFrameModel for handling specific sheet metadata."""

    _default: dict = {}
    _sheet_name: str = ""
    _rounding_rules = {}
    _alt_sheet_names: List[str] = []

    def __init__(self, *args, **kwargs):
        """Initialise CustomDFM instance."""
        super().__init__(*args, **kwargs)

    @classproperty
    def columns(cls) -> Optional[dict]:
        """Retrieve column metadata for the DataFrame."""
        return cls.get_columns()

    @classproperty
    def default_data(cls) -> dict:
        """Retrieve default data for the DataFrame."""
        return cls.get_default_data()

    @classproperty
    def default_df(cls) -> pd.DataFrame:
        """Retrieve default DataFrame instance."""
        return cls.get_default_df()

    @classproperty
    def sheet_name(cls) -> str:
        """Get the primary sheet name."""
        return cls._sheet_name

    @classmethod
    def get_columns(cls) -> Optional[dict]:
        """Retrieve column definitions for the class."""
        return cls.get_metadata().get(cls.__name__).get("columns")

    @classmethod
    def get_default_data(cls) -> dict:
        """Get default values for the class."""
        return cls._default.copy()

    @classmethod
    def get_sheet_name(cls, iterable) -> str:
        """Retrieve the matching sheet name from an iterable."""
        sheet_names = set([cls.sheet_name] + cls._alt_sheet_names)
        present = [x for x in sheet_names if x in iterable]
        if len(present) > 1:
            msg = f"Sheet exists under multiple names: expected <{cls.sheet_name}>, got <{present}>"
            raise ValueError(msg)
        if len(present) == 1:
            return present[0]
        return ""

    @classmethod
    def isin(cls, iterable) -> bool:
        """Check if any sheet name matches in the iterable."""
        return bool(cls.get_sheet_name(iterable))

    @classmethod
    def is_default_df(cls, df: pd.DataFrame) -> bool:
        """Check if the DataFrame is the default instance."""
        return df.equals(cls.get_default_df())

    @classmethod
    def get_default_df(cls) -> pd.DataFrame:
        """Get the default DataFrame instance and validate its dtypes against the schema."""
        df = pd.DataFrame(cls.get_default_data(), index=[0])
        schema = cls.to_schema()
        return schema.validate(df)

    @dataframe_check
    def apply_rounding(cls, df):
        """Apply rounding based on class-specific rules"""
        rules = getattr(cls, "_rounding_rules", {})
        for col, decimals in rules.items():
            if col in df.columns:
                df[col] = df[col].round(decimals)
        return True
