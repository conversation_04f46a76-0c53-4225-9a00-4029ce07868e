from pandas import Int64Dtype as Int64
from pandera import Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM


class Titles(CustomDFM):
    titles: Series[str]
    employees: Series[Int64] = Field(coerce=True, default=0)
    one_year_growth: Series[float] = Field(coerce=True, default=0)
    one_year_hires: Series[Int64] = Field(coerce=True, default=0)
    job_posts: Series[Int64] = Field(coerce=True, default=0)
    percent_of_employees: Series[float] = Field(coerce=True, default=0)
    your_percent: Series[float] = Field(coerce=True, default=0)

    _sheet_name = "Titles"


class Locations(CustomDFM):
    location: Series[str]
    employees: Series[Int64] = Field(coerce=True, default=0)
    one_year_growth: Series[float] = Field(coerce=True, default=0)
    one_year_hires: Series[Int64] = Field(coerce=True, default=0)
    job_posts: Series[Int64] = Field(coerce=True, default=0)
    percent_of_employees: Series[float] = Field(coerce=True, default=0)
    your_percent: Series[float] = Field(coerce=True, default=0)

    _sheet_name = "Locations"


class LocationMovements(CustomDFM):
    location: Series[str]
    departures: Series[Int64] = Field(coerce=True, default=0)
    hires: Series[Int64] = Field(coerce=True, default=0)
    ratio: Series[float] = Field(coerce=True, default=0)
    net_change: Series[Int64] = Field(coerce=True, default=0)

    _sheet_name = "Location Movements"


class Skills(CustomDFM):
    skills: Series[str]
    employees: Series[Int64] = Field(coerce=True, default=0)
    one_year_growth: Series[float] = Field(coerce=True, default=0)
    one_year_hires: Series[Int64] = Field(coerce=True, default=0)
    job_posts: Series[Int64] = Field(coerce=True, default=0)
    percent_of_employees: Series[float] = Field(coerce=True, default=0)
    your_percent: Series[float] = Field(coerce=True, default=0)

    _sheet_name = "Skills"


MODELS = (
    Titles,
    Locations,
    LocationMovements,
    Skills,
)
