import pandera as pa
from pandas import Int64Dtype as Int64
from pandera import Date, Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM
from se_data_pipeline.component.constants import (DATE, DEFAULT_LOC_ID,
                                                  TOP_YEAR, YEAR)
from se_data_pipeline.models import manual_research_competitors as mr


class GeneralOverview(CustomDFM):
    account: Series[str] = Field(alias="Account", nullable=True)
    about: Series[str] = Field(alias="About", nullable=True)
    hq_location: Series[str] = Field(alias="HQ Location", nullable=True)
    location_id: Series[str] = Field(alias="Location ID", nullable=True)
    industry: Series[str] = Field(alias="Industry", nullable=True)
    revenue: Series[Int64] = Field(alias="Revenue (USD)", coerce=True)
    number_of_employees: Series[Int64] = Field(alias="Number of Employees", nullable=True, coerce=True)
    company_type: Series[str] = Field(alias="Company Type", nullable=True)
    website: Series[str] = Field(alias="Website", nullable=True)
    logo_url: Series[str] = Field(alias="Logo URL", nullable=True)
    valuation: Series[Int64] = Field(alias="Valuation (USD)", nullable=True)
    total_funding_amount: Series[int] = Field(alias="Total Funding Amount (USD)", nullable=True)
    number_of_funding_rounds: Series[int] = Field(alias="# Of Funding Rounds", nullable=True)
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name: str = "General Overview"
    _default: dict = {
        "Account": None,
        "About": None,
        "HQ Location": None,
        "Location ID": DEFAULT_LOC_ID,
        "Industry": None,
        "Revenue (USD)": 0,
        "Number of Employees": 0,
        "Company Type": None,
        "Website": None,
        "Logo URL": None,
        "Valuation (USD)": 0,
        "Total Funding Amount (USD)": 0,
        "# Of Funding Rounds": 0,
        "Data as of": DATE,
    }


class Fundings(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    year: Series[int] = Field(alias="Year")
    transaction_name: Series[str] = Field(alias="Transaction Name")
    valuation: Series[Int64] = Field(alias="Valuation (USD)", coerce=True)
    money_raised: Series[Int64] = Field(alias="Money Raised (USD)", coerce=True)
    investors: Series[str] = Field(alias="Investors")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name: str = "Fundings"
    _default = {
        "Row ID": 1,
        "Year": TOP_YEAR,
        "Transaction Name": "-",
        "Valuation (USD)": 0,
        "Money Raised (USD)": 0,
        "Investors": "-",
        "Data as of": DATE,
    }


class Financials(CustomDFM):
    year: Series[Int64] = Field(alias="Year", nullable=True, coerce=True)
    financial_metrics: Series[str] = Field(alias="Financial Metrics")
    category_of_financial_metrics: Series[str] = Field(alias="Category of Financial Metrics")
    revenue: Series[float] = Field(alias="Revenue (USD)", coerce=True, default=0.0)
    ebitda: Series[float] = Field(alias="EBITDA (USD)", coerce=True, default=0.0)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Financials"
    _default = {
        "Year": TOP_YEAR,
        "Financial Metrics": "-",
        "Category of Financial Metrics": "-",
        "Revenue (USD)": 0,
        "EBITDA (USD)": 0,
        "Source": "-",
        "Data as of": DATE,
    }


class Strategy(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    category: Series[str] = Field(alias="Category", nullable=True)
    title: Series[str] = Field(alias="Title", nullable=True)
    description: Series[str] = Field(alias="Description", nullable=True)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Strategy"
    _default = {
        "Row ID": 1,
        "Category": "-",
        "Title": "-",
        "Description": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class Offerings(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    offerings: Series[str] = Field(alias="Offerings")
    offerings_by_category: Series[str] = Field(alias="Offerings by Category", nullable=True)
    products_and_services: Series[str] = Field(alias="Products and Services", nullable=True)
    solution_capabilities: Series[str] = Field(alias="Solution & Capabilities", nullable=True)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Offerings"
    _default = {
        "Row ID": 1,
        "Offerings": "-",
        "Offerings by Category": "-",
        "Products and Services": "-",
        "Solution & Capabilities": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class Relationships(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    year: Series[Int64] = Field(alias="Year", nullable=True, coerce=True)
    category: Series[str] = Field(alias="Category")
    company_name: Series[str] = Field(alias="Company Name")
    additional_information: Series[str] = Field(alias="Additional Information", nullable=True)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Relationships"
    _default = {
        "Row ID": 1,
        "Year": YEAR,
        "Category": "-",
        "Company Name": "-",
        "Additional Information": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class Clients(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    company_name: Series[str] = Field(alias="Company Name")
    epam_status: Series[str] = Field(alias="EPAM Status", nullable=True, default="-")
    additional_information: Series[str] = Field(alias="Additional Information")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Clients"
    _default = {
        "Row ID": 1,
        "Company Name": "-",
        "EPAM Status": "-",
        "Additional Information": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class JobPostings(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    job_title: Series[str] = Field(alias="Job Title")
    skills: Series[str] = Field(alias="Skills")
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID", nullable=True)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "JobPostings"
    _default = {
        "Row ID": 1,
        "Job Title": "-",
        "Skills": "-",
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Source": "-",
        "Data as of": DATE,
    }


class WorkforceByLocation(CustomDFM):
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID", nullable=True)
    employees: Series[Int64] = Field(alias="Employees")
    one_year_growth_percentage: Series[float] = Field(alias="1y growth, %")
    departures: Series[Int64] = Field(alias="Departures")
    hires: Series[Int64] = Field(alias="Hires")
    net_change: Series[float] = Field(alias="Net change")
    job_posts: Series[Int64] = Field(alias="Job Posts", default=0, coerce=True)
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "WorkforceByLocation"
    _default = {
        "Location": "-",
        "Location ID": None,
        "Employees": 0,
        "1y growth, %": 0.0,
        "Departures": 0,
        "Hires": 0,
        "Net change": 0.0,
        "Job Posts": 0,
        "Data as of": DATE,
    }


class WorkforceBySkills(CustomDFM):
    skills: Series[str] = Field(alias="Skills", default="-")
    employees: Series[Int64] = Field(alias="Employees", default=0, coerce=True)
    job_posts: Series[int] = Field(alias="Job Posts")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "WorkforceBySkills"
    _default = {
        "Skills": "-",
        "Employees": 0,
        "Job Posts": 0,
        "Data as of": DATE,
    }


class JobTitlesInDemand(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    title: Series[str] = Field(alias="Title", nullable=True)
    job_posts: Series[int] = Field(alias="Job Posts")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "JobTitlesInDemand"
    _default = {
        "Row ID": 1,
        "Title": "-",
        "Job Posts": 0,
        "Data as of": DATE,
    }


class AwardsAndRecognitions(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    year: Series[int] = Field(alias="Year")
    awards_recognitions: Series[str] = Field(alias="Awards & Recognitions")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Awards & Recognitions"
    _default = {
        "Row ID": 1,
        "Year": YEAR,
        "Awards & Recognitions": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class AdditionalInformation(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    date: Series[Date] = Field(alias="Date")
    additional_materials: Series[str] = Field(alias="Additional Materials")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "AdditionalInformation"
    _default = {
        "Row ID": 1,
        "Date": DATE,
        "Additional Materials": "-",
        "Source": "-",
        "Data as of": DATE,
    }


class InfoNgen(CustomDFM):
    description: Series[str] = Field(alias="Description", nullable=True)

    _sheet_name = "InfoNgen"
    _default = {
        "Description": "-",
    }


class DataAvailability(CustomDFM):
    table: Series[str] = Field(alias="Table")
    status: Series[str] = Field(alias="Status of the research", nullable=True)
    data_as_of: Series[Date] = Field(alias="Data as of")

    _sheet_name = "Data Availability"
    _default = {
        "Table": [
            GeneralOverview.sheet_name,
            Fundings.sheet_name,
            Financials.sheet_name,
            Clients.sheet_name,
            InfoNgen.sheet_name,
            # Events categories
            "Focus Areas",
            "Risks and Challenges",
            "Market Positioning",
            JobPostings.sheet_name,
            JobTitlesInDemand.sheet_name,
            WorkforceByLocation.sheet_name,
            WorkforceBySkills.sheet_name,
            mr.AwardsAndRecognitions.sheet_name,
            AdditionalInformation.sheet_name,
            "Offerings by Industry",
            "Offerings by Services",
            mr.Acquisitions.sheet_name,
            mr.Partnerships.sheet_name,
            mr.Investments.sheet_name,
            mr.Mergers.sheet_name,
        ],
        "Status of the research": None,
        "Data as of": DATE,
    }


MODELS = [
    GeneralOverview,
    Fundings,
    Financials,
    Strategy,
    Offerings,
    Relationships,
    Clients,
    JobPostings,
    WorkforceByLocation,
    WorkforceBySkills,
    JobTitlesInDemand,
    AwardsAndRecognitions,
    AdditionalInformation,
    InfoNgen,
    DataAvailability,
]
