from pandas import Int64Dtype as Int64
from pandas import Series as pdSeries
from pandera import Date, Field, check
from pandera.typing import Series
from typing import Optional

from se_data_pipeline.component.base import CustomDFM
from se_data_pipeline.component.constants import COMPANY_TYPES, TOP_YEAR, MERGING_LOGICS_SHEETS


class MergingLogic(CustomDFM):
    table: Series[str] = Field(isin=MERGING_LOGICS_SHEETS)
    merging_way: Series[str] = Field(default="-")
    description: Optional[Series[str]]
    final_report_tab: Series[str] = Field(nullable=True)
    draup_info_tab: Series[str] = Field(nullable=True)

    _sheet_name: str = "Merging Logics"


class GeneralOverview(CustomDFM):
    account: Series[str]
    about: Series[str]
    hq_location: Series[str]
    parent_company: Series[str] = Field(default="-")
    industry: Series[str]
    number_of_employees: Series[int] = Field(coerce=True)
    company_type: Series[str]
    website: Series[str]
    revenue: Series[int] = Field(coerce=True)
    it_spend: Series[int] = Field(coerce=True)
    currency_code: Series[str]
    logo_url: Series[str]

    _sheet_name: str = "General Overview"

    @check("company_type")
    def valid_company_type(self, company_type: pdSeries) -> pdSeries:
        return company_type.str.upper().isin(COMPANY_TYPES)


class FundingRounds(CustomDFM):
    year: Series[int] = Field(coerce=True, nullable=True)
    transaction_name: Series[str] = Field(default="-")
    valuation: Series[Int64] = Field(coerce=True, default=0)
    money_raised: Series[Int64] = Field(coerce=True, default=0)
    currency_code: Series[str] = Field(default="-")
    investors: Series[str] = Field(default="-")

    _sheet_name: str = "Funding Rounds"


class CurrentEngagements(CustomDFM):
    company_name: Series[str] = Field(default="-")
    customer_code: Series[str] = Field(default="-")
    status: Series[str] = Field(default="-")
    gbu: Series[str] = Field(default="-")
    account_manager: Series[str] = Field(default="-")
    sales_manager: Series[str] = Field(default="-")
    telescope_link: Series[str] = Field(default="-")
    crm_link: Series[str] = Field(default="-")

    _sheet_name = "Current Engagements"


class FinancialTrend(CustomDFM):
    year: Series[Int64] = Field(coerce=True, default=TOP_YEAR)
    revenue: Series[Int64] = Field(coerce=True, default=0)
    it_spend: Series[Int64] = Field(coerce=True, default=0)
    ebitda: Series[Int64] = Field(coerce=True, default=0)
    source: Series[str] = Field(default="-")

    _sheet_name = "Financial Trend"


class CompanyEntities(CustomDFM):
    subsidiary_name: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    website: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")
    status: Series[str] = Field(default="-")
    sales_manager: Series[str] = Field(default="-")
    account_manager: Series[str] = Field(default="-")
    crm_link: Series[str] = Field(default="-")
    link_to_the_report: Series[str] = Field(default="-")
    comments: Series[str] = Field(default="-")

    _sheet_name = "Company Entities"


class CategorizedRevenue(CustomDFM):
    year: Series[int] = Field(coerce=True, nullable=True)
    financial_metrics: Series[str] = Field(default="-")
    category_of_financial_metrics: Series[str] = Field(default="-")
    revenue: Series[Int64] = Field(coerce=True, default=0)
    currency_code: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Categorized Revenue"


class BusinessStructure(CustomDFM):
    business_unit: Series[str] = Field(default="-")
    products_and_services: Series[str] = Field(default="-")
    additional_information: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Business Structure"


class FeaturedClients(CustomDFM):
    company_name: Series[str] = Field(default="-")
    industry: Series[str] = Field(default="-")
    additional_information: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Featured Clients"


class AdditionalInformation(CustomDFM):
    date: Series[Date] = Field(nullable=True)
    additional_materials: Series[str]
    source: Series[str] = Field(default="-")

    _sheet_name = "Additional Information"


class StrategyRisksInitiatives(CustomDFM):
    category: Series[str] = Field(default="-")
    title: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Strategy, Risks and Initiatives"


class Partnerships(CustomDFM):
    company_name: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    date: Series[Date] = Field(nullable=True)
    source: Series[str] = Field(default="-")

    _sheet_name = "Partnerships"


class Acquisitions(CustomDFM):
    company_name: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    epam_clients: Series[str] = Field(default="-")
    date: Series[Date] = Field(nullable=True)
    source: Series[str] = Field(default="-")

    _sheet_name = "Acquisitions"


class Mergers(CustomDFM):
    company_name: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    epam_clients: Series[str] = Field(default="-")
    date: Series[Date] = Field(nullable=True)
    source: Series[str] = Field(default="-")

    _sheet_name = "Mergers"


class Investments(CustomDFM):
    company_name: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    epam_clients: Series[str] = Field(default="-")
    date: Series[Date] = Field(nullable=True)
    source: Series[str] = Field(default="-")

    _sheet_name = "Investments"


class TechStack(CustomDFM):
    category: Series[str] = Field(default="-")
    technology_tools: Series[str] = Field(default="-")

    _sheet_name = "Tech Stack"


class InternalProjects(CustomDFM):
    project_type: Series[str] = Field(default="-")
    technology_tools: Series[str] = Field(default="-")
    project_description: Series[str] = Field(default="-")
    date: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Internal Projects"


class Outsourcing(CustomDFM):
    project_type: Series[str] = Field(default="-")
    technology_tools: Series[str] = Field(default="-")
    company_name: Series[str] = Field(default="-")
    project_description: Series[str] = Field(default="-")
    date: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Outsourcing"


class ITJobPostings(CustomDFM):
    job_title: Series[str] = Field(default="-")
    skills: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "IT Job Postings"


class Competitors(CustomDFM):
    competitor_name: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Competitors"


class KeyPeople(CustomDFM):
    name: Series[str] = Field(default="-")
    job_title: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")

    _sheet_name = "Key People"


class Connections(CustomDFM):
    name: Series[str] = Field(default="-")
    job_title: Series[str] = Field(default="-")
    first_level_connections: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")

    _sheet_name = "Connections"


class Priorities(CustomDFM):
    row_id: Series[int] = Field(coerce=True)
    priority_driver: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    justification: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name = "Priorities"


class ManagementTeamHire(CustomDFM):
    name: Series[str] = Field(default="-")
    job_title_after_hire: Series[str] = Field(default="-")
    hire_date: Series[Date] = Field(nullable=True)
    location: Series[str] = Field(default="-")
    previous_position: Series[str] = Field(default="-")
    previous_company: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")

    _sheet_name = "Management Team Hire"


class ManagementTeamExit(CustomDFM):
    name: Series[str] = Field(default="-")
    job_title_after_exit: Series[str] = Field(default="-")
    exit_date: Series[Date] = Field(nullable=True)
    location: Series[str] = Field(default="-")
    previous_position: Series[str] = Field(default="-")
    joining_company: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")

    _sheet_name = "Management Team Exit"


class ManagementTeamPromo(CustomDFM):
    name: Series[str] = Field(default="-")
    job_title_after_promo: Series[str]
    promo_date: Series[Date] = Field(nullable=True)
    location: Series[str] = Field(default="-")
    previous_position: Series[str]
    linkedin_url: Series[str] = Field(default="-")

    _sheet_name = "Management Team Promo"


class SWOTAnalysis(CustomDFM):
    category: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")

    _sheet_name = "SWOT Analysis"


class InfoNgen(CustomDFM):
    description: Series[str]

    _sheet_name = "InfoNgen"


MODELS = (
    MergingLogic,
    GeneralOverview,
    FundingRounds,
    CurrentEngagements,
    CompanyEntities,
    FinancialTrend,
    CategorizedRevenue,
    BusinessStructure,
    FeaturedClients,
    StrategyRisksInitiatives,
    Partnerships,
    Acquisitions,
    Mergers,
    Investments,
    TechStack,
    InternalProjects,
    Outsourcing,
    ITJobPostings,
    Competitors,
    KeyPeople,
    Connections,
    Priorities,
    ManagementTeamHire,
    ManagementTeamExit,
    ManagementTeamPromo,
    AdditionalInformation,
    SWOTAnalysis,
    InfoNgen,
)
