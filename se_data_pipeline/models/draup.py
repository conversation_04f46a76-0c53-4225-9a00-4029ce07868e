from typing import Optional

from pandas import Int64Dtype as Int64
from pandera import Date, Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM
from se_data_pipeline.component.constants import COMPANY_TYPES


class About(CustomDFM):
    about: Optional[Series[str]]
    account: Series[str]
    download_date: Series[str] = Field(nullable=True)

    _sheet_name = "About"

    @classmethod
    def isin(cls, iterable) -> bool:
        present = [cls.sheet_name for x in iterable if cls.sheet_name in x]
        if len(present) > 1:
            msg = f"Sheet exists under multiple names: expected <{cls.sheet_name}>, got <{present}>"
            raise ValueError(msg)
        if len(present) == 1:
            return True
        return False


class GeneralOverview(CustomDFM):
    account: Series[str]
    company_type: Series[str] = Field(isin=COMPANY_TYPES)
    download_date: Series[str] = Field(default="-")
    facebook_page: Optional[Series[str]]
    hq_location: Series[str]
    instagram_page: Optional[Series[str]]
    linkedin_page: Optional[Series[str]]
    primary_vertical: Series[str] = Field(default="-")
    subvertical: Optional[Series[str]]
    total_workforce: Optional[Series[Int64]]
    twitter_page: Optional[Series[str]]
    vertical: Series[str] = Field(default="-")
    website: Series[str] = Field(default="-")
    year_of_establishment: Series[str] = Field(coerce=True)
    youtube_page: Optional[Series[str]]

    _sheet_name = "General Overview"


class FinancialsTrend(CustomDFM):
    download_date: Series[str] = Field(coerce=True, default="-")
    ebitda: Series[Int64] = Field(coerce=True, default=0)
    financial_metric: Series[Int64] = Field(coerce=True, default=0)
    it_spend: Series[Int64] = Field(coerce=True, default=0)
    rd_intensity: Series[str] = Field(default="-", coerce=True)
    rd_spend: Series[Int64] = Field(coerce=True, default=0)
    revenue: Series[Int64] = Field(coerce=True, default=0)
    currency_code: Series[str] = Field(default="-")

    _sheet_name = "Financials Trend"


class RevenueBusiness(CustomDFM):
    business: Series[str]
    revenue: Series[Int64] = Field(coerce=True, default=0)
    yoy_growth: Optional[Series[str]] = Field(default="-", coerce=True)
    financial_year: Series[Int64] = Field(coerce=True, nullable=True)
    currency_code: Series[str] = Field(default="-")

    _sheet_name = "Revenue by Business"


class RevenueRegion(CustomDFM):
    region: Series[str]
    revenue: Series[Int64] = Field(coerce=True, default=0)
    yoy_growth: Optional[Series[str]] = Field(default="-", coerce=True)
    financial_year: Series[Int64] = Field(nullable=True, coerce=True)
    currency_code: Series[str] = Field(default="-")

    _sheet_name = "Revenue by Region"


class Signals(CustomDFM):
    company_names: Series[str]
    signal_title: Series[str] = Field(default="-")
    key_highlights: Series[str] = Field(default="-")
    signal_category: Series[str] = Field(default="-")
    # signal_type: Series[str] = Field(default="-")
    tags: Series[str] = Field(default="-")
    url: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")
    date: Series[Date] = Field(nullable=True, coerce=True)

    _sheet_name = "Signals"


class AccountPriorities(CustomDFM):
    category: Series[str]
    priority: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")

    _sheet_name = "Account Priorities"


class AcquisitionsDetails(CustomDFM):
    announced_date: Series[Date] = Field(nullable=True, coerce=True)
    acquired: Series[str] = Field(default="-")
    acquisition_primary_intent: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    acquistion_size: Series[str] = Field(default="-")
    universe_logo_url: Series[str] = Field(default="-")
    universe_account_id: Series[str] = Field(default="-")

    _sheet_name = "Acquisitions Details"


class InvestmentsDetails(CustomDFM):
    date: Series[Date] = Field(nullable=True, coerce=True)
    organization_name: Series[str]
    investment_type: Series[str] = Field(default="-")
    amount: Series[str] = Field(default="-")
    round: Series[str] = Field(default="-")
    universe_logo_url: Series[str] = Field(default="-")
    universe_account_id: Series[str] = Field(default="-")

    _sheet_name = "Investments Details"


class EngagementDetails(CustomDFM):
    customer: Series[str]
    epam_status: Optional[Series[str]] = Field(default="-")
    new_addition: Series[str] = Field(default="-")
    customer_verticals: Series[str] = Field(default="-")
    workloads: Series[str] = Field(default="-")
    customer_locations: Series[str] = Field(default="-")
    company_locations: Series[str] = Field(default="-")
    customer_hq_location: Series[str] = Field(default="-")
    number_of_active_workflows: Series[Int64] = Field(default=0, coerce=True)

    _sheet_name = "Engagement Details"


class BusinessIntentions(CustomDFM):
    business_intention: Series[str]
    digital_initiative: Series[str] = Field(default="-")
    digital_initiative_description: Series[str] = Field(default="-")
    development_stage: Series[str] = Field(default="-")
    use_case: Series[str] = Field(default="-")
    use_case_description: Series[str] = Field(default="-")
    digitization_theme: Series[str] = Field(default="-")
    use_case_cluster: Series[str] = Field(default="-")
    technology_areas: Series[str] = Field(default="-")

    _sheet_name = "Business Intentions"


class Solutions(CustomDFM):
    category: Series[str]
    workload: Series[str] = Field(default="-")
    subcategory: Series[str] = Field(default="-")
    solution: Series[str] = Field(default="-")
    developer_company: Series[str] = Field(default="-")
    usage_intensity: Series[str] = Field(default="-")
    country_presence: Series[str] = Field(default="-")

    _sheet_name = "Solutions"


class OutsourcingProviders(CustomDFM):
    provider_name: Series[str]
    new_addition: Series[str] = Field(default="-")
    workloads: Series[str] = Field(default="-")
    company_locations: Series[str] = Field(default="-")
    service_partner_locations: Series[str] = Field(default="-")
    relationship_highlights: Series[str] = Field(default="-")
    num_active_workflows: Series[Int64] = Field(coerce=True, default=0)

    _sheet_name = "Outsourcing Providers"
    _alt_sheet_names = ["Service Provider Partners"]


class JobPostings(CustomDFM):
    job_title: Series[str]
    location: Series[str] = Field(default="-")
    workloads: Series[str] = Field(default="-")
    business_functions: Series[str] = Field(default="-")
    core_skills: Series[str] = Field(default="-")
    benefits: Series[str] = Field(default="-")
    description: Series[str] = Field(default="-")
    posting_date: Series[Date] = Field(nullable=True, coerce=True)

    _sheet_name = "Job Postings"


class JobTitlesDemand(CustomDFM):
    job_titles: Series[str]
    num_job_postings: Series[int] = Field(coerce=True, default=0)
    trailing_trend: Optional[Series[str]] = Field(default="-")

    _sheet_name = "Job Titles in Demand"


class HiringMap(CustomDFM):
    location: Series[str]
    num_job_postings: Series[int] = Field(default=0, coerce=True)

    _sheet_name = "Hiring Map"


class CoreSkillsDemand(CustomDFM):
    core_skills: Series[str] = Field(default="-")
    num_job_postings: Series[Int64] = Field(coerce=True, default=0)
    trailing_trend: Optional[Series[str]] = Field(default="-")

    _sheet_name = "Core Skills in Demand"


class CompetitorInformation(CustomDFM):
    competitor_name: Series[str]
    verticals: Series[str] = Field(default="-")

    _sheet_name = "Competitor Information"


class KeyExecutives(CustomDFM):
    id: Series[int] = Field(coerce=True)
    name: Series[str]
    title: Series[str] = Field(default="-")
    business_function: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    email_adress: Series[str] = Field(default="-")
    linkedin_url: Series[str] = Field(default="-")
    draup_url: Series[str] = Field(default="-")

    _sheet_name = "Key Executives"


class ExecutiveMovementExits(CustomDFM):
    executive: Series[str]
    position_before_exit: Series[str] = Field(default="-")
    location_before_exit: Series[str] = Field(default="-")
    exit_month: Series[Date] = Field(nullable=True, coerce=True)
    joining_position: Series[str] = Field(default="-")
    joining_company: Series[str] = Field(default="-")
    joining_location: Series[str] = Field(default="-")
    joining_month: Series[Date] = Field(nullable=True, coerce=True)

    _sheet_name = "Executive Movement Exits"


class ExecutiveMovementHires(CustomDFM):
    executive: Series[str]
    position_after_hire: Series[str] = Field(default="-")
    location_after_hire: Series[str] = Field(default="-")
    hire_month: Series[Date] = Field(nullable=True, coerce=True)
    position_before_hire: Series[str] = Field(default="-")
    location_before_hire: Series[str] = Field(default="-")
    company_before_hire: Series[str] = Field(default="-")

    _sheet_name = "Executive Movement Hires"


class ExecutiveMovementPromotions(CustomDFM):
    executive: Series[str]
    position_after_promotion: Series[str] = Field(default="-")
    location_after_promotion: Series[str] = Field(default="-")
    promotion_month: Series[Date] = Field(nullable=True, coerce=True)
    position_before_promotion: Series[str] = Field(default="-")
    location_before_promotion: Series[str] = Field(default="-")
    joining_last_promotion_month: Series[str] = Field(default="-", coerce=True)

    _sheet_name = "Executive Movement Promotions"


MODELS = (
    About,
    GeneralOverview,
    FinancialsTrend,
    RevenueBusiness,
    RevenueRegion,
    Signals,
    BusinessIntentions,
    AccountPriorities,
    AcquisitionsDetails,
    InvestmentsDetails,
    EngagementDetails,
    Solutions,
    OutsourcingProviders,
    JobPostings,
    JobTitlesDemand,
    HiringMap,
    CoreSkillsDemand,
    CompetitorInformation,
    KeyExecutives,
    ExecutiveMovementExits,
    ExecutiveMovementHires,
    ExecutiveMovementPromotions,
)
