from pandas import Int64Dtype as Int64
from pandera import Date, Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM
from se_data_pipeline.component.constants import DATE, DATETIME, DEFAULT_JOB_TITLE_ID, DEFAULT_LOC_ID, TOP_DATE, YEAR
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.component.glossary import CodeListManager
from se_data_pipeline.component.constants import ProviderNames


codelist_ref = CodeListManager(ProviderNames.SALES_RESEARCH)


class GeneralOverview(CustomDFM):
    account: Series[str] = Field(alias="Account")
    about: Series[str] = Field(alias="About", nullable=True)
    hq_location: Series[str] = Field(alias="HQ Location")
    location_id: Series[str] = Field(alias="Location ID")
    parent_company: Series[str] = Field(alias="Parent Company")
    industry: Series[str] = Field(alias="Industry")
    number_of_employees: Series[Int64] = Field(alias="Number of Employees", nullable=True, coerce=True)
    company_type: Series[str] = Field(alias="Company Type")
    website: Series[str] = Field(alias="Website")
    revenue: Series[int] = Field(alias="Revenue", coerce=True)
    it_spend: Series[int] = Field(alias="IT Spend", coerce=True)
    valuation: Series[int] = Field(alias="Valuation")
    total_funding_amount: Series[int] = Field(alias="Total Funding Amount")
    currency_code: Series[str] = Field(alias="Currency Code")
    number_of_funding_rounds: Series[int] = Field(alias="# Of Funding Rounds")
    logo_url: Series[str] = Field(alias="Logo URL", nullable=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _rounding_rules = {"Revenue": 0, "IT Spend": 0, "Valuation": 0, "Total Funding Amount": 0}

    _sheet_name: str = "General Overview"
    _default: dict = {
        "Account": None,
        "About": None,
        "HQ Location": None,
        "Location ID": None,
        "Parent Company": "-",
        "Industry": None,
        "Number of Employees": None,
        "Company Type": None,
        "Website": None,
        "Revenue": 0,
        "IT Spend": 0,
        "Valuation": 0,
        "Total Funding Amount": 0,
        "Currency Code": "-",
        "# Of Funding Rounds": 0,
        "Logo URL": None,
        "Data as of": DATETIME,
    }


class FundingRounds(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    year: Series[int] = Field(alias="Year", default=YEAR)
    transaction_name: Series[str] = Field(alias="Transaction Name")
    valuation: Series[int] = Field(alias="Valuation", coerce=True)
    money_raised: Series[int] = Field(alias="Money Raised", coerce=True)
    currency_code: Series[str] = Field(alias="Currency Code")
    investors: Series[str] = Field(alias="Investors")
    data_as_of: Series[str] = Field(alias="Data as of")

    _rounding_rules = {"Valuation": 0, "Money Raised": 0}

    _sheet_name: str = "Funding Rounds"
    _default = {
        "Row ID": 1,
        "Year": YEAR,
        "Transaction Name": "-",
        "Valuation": 0,
        "Money Raised": 0,
        "Currency Code": "-",
        "Investors": "-",
        "Data as of": DATETIME,
    }


class CurrentEngagements(CustomDFM):
    company_name: Series[str] = Field(alias="Company name")
    customer_code: Series[str] = Field(alias="Customer code")
    status: Series[str] = Field(alias="Status")
    gbu: Series[str] = Field(alias="GBU")
    account_manager: Series[str] = Field(alias="Account Manager")
    sales_manager: Series[str] = Field(alias="Sales Manager")
    telescope_link: Series[str] = Field(alias="Telescope Link")
    crm_link: Series[str] = Field(alias="CRM Link")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Current Engagements"
    _default = {
        "Company name": "-",
        "Customer code": "-",
        "Status": "-",
        "GBU": "-",
        "Account Manager": "-",
        "Sales Manager": "-",
        "Telescope Link": "-",
        "CRM Link": "-",
        "Data as of": DATETIME,
    }


class Financials(CustomDFM):
    year: Series[int] = Field(alias="Year", nullable=True)
    financial_metrics: Series[str] = Field(alias="Financial Metrics", isin=codelist_ref.financial_metrics_names)
    category_of_financial_metrics: Series[str] = Field(alias="Category of Financial Metrics")
    location_id: Series[str] = Field(alias="Location ID")
    revenue: Series[int] = Field(alias="Revenue", coerce=True)
    it_spend: Series[int] = Field(alias="IT Spend", coerce=True)
    ebitda: Series[int] = Field(alias="EBITDA", coerce=True)
    currency_code: Series[str] = Field(alias="Currency Code")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _rounding_rules = {"Revenue": 0, "IT Spend": 0, "EBITDA": 0}

    _sheet_name = "Financials"
    _default = {
        "Year": YEAR,
        "Financial Metrics": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Category of Financial Metrics": "-",
        "Revenue": 0,
        "IT Spend": 0,
        "EBITDA": 0,
        "Currency Code": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class CompanyEntities(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    subsidiary_name: Series[str] = Field(alias="Subsidiary Name")
    description: Series[str] = Field(alias="Description")
    website: Series[str] = Field(alias="Website")
    linkedin_url: Series[str] = Field(alias="LinkedIn URL")
    status: Series[str] = Field(alias="Status")
    sales_manager: Series[str] = Field(alias="Sales Manager")
    account_manager: Series[str] = Field(alias="Account Manager")
    crm_link: Series[str] = Field(alias="CRM Link")
    link_to_the_report: Series[str] = Field(alias="Link to the Report")
    comments: Series[str] = Field(alias="Comments")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Company Entities"
    _default = {
        "Row ID": 1,
        "Subsidiary Name": "-",
        "Description": "-",
        "Website": "-",
        "LinkedIn URL": "-",
        "Status": "-",
        "Sales Manager": "-",
        "Account Manager": "-",
        "CRM Link": "-",
        "Link to the Report": "-",
        "Comments": "-",
        "Data as of": DATETIME,
    }


class BusinessStructure(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    business_unit: Series[str] = Field(alias="Business Unit")
    products_and_services: Series[str] = Field(alias="Products and Services")
    additional_information: Series[str] = Field(alias="Additional Information")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Business Structure"
    _default = {
        "Row ID": 1,
        "Business Unit": "-",
        "Products and Services": "-",
        "Additional Information": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class FeaturedClients(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    company_name: Series[str] = Field(alias="Company Name")
    industry: Series[str] = Field(alias="Industry")
    additional_information: Series[str] = Field(alias="Additional Information")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Featured Clients"
    _default = {
        "Row ID": 1,
        "Company Name": "-",
        "Industry": "-",
        "Additional Information": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class AdditionalInformation(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    date: Series[Date] = Field(alias="Date", default=TOP_DATE)
    additional_materials: Series[str] = Field(alias="Additional Materials")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Additional Information"
    _default = {
        "Row ID": 1,
        "Date": TOP_DATE,
        "Additional Materials": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class Events(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    category: Series[str] = Field(alias="Category", isin=codelist_ref.event_categories)
    subcategory: Series[str] = Field(alias="Subcategory", isin=codelist_ref.event_subcategories)
    company_name: Series[str] = Field(alias="Company name")
    epam_clients: Series[str] = Field(alias="EPAM Clients")
    title: Series[str] = Field(alias="Title", nullable=True)
    description: Series[str] = Field(alias="Description", nullable=True)
    date: Series[Date] = Field(alias="Date", nullable=True)
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Events"
    _default = {
        "Row ID": 1,
        "Category": "-",
        "Subcategory": "-",
        "Company name": "-",
        "EPAM Clients": "-",
        "Title": "-",
        "Description": "-",
        "Date": DATE,
        "Source": "-",
        "Data as of": DATETIME,
    }


class TechStack(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    category: Series[str] = Field(alias="Category")
    technology_tools: Series[str] = Field(alias="Technology Tools")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Tech Stack"
    _default = {
        "Row ID": 1,
        "Category": "-",
        "Technology Tools": "-",
        "Data as of": DATETIME,
    }


class Projects(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    project_category: Series[str] = Field(alias="Project Category")
    company_name: Series[str] = Field(alias="Company Name")
    project_description: Series[str] = Field(alias="Project Description", nullable=True)
    project_type: Series[str] = Field(alias="Project Type")
    technology_tools: Series[str] = Field(alias="Technology Tools")
    start_date: Series[Date] = Field(alias="Start Date", nullable=True)
    end_date: Series[Date] = Field(alias="End Date", nullable=True)
    location: Series[str] = Field(alias="Location", nullable=True)
    location_id: Series[str] = Field(alias="Location ID")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Projects"
    _default = {
        "Row ID": 1,
        "Project Category": "-",
        "Company Name": "-",
        "Project Description": "-",
        "Project Type": "-",
        "Technology Tools": "-",
        "Start Date": None,
        "End Date": None,
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Source": "-",
        "Data as of": DATETIME,
    }


class OutsourcingTopMetrics(CustomDFM):
    top_metrics_breakdown: Series[str] = Field(alias="Top Metrics Breakdown", isin={"Location", "Provider"})
    location_id: Series[str] = Field(alias="Location ID")
    metrics: Series[str] = Field(alias="Metrics")
    num_of_active_workflows: Series[int] = Field(alias="# of Active Workflows", coerce=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Outsourcing Top Metrics"
    _default = {
        "Top Metrics Breakdown": "Location",
        "Metrics": "-",
        "Location ID": DEFAULT_LOC_ID,
        "# of Active Workflows": 0,
        "Data as of": DATETIME,
    }


class ITJobPostings(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    job_title: Series[str] = Field(alias="Job Title")
    skills: Series[str] = Field(alias="Skills", nullable=True)
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "IT Job Postings"
    _default = {
        "Row ID": 1,
        "Job Title": "-",
        "Skills": "-",
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Source": "-",
        "Data as of": DATETIME,
    }


class JobTitlesDemand(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    job_titles: Series[str] = Field(alias="Job Titles")
    num_of_job_postings: Series[int] = Field(alias="# of Job Postings for 12 months", coerce=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Job Titles in Demand"
    _default = {
        "Row ID": 1,
        "Job Titles": "-",
        "# of Job Postings for 12 months": 0,
        "Data as of": DATETIME,
    }


class ITWorkforceLocations(CustomDFM):
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID", unique=True)
    employees: Series[Int64] = Field(alias="Employees", default=0, coerce=True)
    one_year_growth: Series[float] = Field(alias="1y growth, %", default=0, coerce=True)
    departures: Series[Int64] = Field(alias="Departures", default=0, coerce=True)
    hires: Series[Int64] = Field(alias="Hires", default=0, coerce=True)
    ratio: Series[float] = Field(alias="Ratio", default=0, coerce=True)
    net_change: Series[Int64] = Field(alias="Net change", default=0, coerce=True)
    num_job_postings: Series[Int64] = Field(alias="# of Job Postings for 12 months", default=0, coerce=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "IT Workforce Locations"
    _default = {
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Employees": 0,
        "1y growth, %": 0,
        "Departures": 0,
        "Hires": 0,
        "Ratio": 0,
        "Net change": 0,
        "# of Job Postings for 12 months": 0,
        "Data as of": DATETIME,
    }


class ITWorkforceSkills(CustomDFM):
    skills: Series[str] = Field(alias="Skills")
    employees: Series[Int64] = Field(alias="Employees", default=0, coerce=True)
    one_year_growth: Series[float] = Field(alias="1y growth", default=0, coerce=True)
    num_job_postings: Series[Int64] = Field(alias="# of Job Postings for 12 months", default=0, coerce=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "IT Workforce Skills"
    _default = {
        "Skills": "-",
        "Employees": 0,
        "1y growth": 0,
        "# of Job Postings for 12 months": 0,
        "Data as of": DATETIME,
    }


class Competitors(CustomDFM):
    competitor_name: Series[str] = Field(alias="Competitor Name")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Competitors"
    _default = {
        "Competitor Name": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class KeyPeople(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    record_type: Series[str] = Field(alias="Record Type")
    name: Series[str] = Field(alias="Name")
    job_title: Series[str] = Field(alias="Job Title")
    job_title_id: Series[str] = Field(alias="Job Title ID")
    first_level_connections_name: Series[str] = Field(alias="1st level connections / Name")
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID")
    linkedin_url: Series[str] = Field(alias="LinkedIn URL", nullable=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Key People"
    _default = {
        "Row ID": 1,
        "Record Type": "-",
        "Name": "-",
        "Job Title": "-",
        "Job Title ID": DEFAULT_JOB_TITLE_ID,
        "1st level connections / Name": "-",
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "LinkedIn URL": "-",
        "Data as of": DATETIME,
    }


class Connections(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    record_type: Series[str] = Field(alias="Record Type")
    name: Series[str] = Field(alias="Name")
    job_title: Series[str] = Field(alias="Job Title")
    job_title_id: Series[str] = Field(alias="Job Title ID")
    first_level_connections_name: Series[str] = Field(alias="1st level connections / Name")
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID")
    linkedin_url: Series[str] = Field(alias="LinkedIn URL", nullable=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Connections"
    _default = {
        "Row ID": 1,
        "Record Type": "-",
        "Name": "-",
        "Job Title": "-",
        "Job Title ID": DEFAULT_JOB_TITLE_ID,
        "1st level connections / Name": "-",
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "LinkedIn URL": "-",
        "Data as of": DATETIME,
    }


class ManagementTeamChanges(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    name: Series[str] = Field(alias="Name")
    job_title: Series[str] = Field(alias="Job Title", nullable=True)
    movement_type: Series[str] = Field(alias="Movement Type")
    start_date: Series[Date] = Field(alias="Start Date")
    location: Series[str] = Field(alias="Location")
    location_id: Series[str] = Field(alias="Location ID")
    previous_position: Series[str] = Field(alias="Previous Position", nullable=True)
    previous_company: Series[str] = Field(alias="Previous Company", nullable=True)
    joining_company: Series[str] = Field(alias="Joining Company", nullable=True)
    linkedin_url: Series[str] = Field(alias="LinkedIn URL")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Management Team Changes"
    _default = {
        "Row ID": 1,
        "Name": "-",
        "Job Title": "-",
        "Movement Type": "-",
        "Start Date": DATE,
        "Location": "-",
        "Location ID": DEFAULT_LOC_ID,
        "Previous Position": "-",
        "Previous Company": "-",
        "Joining Company": "-",
        "LinkedIn URL": "-",
        "Data as of": DATETIME,
    }


class SWOTAnalysis(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    category: Series[str] = Field(alias="Category", isin=codelist_ref.swot_categories)
    description: Series[str] = Field(alias="Description")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "SWOT Analysis"
    _default = {
        "Row ID": 1,
        "Category": "-",
        "Description": "-",
        "Data as of": DATETIME,
    }


class InfoNgen(CustomDFM):
    description: Series[str] = Field(alias="Description")

    _sheet_name = "InfoNgen"
    _default = {
        "Description": "-",
    }


class Priorities(CustomDFM):
    row_id: Series[int] = Field(alias="Row ID")
    priority_driver: Series[str] = Field(alias="Priority / Driver")
    description: Series[str] = Field(alias="Description")
    justification: Series[str] = Field(alias="Justification")
    source: Series[str] = Field(alias="Source")
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Priorities"
    _default = {
        "Row ID": 1,
        "Priority / Driver": "-",
        "Description": "-",
        "Justification": "-",
        "Source": "-",
        "Data as of": DATETIME,
    }


class DataAvailability(CustomDFM):
    table: Series[str] = Field(alias="Table")
    status: Series[str] = Field(alias="Status of the research", nullable=True)
    data_as_of: Series[str] = Field(alias="Data as of")

    _sheet_name = "Data Availability"
    _default = {
        "Table": [
            GeneralOverview.sheet_name,
            FundingRounds.sheet_name,
            CurrentEngagements.sheet_name,
            Financials.sheet_name,
            CompanyEntities.sheet_name,
            BusinessStructure.sheet_name,
            FeaturedClients.sheet_name,
            # Events categories
            "News",
            "Focus Areas",
            "Risks and Challenges",
            "Market Positioning",
            mr.Acquisitions.sheet_name,
            mr.Partnerships.sheet_name,
            mr.Investments.sheet_name,
            mr.Mergers.sheet_name,
            # end of Events categories
            TechStack.sheet_name,
            # Projects categories
            mr.InternalProjects.sheet_name,
            mr.Outsourcing.sheet_name,
            # end of Projects categories
            OutsourcingTopMetrics.sheet_name,
            ITJobPostings.sheet_name,
            JobTitlesDemand.sheet_name,
            ITWorkforceLocations.sheet_name,
            ITWorkforceSkills.sheet_name,
            Competitors.sheet_name,
            KeyPeople.sheet_name,
            Connections.sheet_name,
            ManagementTeamChanges.sheet_name,
            AdditionalInformation.sheet_name,
            SWOTAnalysis.sheet_name,
            InfoNgen.sheet_name,
            Priorities.sheet_name,
        ],
        "Status of the research": None,
        "Data as of": DATETIME,
    }


MODELS = (
    GeneralOverview,
    FundingRounds,
    CurrentEngagements,
    Financials,
    CompanyEntities,
    BusinessStructure,
    FeaturedClients,
    Events,
    TechStack,
    Projects,
    OutsourcingTopMetrics,
    ITJobPostings,
    JobTitlesDemand,
    ITWorkforceLocations,
    ITWorkforceSkills,
    Competitors,
    KeyPeople,
    Connections,
    ManagementTeamChanges,
    AdditionalInformation,
    SWOTAnalysis,
    InfoNgen,
    Priorities,
    DataAvailability,
)
