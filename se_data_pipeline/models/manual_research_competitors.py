from pandas import Int64Dtype as Int64
from pandas import Series as pdSeries
from pandera import Date, Field, check
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM
from se_data_pipeline.component.constants import COMPANY_TYPES, TOP_YEAR


class Miscellaneous(CustomDFM):
    logo_url: Series[str] = Field(nullable=True)
    draup_report: Series[str] = Field(nullable=True)
    infongen: Series[str] = Field(nullable=True)

    _sheet_name: str = "Miscellaneous"


class GeneralOverview(CustomDFM):
    account: Series[str]
    about: Series[str]
    hq_location: Series[str]
    industry: Series[str] = Field(default="-")
    revenue_usd: Series[Int64] = Field(coerce=True, default=0)
    number_of_employees: Series[Int64] = Field(coerce=True, default=0)
    company_type: Series[str]
    website: Series[str] = Field(default="-")

    _sheet_name: str = "General Overview"

    @check("company_type")
    def valid_company_type(self, company_type: pdSeries) -> pdSeries:
        return company_type.str.upper().isin(COMPANY_TYPES)


class Fundings(CustomDFM):
    year: Series[Int64] = Field(nullable=True, coerce=True)
    transaction_name: Series[str]
    valuation_usd: Series[float] = Field(default=0, coerce=True)
    money_raised_usd: Series[float] = Field(default=0, coerce=True)
    investors: Series[str] = Field(default="-")

    _sheet_name: str = "Fundings"


class FinancialTrend(CustomDFM):
    year: Series[int] = Field(nullable=True, coerce=True)
    revenue_usd: Series[float] = Field(default=0.0, coerce=True)
    ebitda_usd: Series[float] = Field(default=0.0, coerce=True)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Financial Trend"


class CategorizedRevenue(CustomDFM):
    year: Series[int] = Field(coerce=True, nullable=True)
    financial_metrics: Series[str]
    category_of_financial_metrics: Series[str] = Field(default="-")
    revenue_usd: Series[float] = Field(default=0.0, coerce=True)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Categorized Revenue"


class Strategy(CustomDFM):
    category: Series[str]
    title: Series[str]
    description: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Strategy"


class OfferingsByIndustry(CustomDFM):
    category: Series[str]
    solutions: Series[str]
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Offerings By Industry"


class OfferingsByServices(CustomDFM):
    service: Series[str]
    product: Series[str]
    solution: Series[str]
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Offerings By Services"


class Partnerships(CustomDFM):
    company_name: Series[str]
    additional_information: Series[str] = Field(default="-")
    year: Series[int] = Field(coerce=True, default=TOP_YEAR)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Partnerships"


class Acquisitions(CustomDFM):
    company_name: Series[str]
    additional_information: Series[str] = Field(default="-")
    year: Series[int] = Field(coerce=True, default=TOP_YEAR)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Acquisitions"


class Investments(CustomDFM):
    company_name: Series[str] = Field(default="-")
    additional_information: Series[str] = Field(default="-")
    year: Series[Int64] = Field(coerce=True, default=TOP_YEAR)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Investments"


class Mergers(CustomDFM):
    company_name: Series[str]
    additional_information: Series[str] = Field(default="-")
    year: Series[Int64] = Field(coerce=True, default=TOP_YEAR)
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Mergers"


class Clients(CustomDFM):
    company_name: Series[str]
    epam_status: Series[str] = Field(default="-")
    additional_information: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Clients"


class JobPostings(CustomDFM):
    job_title: Series[str]
    skills: Series[str] = Field(default="-")
    location: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Job Postings"


class AwardsAndRecognitions(CustomDFM):
    year: Series[Int64] = Field(coerce=True, default=TOP_YEAR)
    awards_recognitions: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Awards & Recognitions"


class AdditionalInformation(CustomDFM):
    date: Series[Date] = Field(nullable=True)
    additional_materials: Series[str] = Field(default="-")
    source: Series[str] = Field(default="-")

    _sheet_name: str = "Additional Information"


MODELS = (
    Miscellaneous,
    GeneralOverview,
    Fundings,
    FinancialTrend,
    CategorizedRevenue,
    Strategy,
    OfferingsByIndustry,
    OfferingsByServices,
    Partnerships,
    Acquisitions,
    Investments,
    Mergers,
    Clients,
    JobPostings,
    AwardsAndRecognitions,
    AdditionalInformation,
)
