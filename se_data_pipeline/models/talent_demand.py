from pandas import Int64Dtype as Int64
from pandera import Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM


class JobTitles(CustomDFM):
    job_title: Series[str]
    num_job_postings: Series[Int64] = Field(default=0, coerce=True)

    _sheet_name = "Job Titles"


class Locations(CustomDFM):
    location: Series[str]
    num_job_postings: Series[Int64] = Field(default=0, coerce=True)

    _sheet_name = "Locations"


class CoreSkills(CustomDFM):
    core_skill: Series[str]
    num_job_postings: Series[Int64] = Field(default=0, coerce=True)

    _sheet_name = "Core Skills"


MODELS = (JobTitles, Locations, CoreSkills)
