from pandas import Int64Dtype as Int64
from pandera import Field
from pandera.typing import Series

from se_data_pipeline.component.base import CustomDFM


class Location(CustomDFM):
    location: Series[str]
    num_professionals_last_year: Series[Int64] = Field(coerce=True, default=0)
    num_professionals_this_year: Series[Int64] = Field(coerce=True, default=0)
    percent_of_total: Series[float] = Field(coerce=True, default=0)
    avg_salary_usd: Series[str] = Field(coerce=True, default="-")

    _sheet_name = "Location"


class TalentMoveLocations(CustomDFM):
    location: Series[str] = Field(default="-")
    number_inflow: Series[Int64] = Field(coerce=True, default=0)
    number_outflow: Series[Int64] = Field(coerce=True, default=0)

    _sheet_name = "Talent move_by_locations"


class Skills(CustomDFM):
    skills: Series[str]
    num_professionals_last_year: Series[Int64] = Field(coerce=True, default=0)
    num_professionals_this_year: Series[Int64] = Field(coerce=True, default=0)
    percent_of_total: Series[float] = Field(coerce=True, default=0)

    _sheet_name = "Skills"


MODELS = (Location, TalentMoveLocations, Skills)
