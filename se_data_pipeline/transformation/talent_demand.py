import re
from typing import Dict, <PERSON><PERSON>

import pandas as pd
from pandera import check_types
from pandera.typing import DataFrame

from se_data_pipeline.component.base import TransformationMixin, Transformer
from se_data_pipeline.component.constants import NOT_RESEARCHED, RESEARCHED
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import sales_research_competitors as src


# pylint: disable=no-member
class TalentDemandTransformer(Transformer, TransformationMixin):
    """Transforms Talent Demand data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        manual_merge_rules: DataFrame,
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes TalentDemandTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping Talent Demand sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.manual_merge_rules = manual_merge_rules
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)
        self.transformations = (
            self.compose_job_titles_demand,
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the Talent Demand data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                "General Overview",
                "Financials",
                "Projects",
                "Outsourcing Top Metrics",
                "IT Job Postings",
                "IT Workforce Locations",
                "Key People",
                "Management Team Changes",
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[sr.DataAvailability.table] == category_name
        status_not_set = self.status_table[sr.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, sr.DataAvailability.status] = status
        return None

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Titles Demand DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.JobTitlesDemand.default_data
        sheet_name = sr.JobTitlesDemand.sheet_name
        job_titles_demand = self.sheet_df_map.get("Job Titles")

        if job_titles_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jtd = data.copy()
        data_jtd["Row ID"] = range(1, len(job_titles_demand) + 1)
        data_jtd["Job Titles"] = job_titles_demand.job_title
        data_jtd["# of Job Postings for 12 months"] = job_titles_demand.num_job_postings
        df = pd.DataFrame(data_jtd)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceLocations.default_data
        sheet_name = sr.ITWorkforceLocations.sheet_name
        hiring_map = self.sheet_df_map.get("Locations")

        if hiring_map is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwf = data.copy()
        data_iwf["Location"] = hiring_map.location
        data_iwf["Location ID"] = hiring_map.location
        data_iwf["# of Job Postings for 12 months"] = hiring_map.num_job_postings
        df = pd.DataFrame(data_iwf)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceSkills.default_data
        sheet_name = sr.ITWorkforceSkills.sheet_name
        core_skills_demand = self.sheet_df_map.get("Core Skills")
        if core_skills_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iws = data.copy()
        data_iws["Skills"] = core_skills_demand.core_skill
        data_iws["# of Job Postings for 12 months"] = core_skills_demand.num_job_postings
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return sr.DataAvailability.sheet_name, self.status_table


class TalentDemandCompetitorsTransformer(Transformer, TransformationMixin):
    """Transforms Talent Demand Competitors data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes TalentDemandCompetitorsTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping Talent Demand Competitors sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)
        self.transformations = (
            self.compose_job_titles_demand,
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the Talent Demand Competitors data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                "General Overview",
                "Financials",
                "Projects",
                "Outsourcing Top Metrics",
                "IT Job Postings",
                "IT Workforce Locations",
                "Locations",
                "Key People",
                "Management Team Changes",
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[src.DataAvailability.table] == category_name
        status_not_set = self.status_table[src.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, src.DataAvailability.status] = status
        return None

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Titles in Demand DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.JobTitlesInDemand.default_data
        sheet_name = src.JobTitlesInDemand.sheet_name
        job_titles_demand = self.sheet_df_map.get("Job Titles")

        if job_titles_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jtd = data.copy()
        data_jtd["Row ID"] = range(1, len(job_titles_demand) + 1)
        data_jtd["Title"] = job_titles_demand.job_title
        data_jtd["Job Posts"] = job_titles_demand.num_job_postings
        df = pd.DataFrame(data_jtd)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceByLocation.default_data
        sheet_name = src.WorkforceByLocation.sheet_name
        hiring_map = self.sheet_df_map.get("Locations")

        if hiring_map is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwf = data.copy()
        data_iwf["Location"] = hiring_map.location
        data_iwf["Location ID"] = hiring_map.location
        data_iwf["Job Posts"] = hiring_map.num_job_postings
        df = pd.DataFrame(data_iwf)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceBySkills.default_data
        sheet_name = src.WorkforceBySkills.sheet_name
        core_skills_demand = self.sheet_df_map.get("Core Skills")
        if core_skills_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iws = data.copy()
        data_iws["Skills"] = core_skills_demand.core_skill
        data_iws["Job Posts"] = core_skills_demand.num_job_postings
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return src.DataAvailability.sheet_name, self.status_table
