from typing import Dict, <PERSON><PERSON>

import pandas as pd
from pandera import check_types
from pandera.typing import Data<PERSON>rame

from se_data_pipeline.component.base import TransformationMixin, Transformer
from se_data_pipeline.component.constants import (
    DEFAULT_LOC_ID,
    NO_INFO,
    NOT_RESEARCHED,
    RESEARCHED,
    MergingLogicType,
)
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.models import manual_research_competitors as mrc
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import sales_research_competitors as src


class ManualResearchTransformer(Transformer, TransformationMixin):
    """Transforms manual research data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        manual_merge_rules: DataFrame,
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes ManualResearchTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping manual research sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location and job title processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.manual_merge_rules = manual_merge_rules
        self.status_table = self.sheet_df_map[sr.DataAvailability.sheet_name]
        self.transformations = (
            self.compose_general_overview,
            self.compose_funding_rounds,
            self.compose_current_engagements,
            self.compose_financials,
            self.compose_company_entities,
            self.compose_business_structure,
            self.compose_featured_clients,
            self.compose_events,
            self.compose_tech_stack,
            self.compose_projects,
            self.compose_outsourcing_top_metrics,
            self.compose_job_postings,
            self.compose_job_titles_demand,
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_competitors,
            self.compose_key_people,
            self.compose_connections,
            self.compose_management_team_changes,
            self.compose_additional_information,
            self.compose_swot_analysis,
            self.compose_infongen,
            self.compose_priorities,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the manual research data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                sr.GeneralOverview.sheet_name,
                sr.Projects.sheet_name,
                sr.Financials.sheet_name,
                sr.OutsourcingTopMetrics.sheet_name,
                sr.ITJobPostings.sheet_name,
                sr.ITWorkforceLocations.sheet_name,
                sr.KeyPeople.sheet_name,
                sr.ManagementTeamChanges.sheet_name,
                sr.Connections.sheet_name,
            },
        )
        sheet_df_map = self._replace_job_title_ids(
            sheet_df_map,
            sheets={sr.KeyPeople.sheet_name, sr.Connections.sheet_name},
        )
        return sheet_df_map

    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[sr.DataAvailability.table] == category_name
        status_not_set = self.status_table[sr.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, sr.DataAvailability.status] = status
        return None

    def should_be_merged(self, sheet_name: str) -> bool:
        # Find configuration for this sheet
        sheet_config = self.manual_merge_rules[self.manual_merge_rules.table.str.contains(sheet_name, na=False)]

        # Get the merging way value
        merging_way = sheet_config["merging_way"].iloc[0]

        # Handle empty/null merging way - default to merge
        if not merging_way or pd.isna(merging_way):
            return True

        # Don't merge if explicitly set to DRAUP_ONLY
        return merging_way != MergingLogicType.DRAUP_ONLY.value

    @check_types
    def compose_general_overview(self) -> Tuple[str, pd.DataFrame]:
        """Composes the General Overview DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the General Overview DataFrame.
        Raises:
            ValueError: If the "General Overview" sheet is empty.
        """
        # composing output dataframe
        data = sr.GeneralOverview.default_data
        sheet_name = sr.GeneralOverview.sheet_name
        general_overview = self.sheet_df_map.get(mr.GeneralOverview.sheet_name)

        if general_overview is None or general_overview.empty:
            raise ValueError("General Overview sheet is empty")

        data["Account"] = general_overview.account.iloc[0]
        data["About"] = general_overview.about.iloc[0]
        data["HQ Location"] = general_overview.hq_location.iloc[0]
        data["Location ID"] = general_overview.hq_location.iloc[0]
        data["Parent Company"] = general_overview.parent_company.iloc[0]
        data["Industry"] = general_overview.industry.iloc[0]
        data["Number of Employees"] = general_overview.number_of_employees.iloc[0]
        data["Company Type"] = general_overview.company_type.iloc[0]
        data["Website"] = general_overview.website.iloc[0]
        data["Logo URL"] = general_overview.logo_url.iloc[0]
        data["Revenue"] = general_overview.revenue.iloc[0]
        data["IT Spend"] = general_overview.it_spend.iloc[0]
        data["Currency Code"] = general_overview.currency_code.iloc[0]

        sheet_name = sr.GeneralOverview.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_funding_rounds(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Funding Rounds DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Funding Rounds DataFrame.
        """
        data = sr.FundingRounds.default_data
        sheet_name = sr.FundingRounds.sheet_name
        funding_rounds = self.sheet_df_map.get(mr.FundingRounds.sheet_name)

        if funding_rounds is None or funding_rounds.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "investors": ",",
        }
        funding_rounds = self._expand_column_values(funding_rounds, column_delimiters)

        data_fr = data.copy()
        data_fr["Row ID"] = range(1, len(funding_rounds) + 1)
        data_fr["Year"] = funding_rounds.year
        data_fr["Transaction Name"] = funding_rounds.transaction_name
        data_fr["Valuation"] = funding_rounds.valuation
        data_fr["Money Raised"] = funding_rounds.money_raised
        data_fr["Currency Code"] = funding_rounds.currency_code
        data_fr["Investors"] = funding_rounds.investors
        df = pd.DataFrame(data_fr)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_current_engagements(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Current Engagements DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Current Engagements DataFrame.
        """
        sheet_name = sr.CurrentEngagements.sheet_name
        data = sr.CurrentEngagements.default_data
        engagements = self.sheet_df_map.get(mr.CurrentEngagements.sheet_name)

        if engagements is None or engagements.empty:
            general_overview = self.sheet_df_map[mr.GeneralOverview.sheet_name]
            data["Company name"] = general_overview.account.iloc[0]
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ce = data.copy()
        data_ce["Company name"] = engagements.company_name
        data_ce["Customer code"] = engagements.customer_code
        data_ce["Status"] = engagements.status
        data_ce["GBU"] = engagements.gbu
        data_ce["Account Manager"] = engagements.account_manager
        data_ce["Sales Manager"] = engagements.sales_manager
        data_ce["Telescope Link"] = engagements.telescope_link
        data_ce["CRM Link"] = engagements.crm_link

        df = pd.DataFrame(data_ce)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_financials(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Financials DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Financials DataFrame.
        """
        fin_trend = self.sheet_df_map.get(mr.FinancialTrend.sheet_name)
        revenue_category = self.sheet_df_map.get(mr.CategorizedRevenue.sheet_name)
        sheet_name = sr.Financials.sheet_name
        data = sr.Financials.default_data

        if all(df is None or df.empty for df in (fin_trend, revenue_category)):
            # no financial data available, default values
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if fin_trend is not None and not fin_trend.empty and self.should_be_merged(mr.FinancialTrend.sheet_name):
            data_ft = data.copy()
            data_ft["Year"] = fin_trend.year
            data_ft["Financial Metrics"] = "Yearly Total"
            data_ft["Revenue"] = fin_trend.revenue
            data_ft["IT Spend"] = fin_trend.it_spend
            data_ft["EBITDA"] = fin_trend.ebitda
            data_ft["Currency Code"] = fin_trend.currency_code
            data_ft["Source"] = fin_trend.source
            transformed_sheets.append(pd.DataFrame(data_ft))

        if (
            revenue_category is not None
            and not revenue_category.empty
            and self.should_be_merged(mr.CategorizedRevenue.sheet_name)
        ):
            data_rc = data.copy()
            data_rc["Year"] = revenue_category.year
            data_rc["Financial Metrics"] = revenue_category.financial_metrics
            data_rc["Category of Financial Metrics"] = revenue_category.category_of_financial_metrics
            data_rc["Revenue"] = revenue_category.revenue
            data_rc["Currency Code"] = revenue_category.currency_code
            data_rc["Source"] = revenue_category.source
            categories = pd.DataFrame(data_rc)
            regions = categories["Financial Metrics"] == "Revenue by Region"
            categories.loc[regions, "Location ID"] = categories["Category of Financial Metrics"]
            transformed_sheets.append(categories)

        if transformed_sheets:
            df = pd.concat(transformed_sheets).reset_index(drop=True)
            self.set_table_status(sheet_name, RESEARCHED)
            return sheet_name, df
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

    @check_types
    def compose_company_entities(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Company Entities dataframe."""
        data = sr.CompanyEntities.default_data
        sheet_name = sr.CompanyEntities.sheet_name
        company_entities = self.sheet_df_map.get(mr.CompanyEntities.sheet_name)

        if company_entities is None or company_entities.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ce = data.copy()

        data_ce["Row ID"] = range(1, len(company_entities) + 1)
        data_ce["Subsidiary Name"] = company_entities.subsidiary_name
        data_ce["Description"] = company_entities.description
        data_ce["Website"] = company_entities.website
        data_ce["LinkedIn URL"] = company_entities.linkedin_url
        data_ce["Status"] = company_entities.status
        data_ce["Sales Manager"] = company_entities.sales_manager
        data_ce["Account Manager"] = company_entities.account_manager
        data_ce["CRM Link"] = company_entities.crm_link
        data_ce["Link to the Report"] = company_entities.link_to_the_report
        data_ce["Comments"] = company_entities.comments

        df = pd.DataFrame(data_ce)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_business_structure(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Business Structure DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Business Structure DataFrame.
        """
        data = sr.BusinessStructure.default_data
        sheet_name = sr.BusinessStructure.sheet_name
        business_structure = self.sheet_df_map.get(mr.BusinessStructure.sheet_name)

        if business_structure is None or business_structure.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_bs = data.copy()
        data_bs["Row ID"] = range(1, len(business_structure) + 1)
        data_bs["Business Unit"] = business_structure.business_unit
        data_bs["Products and Services"] = business_structure.products_and_services
        data_bs["Additional Information"] = business_structure.additional_information
        data_bs["Source"] = business_structure.source

        df = pd.DataFrame(data_bs)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_featured_clients(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Featured Clients DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Featured Clients DataFrame.
        """
        data = sr.FeaturedClients.default_data
        sheet_name = sr.FeaturedClients.sheet_name
        featured_clients = self.sheet_df_map.get(sheet_name)

        if featured_clients is None or featured_clients.empty or not self.should_be_merged(sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_fc = data.copy()
        data_fc["Company Name"] = featured_clients.company_name
        data_fc["Industry"] = featured_clients.industry
        data_fc["Additional Information"] = featured_clients.additional_information
        data_fc["Source"] = featured_clients.source

        df = pd.DataFrame(data_fc)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_events(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Events DataFrame from various manual research event-related sheets.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Events DataFrame.
        """
        data = sr.Events.default_data
        sheet_name = sr.Events.sheet_name
        strat_risks = self.sheet_df_map.get(mr.StrategyRisksInitiatives.sheet_name)
        partnerships = self.sheet_df_map.get(mr.Partnerships.sheet_name)
        acquisitions = self.sheet_df_map.get(mr.Acquisitions.sheet_name)
        mergers = self.sheet_df_map.get(mr.Mergers.sheet_name)
        investments = self.sheet_df_map.get(mr.Investments.sheet_name)

        categories = [
            "Focus Areas",
            "Risks and Challenges",
            "Market Positioning",
            mr.Acquisitions.sheet_name,
            mr.Partnerships.sheet_name,
            mr.Investments.sheet_name,
            mr.Mergers.sheet_name,
        ]

        if all(df is None or df.empty for df in (strat_risks, partnerships, acquisitions, mergers, investments)):
            # no events available, default values
            df = pd.DataFrame(data, index=[0])
            for category in categories:
                self.set_table_status(category, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if strat_risks is not None and not strat_risks.empty:
            data_sr = data.copy()
            data_sr["Category"] = "Strategy, Risks and Initiatives"
            data_sr["Subcategory"] = strat_risks.category
            data_sr["Title"] = strat_risks.title
            data_sr["Description"] = strat_risks.description
            data_sr["Source"] = strat_risks.source
            transformed_sheets.append(pd.DataFrame(data_sr))

        if partnerships is not None and not partnerships.empty:
            data_p = data.copy()
            data_p["Category"] = "Partnerships & Acquisitions"
            data_p["Subcategory"] = "Partnership"
            data_p["Company name"] = partnerships.company_name
            data_p["Description"] = partnerships.description
            data_p["Date"] = partnerships.date
            data_p["Source"] = partnerships.source
            transformed_sheets.append(pd.DataFrame(data_p))

        if acquisitions is not None and not acquisitions.empty:
            data_a = data.copy()
            data_a["Category"] = "Partnerships & Acquisitions"
            data_a["Subcategory"] = "Acquisition"
            data_a["Company name"] = acquisitions.company_name
            data_a["Description"] = acquisitions.description
            data_a["EPAM Clients"] = acquisitions.epam_clients
            data_a["Date"] = acquisitions.date
            data_a["Source"] = acquisitions.source
            transformed_sheets.append(pd.DataFrame(data_a))

        if mergers is not None and not mergers.empty:
            data_m = data.copy()
            data_m["Category"] = "Partnerships & Acquisitions"
            data_m["Subcategory"] = "Merger"
            data_m["Company name"] = mergers.company_name
            data_m["Description"] = mergers.description
            data_m["EPAM Clients"] = mergers.epam_clients
            data_m["Date"] = mergers.date
            data_m["Source"] = mergers.source
            transformed_sheets.append(pd.DataFrame(data_m))

        if investments is not None and not investments.empty:
            data_i = data.copy()
            data_i["Category"] = "Partnerships & Acquisitions"
            data_i["Subcategory"] = "Investment"
            data_i["Company name"] = investments.company_name
            data_i["Description"] = investments.description
            data_i["EPAM Clients"] = investments.epam_clients
            data_i["Date"] = investments.date
            data_i["Source"] = investments.source
            transformed_sheets.append(pd.DataFrame(data_i))

        df = pd.concat(transformed_sheets, ignore_index=True)
        df = df.sort_values(by=["Category", "Subcategory"])
        df["Row ID"] = range(1, len(df) + 1)

        s_categories = set(df["Subcategory"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                # absence of data in manual research means no info available
                self.set_table_status(category, NO_INFO)
        # explicitly defined status for News
        self.set_table_status("News", NOT_RESEARCHED)

        return sheet_name, df

    @check_types
    def compose_tech_stack(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Tech Stack DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Tech Stack DataFrame.
        """
        data = sr.TechStack.default_data
        sheet_name = sr.TechStack.sheet_name
        tech_stack = self.sheet_df_map.get(mr.TechStack.sheet_name)

        if tech_stack is None or tech_stack.empty or not self.should_be_merged(sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "technology_tools": ",",
        }
        tech_stack = self._expand_column_values(tech_stack, column_delimiters)
        tech_stack = tech_stack[~tech_stack["technology_tools"].duplicated()]

        data_ts = data.copy()
        data_ts["Row ID"] = list(range(1, len(tech_stack) + 1))
        data_ts["Category"] = tech_stack.category
        data_ts["Technology Tools"] = tech_stack.technology_tools
        df = pd.DataFrame(data_ts)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_projects(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Projects DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Projects DataFrame.
        """
        data = sr.Projects.default_data
        sheet_name = sr.Projects.sheet_name
        projects = self.sheet_df_map.get(mr.InternalProjects.sheet_name)
        outsourcing = self.sheet_df_map.get(mr.Outsourcing.sheet_name)

        categories = [
            mr.InternalProjects.sheet_name,
            mr.Outsourcing.sheet_name,
        ]

        if all(df is None or df.empty for df in (projects, outsourcing)):
            df = pd.DataFrame(data, index=[0])
            for category in categories:
                self.set_table_status(category, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "project_type": ",",
            "technology_tools": ",",
        }
        projects = self._expand_column_values(projects, column_delimiters)
        outsourcing = self._expand_column_values(outsourcing, column_delimiters)

        transformed_sheets = []
        if projects is not None and not projects.empty:
            data_p = data.copy()
            data_p["Project Category"] = "Internal Project"
            data_p["Project Description"] = projects.project_description
            data_p["Project Type"] = projects.project_type
            data_p["Technology Tools"] = projects.technology_tools
            data_p["Start Date"] = projects.start_date
            data_p["End Date"] = projects.end_date
            data_p["Location"] = projects.location
            data_p["Location ID"] = projects.location
            data_p["Source"] = projects.source
            transformed_sheets.append(pd.DataFrame(data_p))

        if outsourcing is not None and not outsourcing.empty and self.should_be_merged(mr.Outsourcing.sheet_name):
            data_o = data.copy()
            data_o["Project Category"] = "Outsourcing"
            data_o["Project Description"] = outsourcing.project_description
            data_o["Project Type"] = outsourcing.project_type
            data_o["Technology Tools"] = outsourcing.technology_tools
            data_o["Company Name"] = outsourcing.company_name
            data_o["Start Date"] = outsourcing.start_date
            data_o["End Date"] = outsourcing.end_date
            data_o["Location"] = outsourcing.location
            data_o["Location ID"] = outsourcing.location
            data_o["Source"] = outsourcing.source
            transformed_sheets.append(pd.DataFrame(data_o))

        df = pd.concat(transformed_sheets, ignore_index=True)
        df["Row ID"] = range(1, len(df) + 1)

        s_categories = set(df["Project Category"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                self.set_table_status(category, NOT_RESEARCHED)

        return sheet_name, df

    @check_types
    def compose_outsourcing_top_metrics(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Outsourcing Top Metrics DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Outsourcing Top Metrics DataFrame.
        """
        data = sr.OutsourcingTopMetrics.default_data
        sheet_name = sr.OutsourcingTopMetrics.sheet_name
        outsourcing_providers = self.sheet_df_map.get(mr.Outsourcing.sheet_name)

        if outsourcing_providers is None or outsourcing_providers.empty:
            df = pd.DataFrame([data])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        providers = outsourcing_providers["company_name"].value_counts().reset_index()
        providers.columns = ["company_name", "counts"]

        locations = pd.DataFrame(outsourcing_providers["location"])
        locations = locations[~locations["location"].str.contains("Not Available|-", case=False, na=False)]
        locations[sr.OutsourcingTopMetrics.location_id] = locations["location"]
        locations = self._replace_location_ids({sheet_name: locations}, {sheet_name})[sheet_name]

        location_counts = locations[sr.OutsourcingTopMetrics.location_id].value_counts().reset_index()

        locations = pd.merge(
            location_counts,
            locations,
            on=sr.OutsourcingTopMetrics.location_id,
            how="left",
        )
        locations.drop_duplicates(subset=[sr.OutsourcingTopMetrics.location_id], keep="first", inplace=True)
        locations = locations.sort_values(by="count", ascending=False).reset_index(drop=True)
        locations.rename(columns={"location": "location_name", "count": "counts"}, inplace=True)

        locations = locations[locations["counts"] > 1]
        providers = providers[providers["counts"] > 1]

        locations = locations.head(3)
        providers = providers.head(3)

        if locations.empty and providers.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NO_INFO)
            return sheet_name, df

        data_tom = data.copy()
        data_tom["Top Metrics Breakdown"] = ["Location"] * len(locations) + ["Provider"] * len(providers)
        data_tom["Metrics"] = locations.location_name.to_list() + providers.company_name.to_list()
        data_tom["Location ID"] = locations.location_name.to_list() + [DEFAULT_LOC_ID] * len(providers)
        data_tom["# of Active Workflows"] = locations.counts.to_list() + providers.counts.to_list()

        df = pd.DataFrame(data_tom)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_postings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Postings DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Job Postings DataFrame.
        """
        data = sr.ITJobPostings.default_data
        sheet_name = sr.ITJobPostings.sheet_name
        job_postings = self.sheet_df_map.get(mr.ITJobPostings.sheet_name)

        if job_postings is None or job_postings.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jp = data.copy()
        column_delimiters = {
            "skills": ",",
            "location": ";",
        }
        job_postings = self._expand_column_values(job_postings, column_delimiters)
        data_jp["Row ID"] = range(1, len(job_postings) + 1)
        data_jp["Job Title"] = job_postings.job_title
        data_jp["Skills"] = job_postings.skills
        data_jp["Location"] = job_postings.location
        data_jp["Location ID"] = job_postings.location
        data_jp["Source"] = job_postings.source
        df = pd.DataFrame(data_jp)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Job Titles Demand DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = sr.JobTitlesDemand.default_data
        sheet_name = sr.JobTitlesDemand.sheet_name
        df = pd.DataFrame(data, index=[0])
        return sheet_name, df

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default IT Workforce Locations DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = sr.ITWorkforceLocations.default_data
        sheet_name = sr.ITWorkforceLocations.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default IT Workforce Skills DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = sr.ITWorkforceSkills.default_data
        sheet_name = sr.ITWorkforceSkills.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_competitors(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Competitors DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Competitors DataFrame.
        """
        data = sr.Competitors.default_data
        sheet_name = sr.Competitors.sheet_name
        competitors = self.sheet_df_map.get(mr.Competitors.sheet_name)

        if competitors is None or competitors.empty and not self.should_be_merged(sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_c = data.copy()
        data_c["Competitor Name"] = competitors.competitor_name
        data_c["Source"] = competitors.source
        df = pd.DataFrame(data_c)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_key_people(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Key People dataframe."""
        data = sr.KeyPeople.default_data
        sheet_name = sr.KeyPeople.sheet_name
        key_people = self.sheet_df_map.get(mr.KeyPeople.sheet_name)

        if key_people is None or key_people.empty or not self.should_be_merged(sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(mr.KeyPeople.sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        # Transform Key People data
        data_kp = data.copy()
        data_kp["Record Type"] = "Key People"
        data_kp["Name"] = key_people.name
        data_kp["Job Title"] = key_people.job_title
        data_kp["Job Title ID"] = key_people.job_title
        data_kp["Location"] = key_people.location
        data_kp["Location ID"] = key_people.location
        data_kp["LinkedIn URL"] = key_people.linkedin_url

        df = pd.DataFrame(data_kp)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(mr.KeyPeople.sheet_name, RESEARCHED)

        return sheet_name, df

    @check_types
    def compose_connections(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Connections dataframe."""
        data = sr.Connections.default_data
        sheet_name = sr.Connections.sheet_name
        connections = self.sheet_df_map.get(mr.Connections.sheet_name)

        if connections is None or connections.empty or not self.should_be_merged(sr.KeyPeople.sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(mr.Connections.sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        # Transform Connections data
        data_c = data.copy()
        data_c["Record Type"] = "Connections"
        data_c["Name"] = connections.name
        data_c["Job Title"] = connections.job_title
        data_c["Job Title ID"] = connections.job_title
        data_c["1st level connections / Name"] = connections.first_level_connections
        data_c["Location"] = connections.location
        data_c["Location ID"] = connections.location
        data_c["LinkedIn URL"] = connections.linkedin_url

        df = pd.DataFrame(data_c)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(mr.Connections.sheet_name, RESEARCHED)

        return sheet_name, df

    @check_types
    def compose_management_team_changes(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Management Team Changes DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Management Team Changes DataFrame.
        """
        data = sr.ManagementTeamChanges.default_data
        sheet_name = sr.ManagementTeamChanges.sheet_name
        m_exits = self.sheet_df_map.get(mr.ManagementTeamExit.sheet_name)
        m_hires = self.sheet_df_map.get(mr.ManagementTeamHire.sheet_name)
        m_promotions = self.sheet_df_map.get(mr.ManagementTeamPromo.sheet_name)

        if all(df is None or df.empty for df in (m_exits, m_hires, m_promotions)):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if m_exits is not None and not m_exits.empty and self.should_be_merged(mr.ManagementTeamExit.sheet_name):
            data_me = data.copy()
            data_me["Name"] = m_exits.name
            data_me["Job Title"] = m_exits.job_title_after_exit
            data_me["Movement Type"] = "Exit"
            data_me["Start Date"] = m_exits.exit_date
            data_me["Location"] = m_exits.location
            data_me["Location ID"] = m_exits.location
            data_me["Previous Position"] = m_exits.previous_position
            data_me["Joining Company"] = m_exits.joining_company
            data_me["LinkedIn URL"] = m_exits.linkedin_url
            transformed_sheets.append(pd.DataFrame(data_me))

        if m_hires is not None and not m_hires.empty and self.should_be_merged(mr.ManagementTeamHire.sheet_name):
            data_mh = data.copy()
            data_mh["Name"] = m_hires.name
            data_mh["Job Title"] = m_hires.job_title_after_hire
            data_mh["Movement Type"] = "Hire"
            data_mh["Start Date"] = m_hires.hire_date
            data_mh["Location"] = m_hires.location
            data_mh["Location ID"] = m_hires.location
            data_mh["Previous Position"] = m_hires.previous_position
            data_mh["Previous Company"] = m_hires.previous_company
            data_mh["LinkedIn URL"] = m_hires.linkedin_url
            transformed_sheets.append(pd.DataFrame(data_mh))

        if (
            m_promotions is not None
            and not m_promotions.empty
            and self.should_be_merged(mr.ManagementTeamPromo.sheet_name)
        ):
            data_mp = data.copy()
            data_mp["Name"] = m_promotions.name
            data_mp["Job Title"] = m_promotions.job_title_after_promo
            data_mp["Movement Type"] = "Promotion"
            data_mp["Start Date"] = m_promotions.promo_date
            data_mp["Location"] = m_promotions.location
            data_mp["Location ID"] = m_promotions.location
            data_mp["Previous Position"] = m_promotions.previous_position
            data_mp["LinkedIn URL"] = m_promotions.linkedin_url
            transformed_sheets.append(pd.DataFrame(data_mp))

        if transformed_sheets:
            df = pd.concat(transformed_sheets).reset_index(drop=True)
            df["Row ID"] = range(1, len(df) + 1)
            self.set_table_status(sheet_name, RESEARCHED)
            return sheet_name, df
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

    @check_types
    def compose_additional_information(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Additional Information DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Additional Information DataFrame.
        """
        data = sr.AdditionalInformation.default_data
        sheet_name = sr.AdditionalInformation.sheet_name
        ad_info = self.sheet_df_map.get(mr.AdditionalInformation.sheet_name)

        if ad_info is None or ad_info.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ai = data.copy()
        data_ai["Row ID"] = range(1, len(ad_info) + 1)
        data_ai["Date"] = ad_info.date
        data_ai["Additional Materials"] = ad_info.additional_materials
        data_ai["Source"] = ad_info.source

        df = pd.DataFrame(data_ai)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_swot_analysis(self) -> Tuple[str, pd.DataFrame]:
        """Composes the SWOT Analysis DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the SWOT Analysis DataFrame.
        """
        data = sr.SWOTAnalysis.default_data
        sheet_name = sr.SWOTAnalysis.sheet_name
        swot = self.sheet_df_map.get(mr.SWOTAnalysis.sheet_name)

        if swot is None or swot.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_sa = data.copy()
        data_sa["Row ID"] = range(1, len(swot) + 1)
        data_sa["Category"] = swot.category
        data_sa["Description"] = swot.description

        df = pd.DataFrame(data_sa)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_infongen(self) -> Tuple[str, pd.DataFrame]:
        """Composes the InfoNgen DataFrame from manual research data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the InfoNgen DataFrame.
        """
        data = sr.InfoNgen.default_data
        sheet_name = sr.InfoNgen.sheet_name
        infongen = self.sheet_df_map.get(mr.InfoNgen.sheet_name)

        if infongen is None or infongen.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ig = data.copy()
        data_ig["Description"] = infongen.description

        df = pd.DataFrame(data_ig)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_priorities(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Priorities DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Priorities DataFrame.
        """
        data = sr.Priorities.default_data
        sheet_name = sr.Priorities.sheet_name
        priorities = self.sheet_df_map.get(sheet_name)

        if priorities is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data["Row ID"] = priorities.row_id
        data["Priority / Driver"] = priorities.priority_driver
        data["Description"] = priorities.description
        data["Justification"] = priorities.justification
        data["Source"] = priorities.source
        df = pd.DataFrame(data)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return sr.DataAvailability.sheet_name, self.status_table


class ManualResearchCompetitorsTransformer(Transformer, TransformationMixin):
    """Transforms manual research competitor data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes ManualResearchCompetitorsTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping manual research competitor sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = self.sheet_df_map[src.DataAvailability.sheet_name]
        self.transformations = (
            self.compose_general_overview,
            self.compose_fundings,
            self.compose_financials,
            self.compose_strategy,
            self.compose_offerings,
            self.compose_relationships,
            self.compose_clients,
            self.compose_job_postings,
            self.compose_workforce_by_locations,
            self.compose_workforce_by_skills,
            self.compose_job_titles_demand,
            self.compose_awards_and_recognitions,
            self.compose_additional_information,
            self.compose_infongen,
            self.compose_data_availability,
            self.compose_miscellaneous,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the manual research competitor data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                src.GeneralOverview.sheet_name,
                src.JobPostings.sheet_name,
                src.WorkforceByLocation.sheet_name,
            },
        )
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[src.DataAvailability.table] == category_name
        status_not_set = self.status_table[src.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, src.DataAvailability.status] = status
        return None

    @check_types
    def compose_general_overview(self) -> Tuple[str, pd.DataFrame]:
        """Composes the General Overview DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the General Overview DataFrame.
        """
        # composing output dataframe
        data = src.GeneralOverview.default_data
        sheet_name = src.GeneralOverview.sheet_name
        general_overview = self.sheet_df_map.get(sheet_name)

        if general_overview is None or general_overview.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data["Account"] = general_overview.account.iloc[0]
        data["About"] = general_overview.about.iloc[0]
        data["HQ Location"] = general_overview.hq_location.iloc[0]
        data["Location ID"] = general_overview.hq_location.iloc[0]
        data["Industry"] = general_overview.industry.iloc[0]
        data["Revenue (USD)"] = general_overview.revenue_usd.iloc[0]
        data["Number of Employees"] = general_overview.number_of_employees.iloc[0]
        data["Company Type"] = general_overview.company_type.iloc[0]
        data["Website"] = general_overview.website.iloc[0]

        sheet_name = src.GeneralOverview.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, RESEARCHED)

        return sheet_name, df

    @check_types
    def compose_fundings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Fundings DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Fundings DataFrame.
        """
        data = src.Fundings.default_data
        sheet_name = src.Fundings.sheet_name
        fundings = self.sheet_df_map.get(mrc.Fundings.sheet_name)

        if fundings is None or fundings.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "investors": ",",
        }
        fundings = self._expand_column_values(fundings, column_delimiters)

        data_fr = data.copy()
        data_fr["Row ID"] = range(1, len(fundings) + 1)
        data_fr["Year"] = fundings.year
        data_fr["Transaction Name"] = fundings.transaction_name
        data_fr["Valuation (USD)"] = fundings.valuation_usd
        data_fr["Money Raised (USD)"] = fundings.money_raised_usd
        data_fr["Investors"] = fundings.investors

        df = pd.DataFrame(data_fr)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_financials(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Financials DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Financials DataFrame.
        """
        fin_trend = self.sheet_df_map.get(mrc.FinancialTrend.sheet_name)
        categorized_revenue = self.sheet_df_map.get(mrc.CategorizedRevenue.sheet_name)
        sheet_name = src.Financials.sheet_name
        data = src.Financials.default_data

        if all(df is None or df.empty for df in (fin_trend, categorized_revenue)):
            # no financial data available, default values
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if fin_trend is not None and not fin_trend.empty:
            data_ft = data.copy()
            data_ft["Year"] = fin_trend.year
            data_ft["Financial Metrics"] = "Yearly Total"
            data_ft["Revenue (USD)"] = fin_trend.revenue_usd
            data_ft["EBITDA (USD)"] = fin_trend.ebitda_usd
            data_ft["Source"] = fin_trend.source
            transformed_sheets.append(pd.DataFrame(data_ft))

        if categorized_revenue is not None and not categorized_revenue.empty:
            data_rc = data.copy()
            data_rc["Year"] = categorized_revenue.year
            data_rc["Financial Metrics"] = categorized_revenue.financial_metrics
            data_rc["Category of Financial Metrics"] = categorized_revenue.category_of_financial_metrics
            data_rc["Revenue (USD)"] = categorized_revenue.revenue_usd
            data_rc["Source"] = categorized_revenue.source
            categories = pd.DataFrame(data_rc)
            transformed_sheets.append(categories)

        df = pd.concat(transformed_sheets)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_strategy(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Strategy DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Strategy DataFrame.
        """
        data = src.Strategy.default_data
        sheet_name = src.Strategy.sheet_name
        strategy = self.sheet_df_map.get(mrc.Strategy.sheet_name)

        if strategy is None or strategy.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_bs = data.copy()
        data_bs["Row ID"] = range(1, len(strategy) + 1)
        data_bs["Category"] = strategy.category
        data_bs["Title"] = strategy.title
        data_bs["Description"] = strategy.description
        data_bs["Source"] = strategy.source

        categories = ["Focus Areas", "Risks and Challenges", "Market Positioning"]
        s_categories = set(data_bs["Category"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                self.set_table_status(category, NO_INFO)

        df = pd.DataFrame(data_bs)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_offerings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Offerings DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Offerings DataFrame.
        """
        offerings_by_industry = self.sheet_df_map["Offerings by Industry"]
        offerings_by_services = self.sheet_df_map["Offerings by Services"]
        sheet_name = src.Offerings.sheet_name
        data = src.Offerings.default_data

        if all(df is None or df.empty for df in (offerings_by_industry, offerings_by_services)):
            # no financial data available, default values
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if offerings_by_industry is not None and not offerings_by_industry.empty:
            data_oi = data.copy()
            data_oi["Offerings"] = "Offerings by Industry"
            data_oi["Solution & Capabilities"] = offerings_by_industry.solutions_capabilities
            data_oi["Source"] = offerings_by_industry.source
            transformed_sheets.append(pd.DataFrame(data_oi))

        if offerings_by_services is not None and not offerings_by_services.empty:
            data_os = data.copy()
            data_os["Offerings"] = "Offerings by Services"
            data_os["Offerings by Category"] = offerings_by_services.service_category
            data_os["Products and Services"] = offerings_by_services.products_and_services
            data_os["Solution & Capabilities"] = offerings_by_services.solutions_capabilities
            data_os["Source"] = offerings_by_services.source

            offerings_by_services_df = pd.DataFrame(data_os)
            transformed_sheets.append(offerings_by_services_df)

        df = pd.concat(transformed_sheets)
        df.fillna("-", inplace=True)
        df["Row ID"] = range(1, len(df) + 1)

        offerings = ["Offerings by Industry", "Offerings by Services"]
        s_offerings = set(df["Offerings"].unique())
        for offering in offerings:
            offering_exists = any([x in offering for x in s_offerings])
            if offering_exists:
                self.set_table_status(offering, RESEARCHED)
            else:
                self.set_table_status(offering, NO_INFO)

        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_relationships(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Relationships DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Relationships DataFrame.
        """
        data = src.Relationships.default_data
        sheet_name = src.Relationships.sheet_name
        partnerships = self.sheet_df_map.get(mrc.Partnerships.sheet_name)
        acquisitions = self.sheet_df_map.get(mrc.Acquisitions.sheet_name)
        investments = self.sheet_df_map.get(mrc.Investments.sheet_name)
        mergers = self.sheet_df_map.get(mrc.Mergers.sheet_name)

        categories = [
            mrc.Acquisitions.sheet_name,
            mrc.Partnerships.sheet_name,
            mrc.Mergers.sheet_name,
            mrc.Investments.sheet_name,
        ]

        if all(df is None or df.empty for df in (partnerships, acquisitions, mergers, investments)):
            # no events available, default values
            df = pd.DataFrame(data, index=[0])
            for category in categories:
                self.set_table_status(category, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if partnerships is not None and not partnerships.empty:
            data_p = data.copy()
            data_p["Category"] = "Partnership"
            data_p["Company Name"] = partnerships.company_name
            data_p["Additional Information"] = partnerships.additional_information
            data_p["Year"] = partnerships.year
            data_p["Source"] = partnerships.source
            transformed_sheets.append(pd.DataFrame(data_p))

        if acquisitions is not None and not acquisitions.empty:
            data_a = data.copy()
            data_a["Category"] = "Acquisition"
            data_a["Company Name"] = acquisitions.company_name
            data_a["Additional Information"] = acquisitions.additional_information
            data_a["Year"] = acquisitions.year
            data_a["Source"] = acquisitions.source
            transformed_sheets.append(pd.DataFrame(data_a))

        if mergers is not None and not mergers.empty:
            data_m = data.copy()
            data_m["Category"] = "Mergers"
            data_m["Company Name"] = mergers.company_name
            data_m["Additional Information"] = mergers.additional_information
            data_m["Year"] = mergers.year
            data_m["Source"] = mergers.source
            transformed_sheets.append(pd.DataFrame(data_m))

        if investments is not None and not investments.empty:
            data_i = data.copy()
            data_i["Category"] = "Investments"
            data_i["Company Name"] = investments.company_name
            data_i["Additional Information"] = investments.additional_information
            data_i["Year"] = investments.year
            data_i["Source"] = investments.source
            data_i["Additional Information"] = data_i["Additional Information"].apply(lambda x: f"Amount: {x}")
            transformed_sheets.append(pd.DataFrame(data_i))

        df = pd.concat(transformed_sheets, ignore_index=True)
        df = df.sort_values(by=["Category"])
        df["Row ID"] = range(1, len(df) + 1)

        s_categories = set(df["Category"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                self.set_table_status(category, NO_INFO)

        return sheet_name, df

    @check_types
    def compose_clients(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Clients DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Clients DataFrame.
        """
        data = src.Clients.default_data
        sheet_name = src.Clients.sheet_name
        featured_clients = self.sheet_df_map.get(mrc.Clients.sheet_name)

        if featured_clients is None or featured_clients.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_fc = data.copy()
        data_fc["Company Name"] = featured_clients.company_name
        data_fc["EPAM Status"] = featured_clients.epam_status
        data_fc["Additional Information"] = featured_clients.additional_information
        data_fc["Source"] = featured_clients.source

        df = pd.DataFrame(data_fc)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_postings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Postings DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Job Postings DataFrame.
        """
        data = src.JobPostings.default_data
        sheet_name = src.JobPostings.sheet_name
        job_postings = self.sheet_df_map.get(mrc.JobPostings.sheet_name)

        if job_postings is None or job_postings.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jp = data.copy()
        column_delimiters = {
            "skills": ",",
            "location": ";",
        }
        job_postings = self._expand_column_values(job_postings, column_delimiters)
        data_jp["Row ID"] = range(1, len(job_postings) + 1)
        data_jp["Job Title"] = job_postings.job_title
        data_jp["Skills"] = job_postings.skills
        data_jp["Location"] = job_postings.location
        data_jp["Location ID"] = job_postings.location
        data_jp["Source"] = job_postings.source
        df = pd.DataFrame(data_jp)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_workforce_by_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Workforce by Locations DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = src.WorkforceByLocation.default_data
        sheet_name = src.WorkforceByLocation.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_workforce_by_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Workforce by Skills DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = src.WorkforceBySkills.default_data
        sheet_name = src.WorkforceBySkills.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Job Titles Demand DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        data = src.JobTitlesInDemand.default_data
        sheet_name = src.JobTitlesInDemand.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_awards_and_recognitions(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Awards and Recognitions DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Awards and Recognitions DataFrame.
        """
        data = src.AwardsAndRecognitions.default_data
        sheet_name = src.AwardsAndRecognitions.sheet_name
        awards_recognitions = self.sheet_df_map.get(mrc.AwardsAndRecognitions.sheet_name)

        if awards_recognitions is None or awards_recognitions.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ar = data.copy()
        data_ar["Row ID"] = range(1, len(awards_recognitions) + 1)
        data_ar["Year"] = awards_recognitions.year
        data_ar["Awards & Recognitions"] = awards_recognitions.awards_recognitions
        data_ar["Source"] = awards_recognitions.source

        df = pd.DataFrame(data_ar)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_additional_information(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Additional Information DataFrame from manual research competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Additional Information DataFrame.
        """
        data = src.AdditionalInformation.default_data
        sheet_name = src.AdditionalInformation.sheet_name
        ad_info = self.sheet_df_map.get(mrc.AdditionalInformation.sheet_name)

        if ad_info is None or ad_info.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ai = data.copy()
        data_ai["Row ID"] = range(1, len(ad_info) + 1)
        data_ai["Date"] = ad_info.date
        data_ai["Additional Materials"] = ad_info.additional_materials
        data_ai["Source"] = ad_info.source

        df = pd.DataFrame(data_ai)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_infongen(self) -> Tuple[str, pd.DataFrame]:
        """Compose InfoNgen"""
        data = src.InfoNgen.default_data
        sheet_name = src.InfoNgen.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Compose Data Availability"""
        return src.DataAvailability.sheet_name, self.status_table

    def compose_miscellaneous(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Miscellaneous sheet, handling logo, report, and infongen information.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Miscellaneous DataFrame.
        """
        sheet_name = mrc.Miscellaneous.sheet_name
        misc = self.sheet_df_map.get(mrc.Miscellaneous.sheet_name)
        data = {
            "logo_url": "-",
            "draup_report": "-",
            "infongen": "-",
        }

        infongen = src.InfoNgen.sheet_name

        if misc is None or misc.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(infongen, NOT_RESEARCHED)
            return sheet_name, df
        elif (
            misc.logo_url.isin([None, ""]).all()
            and misc.draup_report.isin([None, ""]).all()
            and misc.infongen.isin([None, ""]).all()
        ):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(infongen, NOT_RESEARCHED)
            return sheet_name, df
        elif (
            misc.logo_url.isin(["n/a", "-"]).all()
            and misc.draup_report.isin(["n/a", "-"]).all()
            and misc.infongen.isin(["n/a", "-"]).all()
        ):
            self.set_table_status(infongen, NO_INFO)
        else:
            self.set_table_status(infongen, RESEARCHED)

        return sheet_name, misc
