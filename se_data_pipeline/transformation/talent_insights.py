from typing import Dict, <PERSON><PERSON>

import pandas as pd
from pandera import check_types
from pandera.typing import DataFrame

from se_data_pipeline.component.base import TransformationMixin, Transformer
from se_data_pipeline.component.constants import NOT_RESEARCHED, RESEARCHED
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import sales_research as sr


class TalentInsightsTransformer(Transformer, TransformationMixin):
    """Transforms Talent Insights data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes TalentInsightsTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping Talent Insights sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = self.sheet_df_map[sr.DataAvailability.sheet_name]
        self.transformations = (
            self.compose_job_titles_demand,
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the Talent Insights data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                "General Overview",
                "Financials",
                "Projects",
                "Outsourcing Top Metrics",
                "IT Job Postings",
                "IT Workforce Locations",
                "Key People",
                "Management Team Changes",
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[sr.DataAvailability.table] == category_name
        status_not_set = self.status_table[sr.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, sr.DataAvailability.status] = status
        return None

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Titles Demand DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.JobTitlesDemand.default_data
        sheet_name = sr.JobTitlesDemand.sheet_name
        titles = self.sheet_df_map.get("Titles")

        if titles is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jtd = data.copy()
        data_jtd["Job Titles"] = titles.titles
        data_jtd["# of Job Postings for 12 months"] = titles.job_posts
        df = pd.DataFrame(data_jtd)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceLocations.default_data
        sheet_name = sr.ITWorkforceLocations.sheet_name
        locations = self.sheet_df_map.get("Locations")
        location_movements = self.sheet_df_map.get("Location Movements")

        if any(df is None for df in (locations, location_movements)):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwl = data.copy()
        merged_data = locations.merge(location_movements, on="location", how="outer")
        data_iwl["Location"] = merged_data.location
        data_iwl["Location ID"] = merged_data.location
        data_iwl["Employees"] = merged_data.employees
        data_iwl["1y growth, %"] = merged_data.one_year_growth * 100
        data_iwl["Departures"] = merged_data.departures
        data_iwl["Hires"] = merged_data.hires
        data_iwl["Ratio"] = merged_data.ratio
        data_iwl["Net change"] = merged_data.net_change
        data_iwl["# of Job Postings for 12 months"] = merged_data.job_posts
        df = pd.DataFrame(data_iwl)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceSkills.default_data
        sheet_name = sr.ITWorkforceSkills.sheet_name
        skills = self.sheet_df_map.get("Skills")

        if skills is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iws = data.copy()
        data_iws["Skills"] = skills.skills
        data_iws["Employees"] = skills.employees
        data_iws["1y growth"] = skills.one_year_growth * 100
        data_iws["# of Job Postings for 12 months"] = skills.job_posts
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return sr.DataAvailability.sheet_name, self.status_table
