from typing import Dict, <PERSON><PERSON>

import pandas as pd
from pandera import check_types
from pandera.typing import DataFrame

from se_data_pipeline.component.base import TransformationMixin, Transformer
from se_data_pipeline.component.constants import NOT_RESEARCHED, RESEARCHED
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import sales_research_competitors as src


class HireEZTransformer(Transformer, TransformationMixin):
    """Transforms HireEZ data to sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes HireEZTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping HireEZ sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)
        self.transformations = (
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the HireEZ data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                "IT Workforce Locations",
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[sr.DataAvailability.table] == category_name
        status_not_set = self.status_table[sr.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, sr.DataAvailability.status] = status
        return None

    def _calculate_one_year_growth(self, row):
        """Calculates the one-year growth percentage.

        Args:
            row (pd.Series): A row of the DataFrame containing 'num_professionals_this_year' and 'num_professionals_last_year'.

        Returns:
            float: The one-year growth percentage, or None if data is missing or last year's value is zero.
        """
        pros_this_year = row["num_professionals_this_year"]
        pros_last_year = row["num_professionals_last_year"]

        if pd.isna(pros_this_year) or pd.isna(pros_last_year):
            return None
        if pros_last_year == 0:
            return None

        one_year_growth = ((pros_this_year - pros_last_year) / pros_last_year) * 100
        return one_year_growth

    def _calculate_net_change(self, row):
        """Calculates the net change in employees.

        Args:
            row (pd.Series): A row of the DataFrame containing 'number_inflow' and 'number_outflow'.

        Returns:
            int: The net change, or None if data is missing.
        """
        number_inflow = row["number_inflow"]
        number_outflow = row["number_outflow"]

        if pd.isna(number_inflow) or pd.isna(number_outflow):
            return None

        return number_inflow - number_outflow

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceLocations.default_data
        sheet_name = sr.ITWorkforceLocations.sheet_name
        locations = self.sheet_df_map.get("Location")
        # TODO uncomment lines when Talent_move_by_locations location names are fixed
        # location_movements = self.sheet_df_map.get("Talent move_by_locations")

        # if any(df is None for df in (locations, location_movements)):
        if locations is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwl = data.copy()
        merged_data = locations
        # merged_data = locations.merge(location_movements, on="location", how="outer")
        merged_data["one_year_growth"] = merged_data.apply(self._calculate_one_year_growth, axis=1)
        # merged_data["net_change"] = merged_data.apply(self._calculate_net_change, axis=1)

        data_iwl["Location"] = merged_data.location
        data_iwl["Location ID"] = merged_data.location
        data_iwl["Employees"] = merged_data.num_professionals_this_year
        data_iwl["1y growth, %"] = merged_data.one_year_growth
        # data_iwl["Departures"] = merged_data.number_outflow
        # data_iwl["Hires"] = merged_data.number_inflow
        # data_iwl["Net change"] = merged_data.net_change
        df = pd.DataFrame(data_iwl)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceSkills.default_data
        sheet_name = sr.ITWorkforceSkills.sheet_name
        skills = self.sheet_df_map.get("Skills")

        if skills is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        skills["one_year_growth"] = skills.apply(self._calculate_one_year_growth, axis=1)

        data_iws = data.copy()
        data_iws["Skills"] = skills.skills
        data_iws["Employees"] = skills.num_professionals_this_year
        data_iws["1y growth"] = skills.one_year_growth
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return sr.DataAvailability.sheet_name, self.status_table


class HireEZCompetitorsTransformer(Transformer, TransformationMixin):
    """Transforms HireEZ competitor data to sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes HireEZCompetitorsTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping HireEZ sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(src.DataAvailability.default_data)
        self.transformations = (
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the HireEZ competitor data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map, sheets={src.WorkforceByLocation.sheet_name, "IT Workforce Locations", "Location"}
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[src.DataAvailability.table] == category_name
        status_not_set = self.status_table[src.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, src.DataAvailability.status] = status
        return None

    def _calculate_one_year_growth(self, row):
        """Calculates the one-year growth percentage.

        Args:
            row (pd.Series): A row of the DataFrame containing 'num_professionals_this_year' and 'num_professionals_last_year'.

        Returns:
            float: The one-year growth percentage, or None if data is missing or last year's value is zero.
        """
        pros_this_year = row["num_professionals_this_year"]
        pros_last_year = row["num_professionals_last_year"]

        if pd.isna(pros_this_year) or pd.isna(pros_last_year):
            return None
        if pros_last_year == 0:
            return None

        one_year_growth = ((pros_this_year - pros_last_year) / pros_last_year) * 100
        return one_year_growth

    def _calculate_net_change(self, row):
        """Calculates the net change in employees.

        Args:
            row (pd.Series): A row of the DataFrame containing 'number_inflow' and 'number_outflow'.

        Returns:
            int: The net change, or None if data is missing.
        """
        number_inflow = row["number_inflow"]
        number_outflow = row["number_outflow"]

        if pd.isna(number_inflow) or pd.isna(number_outflow):
            return None

        return number_inflow - number_outflow

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceByLocation.default_data
        sheet_name = src.WorkforceByLocation.sheet_name
        locations = self.sheet_df_map.get("Location")
        # talent_move_by_locations = self.sheet_df_map.get("Talent move_by_locations")

        if locations is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwl = data.copy()
        merged_data = locations
        # merged_data = locations.merge(talent_move_by_locations, on="location", how="outer")
        merged_data["one_year_growth"] = merged_data.apply(self._calculate_one_year_growth, axis=1)
        # merged_data["net_change"] = merged_data.apply(self._calculate_net_change, axis=1)

        data_iwl["Location"] = merged_data.location
        data_iwl["Location ID"] = merged_data.location
        data_iwl["Employees"] = merged_data.num_professionals_this_year
        data_iwl["1y growth, %"] = merged_data.one_year_growth
        # data_iwl["Departures"] = merged_data.number_outflow
        # data_iwl["Hires"] = merged_data.number_inflow
        # data_iwl["Net change"] = merged_data.net_change
        df = pd.DataFrame(data_iwl)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceBySkills.default_data
        sheet_name = src.WorkforceBySkills.sheet_name
        skills = self.sheet_df_map.get("Skills")

        if skills is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        skills["one_year_growth"] = skills.apply(self._calculate_one_year_growth, axis=1)

        data_iws = data.copy()
        data_iws["Skills"] = skills.skills
        data_iws["Employees"] = skills.num_professionals_this_year
        data_iws["1y growth"] = skills.one_year_growth
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return src.DataAvailability.sheet_name, self.status_table
