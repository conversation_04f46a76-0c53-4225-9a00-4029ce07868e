from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING, Dict, List, Optional, Set, Tuple

import pandas as pd

from se_data_pipeline.component.base import Transformer
from se_data_pipeline.component.constants import (
    MERGE,
    NO_INFO,
    NOT_RESEARCHED,
    PRIORITY,
    RESEARCHED,
    MergingLogicType,
    FinalReportType,
)
from se_data_pipeline.component.constants import ProviderNames as pn
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.models import sales_research_competitors as src
from se_data_pipeline.component.logger import logger

if TYPE_CHECKING:
    from se_data_pipeline.component.data_processor import DataFile


class InstructionsConfig:
    def __init__(self, provider_files: pd.DataFrame) -> None:
        self.priority_table_account = {
            sr.GeneralOverview.sheet_name: {PRIORITY: (pn.MANUAL_RESEARCH.value, pn.DRAUP.value)},
            sr.FundingRounds.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.CurrentEngagements.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.Financials.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.CompanyEntities.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.BusinessStructure.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.FeaturedClients.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.Events.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.TechStack.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.Projects.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.OutsourcingTopMetrics.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.ITJobPostings.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.JobTitlesDemand.sheet_name: {MERGE: (pn.DRAUP.value, pn.TALENT_DEMAND.value)},
            sr.ITWorkforceLocations.sheet_name: {MERGE: (pn.DRAUP.value, pn.HIRE_EZ.value, pn.TALENT_DEMAND.value)},
            sr.ITWorkforceSkills.sheet_name: {MERGE: (pn.DRAUP.value, pn.HIRE_EZ.value, pn.TALENT_DEMAND.value)},
            sr.Competitors.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.KeyPeople.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.Connections.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.ManagementTeamChanges.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH.value)},
            sr.AdditionalInformation.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.SWOTAnalysis.sheet_name: {MERGE: (pn.MANUAL_RESEARCH.value,)},
            sr.Priorities.sheet_name: {PRIORITY: (pn.MANUAL_RESEARCH.value,)},
        }
        self.priority_table_competitors = {
            src.GeneralOverview.sheet_name: {PRIORITY: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.Financials.sheet_name: {PRIORITY: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.Strategy.sheet_name: {MERGE: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.Offerings.sheet_name: {PRIORITY: (pn.MANUAL_RESEARCH_COMPETITORS.value,)},
            src.Relationships.sheet_name: {PRIORITY: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.Clients.sheet_name: {PRIORITY: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.JobPostings.sheet_name: {PRIORITY: (pn.DRAUP.value, pn.MANUAL_RESEARCH_COMPETITORS.value)},
            src.WorkforceByLocation.sheet_name: {MERGE: (pn.DRAUP.value, pn.TALENT_DEMAND.value, pn.HIRE_EZ.value)},
            src.WorkforceBySkills.sheet_name: {MERGE: (pn.DRAUP.value, pn.TALENT_DEMAND.value, pn.HIRE_EZ.value)},
            src.JobTitlesInDemand.sheet_name: {PRIORITY: (pn.TALENT_DEMAND.value, pn.DRAUP.value)},
            src.AwardsAndRecognitions.sheet_name: {PRIORITY: (pn.MANUAL_RESEARCH_COMPETITORS.value,)},
            src.AdditionalInformation.sheet_name: {PRIORITY: (pn.MANUAL_RESEARCH_COMPETITORS.value,)},
        }
        self.misc_instructions_account = {
            sr.Financials.sheet_name: sr.Financials.source,
            sr.BusinessStructure.sheet_name: sr.BusinessStructure.source,
            sr.FeaturedClients.sheet_name: sr.FeaturedClients.source,
            sr.Events.sheet_name: sr.Events.source,
            sr.Projects.sheet_name: sr.Projects.source,
            sr.ITJobPostings.sheet_name: sr.ITJobPostings.source,
            sr.Competitors.sheet_name: sr.Competitors.source,
            sr.AdditionalInformation.sheet_name: sr.AdditionalInformation.source,
        }
        self.misc_instructions_competitors = {
            src.Financials.sheet_name: src.Financials.source,
            src.Strategy.sheet_name: src.Strategy.source,
            src.Relationships.sheet_name: src.Relationships.source,
            src.Clients.sheet_name: src.Clients.source,
            src.JobPostings.sheet_name: src.JobPostings.source,
        }

    def get_priority_table(self, key):
        tables = {
            FinalReportType.ACCOUNT.value: self.priority_table_account,
            FinalReportType.COMPETITORS.value: self.priority_table_competitors,
        }
        return tables.get(key)

    def get_misc_instructions(self, key):
        tables = {
            FinalReportType.ACCOUNT.value: self.misc_instructions_account,
            FinalReportType.COMPETITORS.value: self.misc_instructions_competitors,
        }
        return tables.get(key)


class ComposerMixin:
    """
    A mixin class to handle operations on DataFrame maps used for composing sheet data.
    """

    def merge_dataframe_maps(
        self, final_model, dataframe_maps: Dict[str, Dict[str, pd.DataFrame]]
    ) -> Dict[str, pd.DataFrame]:
        """
        Merges multiple DataFrame maps into one consolidated map.

        Args:
            final_model final model class.
            dataframe_maps (List[Dict[str, pd.DataFrame]]): List of DataFrame maps.

        Returns:
            Dict[str, pd.DataFrame]: A consolidated DataFrame map with merged data.
            :param dataframe_maps:
            :param final_model:
        """
        merged_map = {model.sheet_name: pd.DataFrame([model.default_data]) for model in final_model.MODELS}
        for _, default_data in merged_map.items():
            default_data["source_provider"] = "Base"
        for provider, dataframe_map in dataframe_maps.items():
            for sheet_name, dataframe in dataframe_map.items():
                dataframe["source_provider"] = provider
                if sheet_name not in merged_map:
                    merged_map[sheet_name] = dataframe
                else:
                    merged_map[sheet_name] = pd.concat([merged_map[sheet_name], dataframe], ignore_index=True)
        return merged_map

    def prioritize_providers(
        self, sheet_map: Dict[str, pd.DataFrame], default_data, priority_table=None
    ) -> Dict[str, pd.DataFrame]:
        if priority_table is None:
            priority_table = {}

        def _any_logic(df: pd.DataFrame, column: str, values: Tuple[str]) -> pd.DataFrame:
            return df[df[column].isin(values)]

        def _priority_logic(df: pd.DataFrame, column: str, values: Tuple[str], sheet_name: str) -> pd.DataFrame:
            for value in values:
                prioritized_df = df[df[column] == value]
                if value == "Base":
                    return default_data.get(sheet_name)
                elif not prioritized_df.empty and not all(
                    [
                        (
                            default_data.get(sheet_name).values
                            == prioritized_df.loc[:, prioritized_df.columns != "source_provider"].values
                        ).all()
                    ]
                ):
                    return prioritized_df

            return pd.DataFrame(columns=df.columns)

        for sheet_name, logic in priority_table.items():
            if sheet_name in sheet_map:
                df = sheet_map[sheet_name]
                column = "source_provider"

                for operation, values in logic.items():
                    values = values + ("Base",)
                    if operation == MERGE:
                        sheet_map[sheet_name] = _any_logic(df, column, values)
                    elif operation == PRIORITY:
                        sheet_map[sheet_name] = _priority_logic(df, column, values, sheet_name)

        for sheet_name in sheet_map.keys():
            if "source_provider" in sheet_map[sheet_name].columns:
                sheet_map[sheet_name] = sheet_map[sheet_name].drop(columns=["source_provider"])

        return sheet_map

    def remove_duplicates(
        self, final_model, sheet_map: Dict[str, pd.DataFrame], exclude_columns: List[str] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Removes duplicates from DataFrames in the sheet map while respecting excluded columns.

        Args:
            sheet_map (Dict[str, pd.DataFrame]): Map of sheet names to DataFrames.
            exclude_columns (List[str]): Columns to exclude from duplicate checks. Defaults to a standard set.

        Returns:
            Dict[str, pd.DataFrame]: Sheet map with duplicates removed.
        """
        if exclude_columns is None:
            exclude_columns = ["Row ID", "Location", "Data as of"]

        cleaned_map = {}
        for sheet_name, dataframe in sheet_map.items():
            if sheet_name == sr.DataAvailability.sheet_name:
                cleaned_map[sheet_name] = dataframe
                continue

            subset_columns = [col for col in dataframe.columns if col not in exclude_columns]
            cleaned_df = dataframe.drop_duplicates(subset=subset_columns)

            if len(cleaned_df) > 1:
                try:
                    default_data = next(
                        model.default_data for model in final_model.MODELS if model.sheet_name == sheet_name
                    )
                    default_df = pd.DataFrame([default_data])
                    default_subset = [col for col in default_df.columns if col not in exclude_columns]
                    cleaned_df = cleaned_df.loc[
                        ~cleaned_df[default_subset]
                        .apply(tuple, axis=1)
                        .isin(default_df[default_subset].apply(tuple, axis=1))
                    ]
                except StopIteration:
                    pass

            cleaned_map[sheet_name] = cleaned_df

        return cleaned_map

    def reset_row_ids(self, sheet_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Resets the "Row ID" column in each DataFrame to start from 1 if it exists.

        Args:
            sheet_map (Dict[str, pd.DataFrame]): Map of sheet names to DataFrames.

        Returns:
            Dict[str, pd.DataFrame]: Updated sheet map with reset Row IDs.
        """
        updated_map = {}
        for sheet_name, dataframe in sheet_map.items():
            if "Row ID" in dataframe.columns:
                dataframe["Row ID"] = range(1, len(dataframe) + 1)
            updated_map[sheet_name] = dataframe
        return updated_map

    def set_source_link(
        self, instructions: Dict[str:str], sheet_map: Dict[str, pd.DataFrame], link_to_draup: str
    ) -> Dict[str, pd.DataFrame]:

        def update_column_values(df, column_to_update, replacement_value):
            df[column_to_update] = df[column_to_update].replace([None, "-", "n/a"], replacement_value)
            return df

        for sheet_name, column in instructions.items():
            try:
                sheet_map[sheet_name] = update_column_values(sheet_map[sheet_name], column, link_to_draup)
            except Exception:
                logger.error("Error adding source link")
                raise

        return sheet_map

    def process_data_availability(self, sheet_map: Dict[str, pd.DataFrame], final_model) -> Dict[str, pd.DataFrame]:
        """
        Updates the Data Availability sheet with default values and removes duplicates.

        Args:
            sheet_map (Dict[str, pd.DataFrame]): Map of sheet names to DataFrames.

        Returns:
            Dict[str, pd.DataFrame]: Updated sheet map with processed Data Availability sheet.
            :param final_model:
        """
        default_data = pd.DataFrame(final_model.DataAvailability.default_data)
        availability_df = sheet_map.get("Data Availability")
        for table_name in default_data["Table"]:
            table_statuses = availability_df.loc[availability_df["Table"] == table_name, "Status of the research"]

            if NO_INFO in table_statuses.values:
                status = NO_INFO
            elif RESEARCHED in table_statuses.values:
                status = RESEARCHED
            else:
                status = NOT_RESEARCHED

            default_data.loc[default_data["Table"] == table_name, "Status of the research"] = status

        sheet_map["Data Availability"] = default_data
        return sheet_map

    def _rename_data_availability_categories(self, sheet_df_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Renames categories in the Data Availability sheet for consistency.

        Args:
            sheet_df_map (Dict[str, pd.DataFrame]): The current sheet_df_map.

        Returns:
            Dict[str, pd.DataFrame]: The updated sheet_df_map with renamed categories in the Data Availability sheet.
        """
        data_status = "Data Availability"
        if data_status not in sheet_df_map:
            return sheet_df_map

        replacement_dict = {
            "Acquisitions": "Acquisition",
            "Partnerships": "Partnership",
            "Investments": "Investment",
            "Mergers": "Merger",
            "Internal Projects": "Internal Project",
        }
        sheet_df_map[data_status].replace(replacement_dict, inplace=True)
        return sheet_df_map

    def post_merge(
        self, sheet_df_map: Dict[str, pd.DataFrame], sheets_to_merge_list: Dict[str : Set[str]]
    ) -> Dict[str, pd.DataFrame]:
        if sheets_to_merge_list == {}:
            return sheet_df_map

        for base_sheet, sheets_to_merge in sheets_to_merge_list.items():
            sheet_df_map[base_sheet] = pd.concat(
                [sheet_df_map[base_sheet]] + [sheet_df_map[sheet] for sheet in sheets_to_merge], ignore_index=True
            )
            for sheet in sheets_to_merge:
                del sheet_df_map[sheet]

        return sheet_df_map


class SalesResearchComposer(Transformer, ComposerMixin):
    """Composes the final Sales Research report by combining data from different providers."""

    def __init__(
        self,
        data_files: List[DataFile],
    ) -> None:
        """Initializes SalesResearchComposer with a list of DataFile objects.

        Args:
            data_files (List[DataFile]): A list of DataFile objects, each containing data from a specific provider.
        """
        self.data_files = data_files
        self.provider_files = {data_file.provider.name.value: data_file for data_file in data_files}
        self.instructions = InstructionsConfig(self.provider_files)
        self.available_providers = [data_file.provider for data_file in data_files]
        self.all_groups = self.get_all_available_groups()

    @property
    def file_name(self) -> str:
        """Returns the file name of the Manual Research or Draup data file.

        Returns:
            str: The file name.
        """
        manual_research_file = self.provider_files.get(pn.MANUAL_RESEARCH.value)
        draup_file = self.provider_files.get(pn.DRAUP.value)

        if manual_research_file:
            return manual_research_file.file_name
        elif draup_file:
            return draup_file.file_name
        else:
            raise KeyError("Neither 'Manual Research' nor 'Draup' files are available to extarct file name.")

    @property
    def company_name(self) -> str:
        """Returns the company name from the Manual Research or Draup data file.

        Returns:
            str: The company name.
        """
        manual_research_file = self.provider_files.get(pn.MANUAL_RESEARCH.value)
        draup_file = self.provider_files.get(pn.DRAUP.value)

        if manual_research_file:
            return manual_research_file.company_name
        elif draup_file:
            return draup_file.company_name
        else:
            raise KeyError("Neither 'Manual Research' nor 'Draup' files are available to extarct company name.")

    @property
    def link_to_draup(self):
        manual_file = self.provider_files.get(pn.MANUAL_RESEARCH.value)
        if manual_file is None:
            return "-"
        merging_logics_df = manual_file._normalised_df.get("Merging Logics")
        if merging_logics_df is None:
            return "-"
        try:
            draup_link_row = merging_logics_df[merging_logics_df.table == "Draup Report Link"]

            if not draup_link_row.empty:
                return draup_link_row.iloc[0].merging_way

        except (KeyError, AttributeError):
            logger.error("Draup Report Link not found or DataFrame malformed.")
            raise

        return "-"

    def get_all_available_groups(self):
        """Returns all available data groups from the first provider in provider_files.

        Returns:
            dict: A dictionary of data groups.
        Raises:
            IndexError: If provider_files is empty.
        """
        return list(self.provider_files.values())[0].provider.get_all_data_groups(pn.SALES_RESEARCH.name)

    def get_group(self) -> bool:
        available_provider_set = set(provider.name for provider in self.available_providers)
        for group in self.all_groups:
            group_provider_set = set(provider.name for provider in group)
            if available_provider_set == group_provider_set:
                return True
        return False

    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the data based on the identified data group.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to transformed DataFrames.
        Raises:
            NotImplementedError: If composing rules for the available providers are not implemented.
        """
        if not self.get_group():
            raise ValueError("No matching group found for the available providers")
        # Composing
        final_df = {data_file.provider.name.value: data_file.transformed_df for data_file in self.data_files}
        merged_dataframe_maps = self.merge_dataframe_maps(sr, final_df)
        prioritized_dataframes = self.prioritize_providers(
            merged_dataframe_maps,
            {model.sheet_name: pd.DataFrame([model.default_data]) for model in sr.MODELS},
            self.instructions.get_priority_table(FinalReportType.ACCOUNT.value),
        )

        pm_df_map = self.post_merge(prioritized_dataframes, {sr.KeyPeople.sheet_name: {sr.Connections.sheet_name}})
        cleaned_dataframes = self.remove_duplicates(sr, pm_df_map)
        reset_dataframes = self.reset_row_ids(cleaned_dataframes)

        miscellaneous_data = self.set_source_link(
            self.instructions.get_misc_instructions(FinalReportType.ACCOUNT.value),
            reset_dataframes,
            self.link_to_draup,
        )

        data_with_availability = self.process_data_availability(miscellaneous_data, sr)
        final_dataframes = self.update_general_overview(data_with_availability)

        sheet_df_map = self._rename_data_availability_categories(final_dataframes)
        sheet_df_map = self.aggregate_tables(sheet_df_map)

        return sheet_df_map

    def update_general_overview(self, sheet_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Calculates and updates the General Overview sheet based on other sheets.

        Args:
            sheet_map (Dict[str, pd.DataFrame]): Map of sheet names to DataFrames.

        Returns:
            Dict[str, pd.DataFrame]: Updated sheet map with General Overview data.
        """
        # Extract required sheets
        funding_rounds = sheet_map.get(sr.FundingRounds.sheet_name)
        financials = sheet_map.get(sr.Financials.sheet_name)
        data_availability = sheet_map.get(sr.DataAvailability.sheet_name)
        general_overview = sheet_map.get(sr.GeneralOverview.sheet_name)

        # Validate that all required sheets are present
        if not all(df is not None for df in [funding_rounds, financials, general_overview]):
            raise ValueError("Missing required sheets for General Overview update.")

        # Clean funding rounds data by dropping duplicates
        funding_rounds = funding_rounds.drop_duplicates(
            subset=["Year", "Transaction Name", "Valuation", "Money Raised", "Currency Code"]
        )

        # Get the latest year from funding rounds and financials
        latest_year_fr = funding_rounds["Year"].max()
        latest_year_fin = financials["Year"].max()
        current_year = datetime.now().year

        # Check if funding rounds data is marked as unavailable
        if (
            data_availability.query("Table == @sr.FundingRounds.sheet_name and `Status of the research` == 'No Info'")
            .any()
            .any()
        ):
            funding_rounds_count = 0
        else:
            # Calculate the number of funding rounds
            funding_rounds_count = funding_rounds[["Year", "Investors"]].drop_duplicates().shape[0]

        # Check if financial data is not set in the general overview
        general_overview_fin_not_set = all(
            [
                general_overview["Revenue"].iloc[0] == 0,
                general_overview["IT Spend"].iloc[0] == 0,
            ]
        )

        # Check if funding rounds data is not set in the general overview
        general_overview_fr_not_set = general_overview["Valuation"].iloc[0] == 0

        # Update financial data in the general overview if conditions are met
        if latest_year_fin >= current_year - 2 and general_overview_fin_not_set:
            financials_filtered = financials.query("Year == @latest_year_fin and `Financial Metrics` == 'Yearly Total'")
            revenue = financials_filtered["Revenue"].sum()
            it_spend = financials_filtered["IT Spend"].sum()
            currency_code = financials_filtered["Currency Code"].unique()
            currency_code = currency_code[0] if len(currency_code) == 1 else "-"

            general_overview["Revenue"] = (revenue,)
            general_overview["IT Spend"] = (it_spend,)
            general_overview["Currency Code"] = (currency_code,)

        # Update funding rounds data in the general overview if conditions are met
        if general_overview_fr_not_set:
            valuation = funding_rounds.loc[funding_rounds["Year"] == latest_year_fr, "Valuation"].sum()

            general_overview["Valuation"] = (valuation,)

        total_funding = funding_rounds["Money Raised"].sum()
        general_overview["Total Funding Amount"] = (total_funding,)

        # Update the number of funding rounds in the general overview
        general_overview["# Of Funding Rounds"] = (funding_rounds_count,)

        # Update the sheet map with the modified general overview
        sheet_map[sr.GeneralOverview.sheet_name] = general_overview
        return sheet_map


class SalesResearchCompetitorsComposer(Transformer, ComposerMixin):
    """Composes the final Sales Research Competitors report by combining data from different providers."""

    def __init__(
        self,
        data_files: List[DataFile],
    ) -> None:
        """Initializes SalesResearchCompetitorsComposer with a list of DataFile objects.

        Args:
            data_files (List[DataFile]): A list of DataFile objects, each containing data from a specific provider.
        """
        self.instructions = InstructionsConfig()
        self.data_files = data_files
        self.provider_files = {data_file.provider.name.value: data_file for data_file in data_files}
        self.available_providers = [data_file.provider for data_file in data_files]
        self.all_groups = self.get_all_available_groups()

    @property
    def file_name(self) -> str:
        """Returns the file name of the Manual Research or Draup data file.

        Returns:
            str: The file name.
        """
        manual_research_file = self.provider_files.get(pn.MANUAL_RESEARCH_COMPETITORS.value)
        draup_file = self.provider_files.get(pn.DRAUP.value)

        if manual_research_file:
            return manual_research_file.file_name
        elif draup_file:
            return draup_file.file_name
        else:
            raise KeyError("Neither 'Manual Research' nor 'Draup' files are available to extarct file name.")

    @property
    def company_name(self) -> str:
        """Returns the company name from the Manual Research or Draup data file.

        Returns:
            str: The company name.
        """
        manual_research_file = self.provider_files.get(pn.MANUAL_RESEARCH_COMPETITORS.value)
        draup_file = self.provider_files.get(pn.DRAUP.value)

        if manual_research_file:
            return manual_research_file.company_name
        elif draup_file:
            return draup_file.company_name
        else:
            raise KeyError("Neither 'Manual Research' nor 'Draup' files are available to extarct company name.")

    def get_all_available_groups(self):
        """Returns all available data groups from the first provider in provider_files.

        Returns:
            dict: A dictionary of data groups.
        Raises:
            IndexError: If provider_files is empty.
        """
        return list(self.provider_files.values())[0].provider.get_all_data_groups(pn.SALES_RESEARCH_COMPETITORS.name)

    def get_group(self) -> bool:
        available_provider_set = set(provider.name for provider in self.available_providers)
        for group in self.all_groups:
            group_provider_set = set(provider.name for provider in group)
            if available_provider_set == group_provider_set:
                return True
        return False

    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the competitor data based on the identified data group.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sheet names to transformed DataFrames.
        Raises:
            NotImplementedError: If composing rules for the available providers are not implemented.
        """
        group_name = self.get_group()
        if not group_name:
            raise ValueError("No matching group found for the available providers")
        # Composing
        final_df = {data_file.provider.name.value: data_file.transformed_df for data_file in self.data_files}
        merged_dataframe_maps = self.merge_dataframe_maps(src, final_df)
        prioritized_dataframes = self.prioritize_providers(
            merged_dataframe_maps,
            {model.sheet_name: pd.DataFrame([model.default_data]) for model in src.MODELS},
            self.instructions.get_priority_table("competitors"),
        )
        pm_sheet_map = self.post_merge(prioritized_dataframes, {})
        cleaned_dataframes = self.remove_duplicates(src, pm_sheet_map)
        reset_dataframes = self.reset_row_ids(cleaned_dataframes)
        miscellaneous_data = self.set_miscellaneous_sheet(
            reset_dataframes, self.instructions.get_misc_instructions("competitors")
        )
        data_with_availability = self.process_data_availability(miscellaneous_data, src)
        final_dataframes = self.update_general_overview(data_with_availability)

        sheet_df_map = self._rename_data_availability_categories(final_dataframes)
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    def update_general_overview(self, sheet_map: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Calculates and updates the General Overview sheet based on other sheets.

        Args:
            sheet_map (Dict[str, pd.DataFrame]): Map of sheet names to DataFrames.

        Returns:
            Dict[str, pd.DataFrame]: Updated sheet map with General Overview data.
        """
        fundings = sheet_map.get(src.Fundings.sheet_name)
        data_availability = sheet_map.get(src.DataAvailability.sheet_name)
        general_overview = sheet_map.get(src.GeneralOverview.sheet_name)

        if not all(df is not None for df in [fundings, general_overview]):
            raise ValueError("Missing required sheets for General Overview update.")

        funding_rounds = fundings.drop_duplicates(
            subset=["Year", "Transaction Name", "Valuation (USD)", "Money Raised (USD)"]
        )
        latest_year = fundings["Year"].max()
        valuation = funding_rounds.loc[funding_rounds["Year"] == latest_year, "Valuation (USD)"].sum()
        total_funding = funding_rounds["Money Raised (USD)"].sum()
        funding_rounds_count = funding_rounds["Transaction Name"].nunique()

        if (
            data_availability.query("Table == @src.Fundings.sheet_name and `Status of the research` == 'No Info'")
            .any()
            .any()
        ):
            funding_rounds_count = 0

        general_overview["Valuation (USD)"] = valuation
        general_overview["Total Funding Amount (USD)"] = total_funding
        general_overview["# Of Funding Rounds"] = funding_rounds_count

        sheet_map[src.GeneralOverview.sheet_name] = general_overview
        return sheet_map
