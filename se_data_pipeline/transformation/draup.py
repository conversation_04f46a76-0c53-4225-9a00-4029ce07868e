import re
from itertools import product
from typing import Dict, List, Tuple

import pandas as pd
from pandera import check_types
from pandera.typing import Data<PERSON>rame

from se_data_pipeline.component.base import TransformationMixin, Transformer
from se_data_pipeline.component.constants import DEFAULT_LOC_ID, NO_INFO, NOT_RESEARCHED, RESEARCHED, MergingLogicType
from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.component.utils import normalise_string
from se_data_pipeline.models import draup
from se_data_pipeline.models import manual_research as mr
from se_data_pipeline.models import manual_research_competitors as mrc
from se_data_pipeline.models import sales_research as sr
from se_data_pipeline.models import sales_research_competitors as src


# pylint: disable=no-member
class DraupTransformer(Transformer, TransformationMixin):
    """Transforms Draup data to sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        manual_merge_rules: <PERSON>Frame,
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes DraupTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping Draup sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location and job title processing.
        """
        self.sheet_df_map = sheet_df_map
        self.manual_merge_rules = manual_merge_rules
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(sr.DataAvailability.default_data)
        self.transformations = (
            self.compose_general_overview,
            self.compose_funding_rounds,
            self.compose_current_engagements,
            self.compose_financials,
            self.compose_company_entities,
            self.compose_business_structure,
            self.compose_featured_clients,
            self.compose_events,
            self.compose_tech_stack,
            self.compose_projects,
            self.compose_outsourcing_top_metrics,
            self.compose_job_postings,
            self.compose_job_titles_demand,
            self.compose_it_workforce_locations,
            self.compose_it_workforce_skills,
            self.compose_competitors,
            self.compose_key_people,
            self.compose_management_team_changes,
            self.compose_additional_information,
            self.compose_swot_analysis,
            self.compose_infongen,
            self.compose_priorities,
            self.compose_data_availability,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the Draup data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                "General Overview",
                "Financials",
                "Projects",
                "Outsourcing Top Metrics",
                "IT Job Postings",
                "IT Workforce Locations",
                "Key People",
                "Management Team Changes",
            },
        )
        sheet_df_map = self._replace_job_title_ids(
            sheet_df_map,
            sheets={
                "Key People",
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    # TODO remove duplication
    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[sr.DataAvailability.table] == category_name
        status_not_set = self.status_table[sr.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, sr.DataAvailability.status] = status
        return None

    def should_be_merged(self, sheet_name: str) -> bool:

        if self.manual_merge_rules is not None:
            # Find configuration for this sheet
            sheet_config = self.manual_merge_rules[
                self.manual_merge_rules.draup_info_tab.str.contains(sheet_name, na=False)
            ]
        else:
            return False

        # Get the merging way value
        merging_way = sheet_config["merging_way"].iloc[0]

        # Handle empty/null merging way
        if not merging_way or pd.isna(merging_way):
            return False

        # Don't merge if explicitly set to MANUAL_ONLY
        return merging_way != MergingLogicType.MANUAL_ONLY.value

    @check_types
    def compose_general_overview(self) -> Tuple[str, pd.DataFrame]:
        """Composes the General Overview DataFrame from Draup data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the General Overview DataFrame.
        """
        about = self.sheet_df_map["About"]
        general_overview = self.sheet_df_map["General Overview"]
        fin_trend = self.sheet_df_map.get("Financials Trend")

        latest_fin_year = None
        if fin_trend is not None:
            # extracting the latest financial year data
            latest_fin_year = fin_trend[fin_trend["financial_metric"] == fin_trend["financial_metric"].max()]
            assert len(latest_fin_year) == 1, "DataFrame does not have 1 row"
            draup.FinancialsTrend.validate(latest_fin_year)

        # composing output dataframe
        data = sr.GeneralOverview.default_data
        data["Account"] = about.account.iloc[0]
        data["About"] = about.about.iloc[0] if "about" in about.columns else None
        data["HQ Location"] = general_overview.hq_location.iloc[0]
        data["Location ID"] = general_overview.hq_location.iloc[0]
        data["Industry"] = general_overview.primary_vertical.iloc[0]

        # total workforce is not always available
        if "total_workforce" in general_overview.columns:
            data["Number of Employees"] = general_overview.total_workforce.iloc[0]

        data["Company Type"] = general_overview.company_type.iloc[0]
        data["Website"] = general_overview.website.iloc[0]

        # private companies do not provide financial data
        if data["Company Type"] == "PUBLIC" and latest_fin_year is not None:
            data["Revenue"] = latest_fin_year.revenue.iloc[0]
            data["IT Spend"] = latest_fin_year.it_spend.iloc[0]
            data["Currency Code"] = latest_fin_year.currency_code.iloc[0]

        sheet_name = sr.GeneralOverview.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_funding_rounds(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Funding Rounds DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty Funding Rounds DataFrame.
        """
        sheet_name = sr.FundingRounds.sheet_name
        df = pd.DataFrame(sr.FundingRounds.default_data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_current_engagements(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Current Engagements DataFrame with default data.

        Returns:
            Tuple[str, pd.DataFrame]: Sheet name and DataFrame with company name populated.
        """
        data = sr.CurrentEngagements.default_data
        data["Company name"] = self.sheet_df_map["About"].account.iloc[0]
        sheet_name = sr.CurrentEngagements.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_financials(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Financials DataFrame from various Draup financial sheets.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the combined Financials DataFrame.
        """
        fin_trend = self.sheet_df_map.get("Financials Trend")
        revenue_business = self.sheet_df_map.get("Revenue by Business")
        revenue_region = self.sheet_df_map.get("Revenue by Region")
        sheet_name = sr.Financials.sheet_name

        data = sr.Financials.default_data
        if all(df is None for df in (fin_trend, revenue_business, revenue_region)):
            # no financial data available, default values
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        # handling Financial Trends data
        if fin_trend is not None and self.should_be_merged(draup.FinancialsTrend.sheet_name):
            data_ft = data.copy()
            data_ft["Year"] = fin_trend.financial_metric
            data_ft["Financial Metrics"] = "Yearly Total"
            data_ft["Revenue"] = fin_trend.revenue
            data_ft["IT Spend"] = fin_trend.it_spend
            data_ft["EBITDA"] = fin_trend.ebitda
            data_ft["Currency Code"] = fin_trend.currency_code
            transformed_sheets.append(pd.DataFrame(data_ft))

        # handling Revenue by Business data
        if revenue_business is not None and self.should_be_merged(draup.RevenueBusiness.sheet_name):
            data_rb = data.copy()
            data_rb["Year"] = revenue_business.financial_year
            data_rb["Financial Metrics"] = "Revenue by Business"
            data_rb["Category of Financial Metrics"] = revenue_business.business
            data_rb["Revenue"] = revenue_business.revenue
            data_rb["Currency Code"] = revenue_business.currency_code
            transformed_sheets.append(pd.DataFrame(data_rb))

        # handling Revenue by Region data
        if revenue_region is not None and self.should_be_merged(draup.RevenueRegion.sheet_name):
            data_rr = data.copy()
            data_rr["Year"] = revenue_region.financial_year
            data_rr["Financial Metrics"] = "Revenue by Region"
            data_rr["Category of Financial Metrics"] = revenue_region.region
            data_rr["Location ID"] = revenue_region.region
            data_rr["Revenue"] = revenue_region.revenue
            data_rr["Currency Code"] = revenue_region.currency_code
            transformed_sheets.append(pd.DataFrame(data_rr))

        if transformed_sheets:
            df = pd.concat(transformed_sheets).reset_index(drop=True)
            self.set_table_status(sheet_name, RESEARCHED)
            return sheet_name, df
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

    @check_types
    def compose_company_entities(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Company Entities default dataframe."""
        data = sr.CompanyEntities.default_data
        sheet_name = sr.CompanyEntities.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_business_structure(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Business Structure DataFrame with default data and company name.

        Returns:
            Tuple[str, pd.DataFrame]: Sheet name and DataFrame with company name.
        """
        data = sr.BusinessStructure.default_data
        data["Business Unit"] = self.sheet_df_map["About"].account.iloc[0]
        sheet_name = sr.BusinessStructure.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_featured_clients(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Featured Clients DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty Featured Clients DataFrame.
        """
        data = sr.FeaturedClients.default_data
        sheet_name = sr.FeaturedClients.sheet_name
        engagement_details = self.sheet_df_map.get(draup.EngagementDetails.sheet_name)

        if (
            engagement_details is None
            or engagement_details.empty
            or not self.should_be_merged(draup.EngagementDetails.sheet_name)
        ):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "customer_verticals": ";",
        }
        engagement_details = self._expand_column_values(engagement_details, column_delimiters)

        data_fc = data.copy()
        data_fc["Company Name"] = engagement_details.customer
        data_fc["Industry"] = engagement_details.customer_verticals
        data_fc["Additional Information"] = engagement_details.workloads
        data_fc["Source"] = "-"

        df = pd.DataFrame(data_fc)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_additional_information(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Additional Information DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty Additional Information DataFrame.
        """
        data = sr.AdditionalInformation.default_data
        sheet_name = sr.AdditionalInformation.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    def _derive_subcategory(self, tag):
        """Derives the subcategory from a given tag.

        Args:
            tag (str): The input tag.

        Returns:
            str: The derived subcategory.
        """
        known_tag = normalise_string(tag)
        mapping = {
            "partnerships": "Partnership",
            "fundraise_and_investments": "Investment",
            "mergers_and_acquisitions": "Acquisition",
        }
        return mapping.get(known_tag, tag)

    def _derive_category(self, subcategory):
        """Derives the category from a given subcategory.

        Args:
            subcategory (str): The input subcategory.

        Returns:
            str: The derived category.
        """
        pa_categories = {
            "Partnership",
            "Investment",
            "Acquisition",
        }
        return "Partnerships & Acquisitions" if subcategory in pa_categories else "News"

    def _derive_title(self, row):
        """Derives the title from a given row.

        Args:
            row (pd.Series): The input row.

        Returns:
            str: The derived title.
        """
        return row["signal_title"]

    def _drop_duplicate_titles(self, df: pd.DataFrame) -> pd.DataFrame:
        """Drops duplicate titles within each category based on title and description.

        Args:
            df (pd.DataFrame): The input DataFrame.

        Returns:
            pd.DataFrame: The DataFrame with duplicate titles removed.
        """
        df = df.copy()
        categories = df["Category"].unique().tolist()
        unique_columns = ["Title", "Description"]
        combinations = [tuple(comb) for comb in product(categories, unique_columns)]
        indeces_to_drop = []
        for category, column in combinations:
            selection = df[df["Category"] == category]
            ids = selection.loc[selection.duplicated(subset=[column], keep="first")].index
            indeces_to_drop.extend(ids)
        return df.drop(indeces_to_drop).reset_index(drop=True)

    def _fill_description(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fills missing descriptions with titles.

        Args:
            df (pd.DataFrame): The input DataFrame.

        Returns:
            pd.DataFrame: The DataFrame with filled descriptions.
        """
        df["Description"] = df["Description"].fillna(df["Title"])
        return df

    @check_types
    def compose_events(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Events DataFrame from Business Intentions and Account Priorities.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Events DataFrame.
        """
        data = sr.Events.default_data
        sheet_name = sr.Events.sheet_name

        categories = [
            "Focus Areas",
            "Risks and Challenges",
            "Market Positioning",
            mr.Acquisitions.sheet_name,
            mr.Partnerships.sheet_name,
            mr.Investments.sheet_name,
            mr.Mergers.sheet_name,
        ]
        df = pd.DataFrame(data, index=[0])
        for category in categories:
            self.set_table_status(category, NOT_RESEARCHED)
        self.set_table_status(sheet_name, NOT_RESEARCHED)

        return sheet_name, df

    def _get_category_tech_tools(self, df: pd.DataFrame) -> Tuple[List[str], List[str]]:
        """Extracts categories and tech tools from the solutions DataFrame.

        Args:
            df (pd.DataFrame): The input DataFrame (Solutions sheet).

        Returns:
            Tuple[List[str], List[str]]: Lists of categories and corresponding tech tools.
        """
        categories = []
        tech_tools = []
        for index, row in df.iterrows():
            category = row["category"]
            tools = row["solution"].split(", ")
            categories.extend([category] * len(tools))
            tech_tools.extend(tools)
        return categories, tech_tools

    def _lowercase_drop_duplicates(self, df: pd.DataFrame, column_name: str) -> pd.DataFrame:
        """Converts a column to lowercase and drops duplicate rows based on that column.

        Args:
            df (pd.DataFrame): The input DataFrame.
            column_name (str): The name of the column to process.

        Returns:
            pd.DataFrame: The DataFrame with duplicates removed based on the lowercase version of the specified column.
        """
        lowercase = "lowercase"
        df = df.copy()
        df[lowercase] = df[column_name].str.lower()
        df = df.drop_duplicates(subset=lowercase, keep="first").reset_index(drop=True)
        df = df.drop(columns=[lowercase])
        return df

    @check_types
    def compose_tech_stack(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Tech Stack DataFrame from the Draup Solutions sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Tech Stack DataFrame.
        """
        data = sr.TechStack.default_data
        sheet_name = sr.TechStack.sheet_name
        tech_stack = self.sheet_df_map.get(draup.Solutions.sheet_name)

        if tech_stack is None or tech_stack.empty or not self.should_be_merged(draup.Solutions.sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        categories, tech_tools = self._get_category_tech_tools(tech_stack)
        data_ts = data.copy()
        data_ts["Category"] = categories
        data_ts["Technology Tools"] = tech_tools
        df = pd.DataFrame(data_ts)
        df = self._lowercase_drop_duplicates(df, "Technology Tools")
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def _expand_workloads_locations(self, df: pd.DataFrame) -> pd.DataFrame:
        """Expands the workloads and locations columns by splitting on semicolons.

        Args:
            df (pd.DataFrame): Input DataFrame containing 'workloads' and 'service_partner_locations' columns.

        Returns:
            pd.DataFrame: DataFrame with expanded workloads and locations.
        """
        data = df.copy()
        data["workloads"] = data["workloads"].str.split(";")
        data["service_partner_locations"] = data["service_partner_locations"].str.split(";")
        data = data.explode("workloads").reset_index(drop=True)
        data = data.explode("service_partner_locations").reset_index(drop=True)
        data["workloads"] = data["workloads"].str.strip()
        data["service_partner_locations"] = data["service_partner_locations"].str.strip()
        return data

    @check_types
    def compose_projects(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Projects DataFrame from Outsourcing Providers data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Projects DataFrame.
        """
        data = sr.Projects.default_data
        sheet_name = sr.Projects.sheet_name
        provider_sheet_name = draup.OutsourcingProviders.get_sheet_name(self.sheet_df_map.keys())
        outsourcing_providers = self.sheet_df_map.get(provider_sheet_name)
        if (
            outsourcing_providers is None
            or outsourcing_providers.empty
            or not self.should_be_merged(provider_sheet_name)
        ):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status("Outsourcing", NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "workloads": ";",
            "service_partner_locations": ";",
        }
        outsourcing_providers = self._expand_column_values(outsourcing_providers, column_delimiters)
        data_p = data.copy()
        data_p["Row ID"] = range(1, len(outsourcing_providers) + 1)
        data_p["Project Category"] = "Outsourcing"
        data_p["Company Name"] = outsourcing_providers.provider_name
        data_p["Project Description"] = outsourcing_providers.relationship_highlights
        data_p["Project Type"] = outsourcing_providers.workloads
        data_p["Location"] = outsourcing_providers.service_partner_locations
        data_p["Location ID"] = outsourcing_providers.service_partner_locations
        df = pd.DataFrame(data_p)
        self.set_table_status("Outsourcing", RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_outsourcing_top_metrics(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Outsourcing Top Metrics DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.OutsourcingTopMetrics.default_data
        sheet_name = sr.OutsourcingTopMetrics.sheet_name
        provider_sheet_name = draup.OutsourcingProviders.get_sheet_name(self.sheet_df_map.keys())
        outsourcing_providers = self.sheet_df_map.get(provider_sheet_name)

        if outsourcing_providers is None:
            df = pd.DataFrame(data)
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        outsourcing_providers_exp = self._expand_workloads_locations(outsourcing_providers)

        locations = (
            outsourcing_providers_exp
            .groupby("service_partner_locations")["num_active_workflows"]
            .sum()
            .reset_index()
        )  # fmt: skip
        locations = locations.sort_values(
            ["num_active_workflows", "service_partner_locations"],
            ascending=[False, True],
        )

        providers = outsourcing_providers.groupby("provider_name")["num_active_workflows"].sum().reset_index()
        providers = providers.sort_values(["num_active_workflows", "provider_name"], ascending=[False, True])

        locations = locations[locations["num_active_workflows"] > 1]
        providers = providers[providers["num_active_workflows"] > 1]

        locations = locations.head(3)
        providers = providers.head(3)

        if locations.empty and providers.empty:
            df = pd.DataFrame(data)
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_tom = data.copy()
        data_tom["Top Metrics Breakdown"] = ["Location"] * len(locations) + ["Provider"] * len(providers)
        data_tom["Metrics"] = locations.service_partner_locations.to_list() + providers.provider_name.to_list()
        data_tom["Location ID"] = locations.service_partner_locations.to_list() + [DEFAULT_LOC_ID] * len(providers)
        data_tom["# of Active Workflows"] = (
            locations.num_active_workflows.to_list() + providers.num_active_workflows.to_list()
        )
        df = pd.DataFrame(data_tom)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_postings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Postings DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITJobPostings.default_data
        sheet_name = sr.ITJobPostings.sheet_name
        job_postings = self.sheet_df_map.get("Job Postings")

        if job_postings is None or job_postings.empty or not self.should_be_merged(draup.JobPostings.sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jp = data.copy()
        column_delimiters = {
            "core_skills": ",",
            "location": ";",
        }
        job_postings = self._expand_column_values(job_postings, column_delimiters)
        data_jp["Row ID"] = range(1, len(job_postings) + 1)
        data_jp["Job Title"] = job_postings.job_title
        data_jp["Skills"] = job_postings.core_skills
        data_jp["Location"] = job_postings.location
        data_jp["Location ID"] = job_postings.location
        df = pd.DataFrame(data_jp)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_titles_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Titles in Demand DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.JobTitlesDemand.default_data
        sheet_name = sr.JobTitlesDemand.sheet_name
        job_titles_demand = self.sheet_df_map.get("Job Titles in Demand")

        if job_titles_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_jtd = data.copy()
        data_jtd["Row ID"] = range(1, len(job_titles_demand) + 1)
        data_jtd["Job Titles"] = job_titles_demand.job_titles
        data_jtd["# of Job Postings for 12 months"] = job_titles_demand.num_job_postings
        df = pd.DataFrame(data_jtd)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_locations(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Locations DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceLocations.default_data
        sheet_name = sr.ITWorkforceLocations.sheet_name
        hiring_map = self.sheet_df_map.get("Hiring Map")

        if hiring_map is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iwf = data.copy()
        data_iwf["Location"] = hiring_map.location
        data_iwf["Location ID"] = hiring_map.location
        data_iwf["# of Job Postings for 12 months"] = hiring_map.num_job_postings
        df = pd.DataFrame(data_iwf)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_it_workforce_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the IT Workforce Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ITWorkforceSkills.default_data
        sheet_name = sr.ITWorkforceSkills.sheet_name
        core_skills_demand = self.sheet_df_map.get("Core Skills in Demand")
        if core_skills_demand is None:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_iws = data.copy()
        data_iws["Skills"] = core_skills_demand.core_skills
        data_iws["# of Job Postings for 12 months"] = core_skills_demand.num_job_postings
        df = pd.DataFrame(data_iws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_competitors(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Competitors DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.Competitors.default_data
        sheet_name = sr.Competitors.sheet_name
        competitors = self.sheet_df_map.get("Competitor Information")

        if (
            competitors is None
            or competitors.empty
            or not self.should_be_merged(draup.CompetitorInformation.sheet_name)
        ):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_c = data.copy()
        data_c["Competitor Name"] = competitors.competitor_name
        df = pd.DataFrame(data_c)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_key_people(self) -> Tuple[str, pd.DataFrame]:
        data = sr.KeyPeople.default_data
        sheet_name = sr.KeyPeople.sheet_name
        key_executives = self.sheet_df_map.get("Key Executives")

        if key_executives is None or key_executives.empty or not self.should_be_merged(draup.KeyExecutives.sheet_name):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_kp = data.copy()
        data_kp["Row ID"] = range(1, len(key_executives) + 1)
        data_kp["Record Type"] = "Key People"
        data_kp["Name"] = key_executives.name
        data_kp["Job Title"] = key_executives.title
        data_kp["Job Title ID"] = key_executives.title
        data_kp["Location"] = key_executives.location
        data_kp["Location ID"] = key_executives.location
        data_kp["LinkedIn URL"] = key_executives.linkedin_url
        df = pd.DataFrame(data_kp)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_management_team_changes(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Management Team Changes sheet.

        Combines data from "Executive Movement Exits," "Executive Movement Hires," and
        "Executive Movement Promotions" sheets into a single DataFrame. If none of these
        sheets are present, it returns a default DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = sr.ManagementTeamChanges.default_data
        sheet_name = sr.ManagementTeamChanges.sheet_name
        m_exits = self.sheet_df_map.get("Executive Movement Exits")
        m_hires = self.sheet_df_map.get("Executive Movement Hires")
        m_promotions = self.sheet_df_map.get("Executive Movement Promotions")

        if all(df is None for df in (m_exits, m_hires, m_promotions)):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        if m_exits is not None and not m_exits.empty and self.should_be_merged(draup.ExecutiveMovementExits.sheet_name):
            data_me = data.copy()
            data_me["Name"] = m_exits.executive
            data_me["Job Title"] = m_exits.joining_position
            data_me["Movement Type"] = "Exit"
            data_me["Start Date"] = m_exits.exit_month
            data_me["Location"] = m_exits.joining_location
            data_me["Location ID"] = m_exits.joining_location
            data_me["Previous Position"] = m_exits.position_before_exit
            data_me["Joining Company"] = m_exits.joining_company
            transformed_sheets.append(pd.DataFrame(data_me))

        if m_hires is not None and not m_hires.empty and self.should_be_merged(draup.ExecutiveMovementHires.sheet_name):
            data_mh = data.copy()
            data_mh["Name"] = m_hires.executive
            data_mh["Job Title"] = m_hires.position_after_hire
            data_mh["Movement Type"] = "Hire"
            data_mh["Start Date"] = m_hires.hire_month
            data_mh["Location"] = m_hires.location_after_hire
            data_mh["Location ID"] = m_hires.location_after_hire
            data_mh["Previous Position"] = m_hires.position_before_hire
            data_mh["Previous Company"] = m_hires.company_before_hire
            transformed_sheets.append(pd.DataFrame(data_mh))

        if (
            m_promotions is not None
            and not m_promotions.empty
            and self.should_be_merged(draup.ExecutiveMovementPromotions.sheet_name)
        ):
            data_mp = data.copy()
            data_mp["Name"] = m_promotions.executive
            data_mp["Job Title"] = m_promotions.position_after_promotion
            data_mp["Movement Type"] = "Promotion"
            data_mp["Start Date"] = m_promotions.promotion_month
            data_mp["Location"] = m_promotions.location_after_promotion
            data_mp["Location ID"] = m_promotions.location_after_promotion
            data_mp["Previous Position"] = m_promotions.position_before_promotion
            transformed_sheets.append(pd.DataFrame(data_mp))

        if transformed_sheets:
            df = pd.concat(transformed_sheets).reset_index(drop=True)
            df["Row ID"] = range(1, len(df) + 1)
            self.set_table_status(sheet_name, RESEARCHED)
            return sheet_name, df
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

    @check_types
    def compose_swot_analysis(self) -> Tuple[str, pd.DataFrame]:
        """Composes the SWOT Analysis sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and a default SWOT Analysis DataFrame.
        """
        data = sr.SWOTAnalysis.default_data
        sheet_name = sr.SWOTAnalysis.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_infongen(self) -> Tuple[str, pd.DataFrame]:
        """Composes the InfoNgen sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and a default InfoNgen DataFrame.
        """
        data = sr.InfoNgen.default_data
        sheet_name = sr.InfoNgen.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_priorities(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Priorities sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and a default Priorities DataFrame.
        """
        data = sr.Priorities.default_data
        sheet_name = sr.Priorities.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Data Availability DataFrame.
        """
        return sr.DataAvailability.sheet_name, self.status_table


class DraupCompetitorsTransformer(Transformer, TransformationMixin):
    """Transforms Draup competitor data into the sales research format."""

    def __init__(
        self,
        sheet_df_map: Dict[str, DataFrame],
        llm_data_handler: LLMDataHandler,
    ) -> None:
        """Initializes DraupCompetitorsTransformer with sheet data and an LLM data handler.

        Args:
            sheet_df_map (Dict[str, DataFrame]): A dictionary mapping Draup sheet names to their respective DataFrames.
            llm_data_handler (LLMDataHandler): An instance of LLMDataHandler for location processing.
        """
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler
        self.status_table = pd.DataFrame(src.DataAvailability.default_data)
        self.transformations = (
            self.compose_general_overview,
            self.compose_funding_rounds,
            self.compose_financials,
            self.compose_strategy,
            self.compose_offerings,
            self.compose_relationships,
            self.compose_clients,
            self.compose_job_postings,
            self.compose_workforce_by_location,
            self.compose_workforce_by_skills,
            self.compose_job_titles_in_demand,
            self.compose_awards_and_recognitions,
            self.compose_additional_information,
            self.compose_infongen,
            self.compose_data_availability,
            self.compose_miscellaneous,
        )

    @check_types
    def transform(self) -> Dict[str, pd.DataFrame]:
        """Transforms the Draup competitor data into the sales research format.

        Returns:
            Dict[str, pd.DataFrame]: A dictionary mapping sales research sheet names to their transformed DataFrames.
        """
        sheet_df_map = {sheet_name: df for sheet_name, df in (compose_df() for compose_df in self.transformations)}
        sheet_df_map = self._replace_location_ids(
            sheet_df_map,
            sheets={
                src.GeneralOverview.sheet_name,
                src.JobPostings.sheet_name,
                src.WorkforceByLocation.sheet_name,
            },
        )
        sheet_df_map = self.aggregate_tables(sheet_df_map)
        return sheet_df_map

    def set_table_status(self, category_name, status):
        """Sets the data availability status for a given category/table.

        Args:
            category_name (str): The name of the category/table.
            status (str): The status to set (e.g., "RESEARCHED", "NOT_RESEARCHED").
        """
        table_match = self.status_table[src.DataAvailability.table] == category_name
        status_not_set = self.status_table[src.DataAvailability.status].isnull()
        self.status_table.loc[table_match & status_not_set, src.DataAvailability.status] = status
        return None

    @check_types
    def compose_general_overview(self) -> Tuple[str, pd.DataFrame]:
        """Composes the General Overview DataFrame from Draup competitor data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the General Overview DataFrame.
        """
        about = self.sheet_df_map["About"]
        general_overview = self.sheet_df_map["General Overview"]
        fin_trend = self.sheet_df_map.get("Financials Trend")
        latest_fin_year = None
        if fin_trend is not None:
            # extracting the latest financial year data
            latest_fin_year = fin_trend[fin_trend["financial_metric"] == fin_trend["financial_metric"].max()]
            assert len(latest_fin_year) == 1, "DataFrame does not have 1 row"
            draup.FinancialsTrend.validate(latest_fin_year)

        # composing output dataframe
        data = src.GeneralOverview.default_data
        data["Account"] = about.account.iloc[0]
        data["About"] = about.about.iloc[0] if "about" in about.columns else None
        data["HQ Location"] = general_overview.hq_location.iloc[0]
        data["Location ID"] = general_overview.hq_location.iloc[0]
        data["Industry"] = general_overview.primary_vertical.iloc[0]

        # total workforce is not always available
        if "total_workforce" in general_overview.columns:
            data["Number of Employees"] = general_overview.total_workforce.iloc[0]

        data["Company Type"] = general_overview.company_type.iloc[0]
        data["Website"] = general_overview.website.iloc[0]

        # private companies do not provide financial data
        if data["Company Type"] == "PUBLIC" and latest_fin_year is not None:
            data["Revenue (USD)"] = latest_fin_year.revenue.iloc[0]

        sheet_name = src.GeneralOverview.sheet_name
        df = pd.DataFrame(data, index=[0])
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_funding_rounds(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Funding Rounds DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty Funding Rounds DataFrame.
        """
        sheet_name = src.Fundings.sheet_name
        df = pd.DataFrame(src.Fundings.default_data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_financials(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Financials DataFrame from various Draup financial sheets.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the combined Financials DataFrame.
        """
        fin_trend = self.sheet_df_map.get("Financials Trend")
        revenue_business = self.sheet_df_map.get("Revenue by Business")
        revenue_region = self.sheet_df_map.get("Revenue by Region")
        sheet_name = src.Financials.sheet_name

        data = src.Financials.default_data
        if all(df is None for df in (fin_trend, revenue_business, revenue_region)):
            # no financial data available, default values
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        transformed_sheets = []
        # handling Financial Trends data
        if fin_trend is not None:
            data_ft = data.copy()
            data_ft["Year"] = fin_trend.financial_metric
            data_ft["Financial Metrics"] = "Yearly Total"
            data_ft["Revenue (USD)"] = fin_trend.revenue
            data_ft["EBITDA (USD)"] = fin_trend.ebitda
            transformed_sheets.append(pd.DataFrame(data_ft))

        # handling Revenue by Business data
        if revenue_business is not None:
            data_rb = data.copy()
            data_rb["Year"] = revenue_business.financial_year
            data_rb["Financial Metrics"] = "Revenue by Business"
            data_rb["Category of Financial Metrics"] = revenue_business.business
            data_rb["Revenue (USD)"] = revenue_business.revenue
            transformed_sheets.append(pd.DataFrame(data_rb))

        # handling Revenue by Region data
        if revenue_region is not None:
            data_rr = data.copy()
            data_rr["Year"] = revenue_region.financial_year
            data_rr["Financial Metrics"] = "Revenue by Region"
            data_rr["Category of Financial Metrics"] = revenue_region.region
            data_rr["Revenue (USD)"] = revenue_region.revenue
            transformed_sheets.append(pd.DataFrame(data_rr))

        df = pd.concat(transformed_sheets)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_strategy(self):
        """Composes the Strategy DataFrame, focusing on "Focus Areas" from Draup data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Strategy DataFrame.
        """
        data = src.Strategy.default_data
        sheet_name = src.Strategy.sheet_name
        business_intentions = self.sheet_df_map.get("Business Intentions")
        account_priorities = self.sheet_df_map.get("Account Priorities")

        transformed_sheets = []
        if business_intentions is not None or account_priorities is not None:
            if business_intentions is not None:
                data_bi = data.copy()
                data_bi["Category"] = "Focus Areas"
                data_bi["Title"] = business_intentions.use_case
                data_bi["Description"] = business_intentions.digital_initiative_description
                transformed_sheets.append(pd.DataFrame(data_bi))

            if account_priorities is not None:
                data_ap = data.copy()
                data_ap["Category"] = "Focus Areas"
                data_ap["Title"] = account_priorities.priority
                data_ap["Description"] = account_priorities.description
                transformed_sheets.append(pd.DataFrame(data_ap))
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        df = pd.concat(transformed_sheets)

        categories = ["Focus Areas"]
        s_categories = set(df["Category"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                self.set_table_status(category, NO_INFO)

        self.set_table_status("Risks and Challenges", NOT_RESEARCHED)
        self.set_table_status("Market Positioning", NOT_RESEARCHED)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_offerings(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Offerings DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty Offerings DataFrame.
        """
        sheet_name = src.Offerings.sheet_name
        df = pd.DataFrame(src.Offerings.default_data, index=[0])
        self.set_table_status("Offerings by Industry", NOT_RESEARCHED)
        self.set_table_status("Offerings by Services", NOT_RESEARCHED)
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_relationships(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Relationships DataFrame from Acquisitions and Investments data.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Relationships DataFrame.
        """
        data = src.Relationships.default_data
        sheet_name = src.Relationships.sheet_name
        acquisition = self.sheet_df_map.get("Acquisitions Details")
        investment = self.sheet_df_map.get("Investments Details")

        transformed_sheets = []
        if acquisition is not None or investment is not None:
            if investment is not None:
                data_in = data.copy()
                data_in["Year"] = pd.Series(
                    [date.year if hasattr(date, "year") else int(date[-4:]) for date in investment.date]
                )
                data_in["Category"] = "Investment"
                data_in["Company Name"] = investment.organization_name
                data_in["Additional Information"] = investment.amount
                data_in["Additional Information"] = data_in["Additional Information"].apply(lambda x: f"Amount: {x}")
                transformed_sheets.append(pd.DataFrame(data_in))
            if acquisition is not None:
                data_ac = data.copy()
                data_ac["Year"] = pd.Series(
                    [date.year if hasattr(date, "year") else int(date[-4:]) for date in acquisition.announced_date]
                )
                data_ac["Category"] = "Acquisition"
                data_ac["Company Name"] = acquisition.acquired
                data_ac["Additional Information"] = acquisition.acquistion_size
                transformed_sheets.append(pd.DataFrame(data_ac))
        else:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        df = pd.concat(transformed_sheets)

        categories = [
            "Acquisitions",
            "Investments",
        ]
        s_categories = set(df["Category"].unique())
        for category in categories:
            category_exists = any([x in category for x in s_categories])
            if category_exists:
                self.set_table_status(category, RESEARCHED)
            else:
                self.set_table_status(category, NO_INFO)

        self.set_table_status("Partnerships", NOT_RESEARCHED)
        self.set_table_status("Mergers", NOT_RESEARCHED)

        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_clients(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Clients DataFrame from Engagement Details.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed Clients DataFrame.
        """
        data = src.Clients.default_data
        sheet_name = src.Clients.sheet_name
        clients = self.sheet_df_map.get("Engagement Details")

        if clients is None or clients.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_cl = data.copy()
        data_cl["Company Name"] = clients.customer
        data_cl["EPAM Status"] = clients.epam_status
        data_cl["Additional Information"] = clients.workloads

        df = pd.DataFrame(data_cl)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_postings(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Postings DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.JobPostings.default_data
        sheet_name = src.JobPostings.sheet_name
        jobs = self.sheet_df_map.get("Job Postings")

        if jobs is None or jobs.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        column_delimiters = {
            "core_skills": ",",
            "location": ",",
        }
        jobs = self._expand_column_values(jobs, column_delimiters)

        data_jp = data.copy()
        data_jp["Job Title"] = jobs.job_title
        data_jp["Skills"] = jobs.core_skills
        data_jp["Location"] = jobs.location
        data_jp["Location ID"] = jobs.location

        df = pd.DataFrame(data_jp)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_workforce_by_location(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Workforce by Location DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceByLocation.default_data
        sheet_name = src.WorkforceByLocation.sheet_name
        workforce_by_location = self.sheet_df_map.get("Hiring Map")

        if workforce_by_location is None or workforce_by_location.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_wl = data.copy()
        data_wl["Location"] = workforce_by_location.location
        data_wl["Location ID"] = workforce_by_location.location
        data_wl["Job Posts"] = workforce_by_location.num_job_postings

        df = pd.DataFrame(data_wl)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_workforce_by_skills(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Workforce by Skills DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.WorkforceBySkills.default_data
        sheet_name = src.WorkforceBySkills.sheet_name
        workforce_by_skills = self.sheet_df_map.get("Core Skills in Demand")

        if workforce_by_skills is None or workforce_by_skills.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_ws = data.copy()
        data_ws["Skills"] = workforce_by_skills.core_skills
        data_ws["Job Posts"] = workforce_by_skills.num_job_postings

        df = pd.DataFrame(data_ws)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_job_titles_in_demand(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Job Titles in Demand DataFrame.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the composed DataFrame.
        """
        data = src.JobTitlesInDemand.default_data
        sheet_name = src.JobTitlesInDemand.sheet_name
        job_titles_in_demand = self.sheet_df_map.get("Job Titles in Demand")

        if job_titles_in_demand is None or job_titles_in_demand.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(sheet_name, NOT_RESEARCHED)
            return sheet_name, df

        data_td = data.copy()
        data_td["Title"] = job_titles_in_demand.job_titles
        data_td["Job Posts"] = job_titles_in_demand.num_job_postings

        df = pd.DataFrame(data_td)
        df["Row ID"] = range(1, len(df) + 1)
        self.set_table_status(sheet_name, RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_awards_and_recognitions(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Awards and Recognitions DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        sheet_name = src.AwardsAndRecognitions.sheet_name
        df = pd.DataFrame(src.AwardsAndRecognitions.default_data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_additional_information(self) -> Tuple[str, pd.DataFrame]:
        """Composes a default Additional Information DataFrame (not researched).

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and an empty DataFrame.
        """
        sheet_name = src.AdditionalInformation.sheet_name
        df = pd.DataFrame(src.AdditionalInformation.default_data, index=[0])
        self.set_table_status(sheet_name, NOT_RESEARCHED)
        return sheet_name, df

    @check_types
    def compose_infongen(self) -> Tuple[str, pd.DataFrame]:
        """Composes the InfoNgen DataFrame with default data.

        Returns:
            Tuple[str, pd.DataFrame]: Sheet name and default DataFrame.
        """
        data = src.InfoNgen.default_data
        sheet_name = src.InfoNgen.sheet_name
        df = pd.DataFrame(data, index=[0])
        return sheet_name, df

    def compose_miscellaneous(self) -> Tuple[str, pd.DataFrame]:
        """Composes the Miscellaneous sheet, handling logo, report, and infongen information.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the Miscellaneous DataFrame.
        """
        sheet_name = mrc.Miscellaneous.sheet_name
        misc = self.sheet_df_map.get(mrc.Miscellaneous.sheet_name)
        data = {
            "logo_url": "-",
            "draup_report": "-",
            "infongen": "-",
        }

        infongen = src.InfoNgen.sheet_name

        if misc is None or misc.empty:
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(infongen, NOT_RESEARCHED)
            return sheet_name, df
        elif (
            misc.logo_url.isin([None, ""]).all()
            and misc.draup_report.isin([None, ""]).all()
            and misc.infongen.isin([None, ""]).all()
        ):
            df = pd.DataFrame(data, index=[0])
            self.set_table_status(infongen, NOT_RESEARCHED)
            return sheet_name, df
        elif (
            misc.logo_url.isin(["n/a", "-"]).all()
            and misc.draup_report.isin(["n/a", "-"]).all()
            and misc.infongen.isin(["n/a", "-"]).all()
        ):
            self.set_table_status(infongen, NO_INFO)
        else:
            self.set_table_status(infongen, RESEARCHED)

        return sheet_name, misc

    @check_types
    def compose_data_availability(self) -> Tuple[str, pd.DataFrame]:
        """Returns the Data Availability sheet.

        Returns:
            Tuple[str, pd.DataFrame]: A tuple containing the sheet name and the status table.
        """
        return src.DataAvailability.sheet_name, self.status_table
