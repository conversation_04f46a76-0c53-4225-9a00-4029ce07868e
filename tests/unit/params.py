from datetime import date
from datetime import datetime as dt

import numpy as np
import pandas as pd

from se_data_pipeline.component.constants import DRAUP_NOTES


test_replace_with_nan_values = (
    "sheet_df_map, expected_sheet1, expected_sheet2",
    [
        (
            {
                "Sheet1": pd.DataFrame(),
                "Sheet2": pd.DataFrame({"A": ["n/a", "-", 1], "B": ["-", "n/a", 4]}),
            },
            pd.DataFrame(),
            pd.DataFrame({"A": [np.nan, np.nan, 1], "B": [np.nan, np.nan, 4]}),
        ),
    ],
)

test_fill_currency_code = (
    "input_sheet_df_map, financial_tables, expected_sheet_df_map",
    [
        (
            {
                "Sheet1": pd.DataFrame({"Revenue (in USD)": [100, 200], "Expenses (USD)": [50, 75]}),
                "Sheet2": pd.DataFrame({"Income (EUR)": [300, 400], "Taxes": [20, 30]}),
                "Sheet3": pd.DataFrame({"Sales": [150, 250], "Costs (USD)": [100, 150]}),
            },
            {"Sheet1", "Sheet2", "Sheet3"},
            {
                "Sheet1": pd.DataFrame({"Revenue": [100, 200], "Expenses": [50, 75], "currency_code": ["USD", "USD"]}),
                "Sheet2": pd.DataFrame({"Income": [300, 400], "Taxes": [20, 30], "currency_code": ["EUR", "EUR"]}),
                "Sheet3": pd.DataFrame({"Sales": [150, 250], "Costs": [100, 150], "currency_code": ["USD", "USD"]}),
            },
        ),
    ],
)

test_apply_financial_to_int_conversion = (
    "sheet_df_map, fin_sheets, expected_sheet_df_map",
    [
        (
            {
                "Sheet1": pd.DataFrame({"Revenue": ["10 M", "20 B"], "Expenses": ["5 M", "750 Mn"]}),
                "Sheet2": pd.DataFrame({"Income": ["300 M", "4 B"], "Taxes": ["20 Mn", "30 Bn"]}),
            },
            {"Sheet1", "Sheet2", "Sheet3"},
            {
                "Sheet1": pd.DataFrame({"Revenue": [10000000, 20000000000], "Expenses": [5000000, *********]}),
                "Sheet2": pd.DataFrame({"Income": [*********, 4000000000], "Taxes": [20000000, *********00]}),
            },
        ),
    ],
)

test_normalise_column_names = (
    "input_columns, expected_columns",
    [
        (["column_one", "column_two"], ["column_one", "column_two"]),
        (["Column One", "Column Two"], ["column_one", "column_two"]),
        ([1, 2], [1, 2]),
        ([], []),
    ],
)

test_set_project_dates = (
    "sheet_df_map, sheets, expected_sheet1, expected_sheet2",
    [
        (
            {
                "Sheet1": pd.DataFrame({"date": ["March 2022 - April 2023", "2021", "01.05.2023"]}),
                "Sheet2": pd.DataFrame({"date": ["March 2022 - Present", "Invalid Date"]}),
            },
            {"Sheet1", "Sheet2"},
            pd.DataFrame(
                {
                    "date": ["March 2022 - April 2023", "2021", "01.05.2023"],
                    "start_date": [date(2022, 3, 1), None, None],
                    "end_date": [date(2023, 4, 1), date(2021, 1, 1), date(2023, 5, 1)],
                }
            ),
            pd.DataFrame(
                {
                    "date": ["March 2022 - Present", "Invalid Date"],
                    "start_date": [date(2022, 3, 1), None],
                    "end_date": [None, None],
                }
            ),
        ),
    ],
)

test_set_top_date = (
    "sheet_df_map, sheets, expected_sheet_df_map",
    [
        (
            {
                "Funding Rounds": pd.DataFrame({"year": ["n/a", 2020, "-"], "amount": [100, 200, 300]}),
                "Partnerships": pd.DataFrame({"date": ["2021-01-01", "n/a", "2022-01-01"], "partner": ["A", "B", "C"]}),
                "Acquisitions": pd.DataFrame({"date": ["-", "2023-01-01", "N/A"], "company": ["X", "Y", "Z"]}),
            },
            [
                ("Funding Rounds", "year", 9999),
                ("Partnerships", "date", dt.strptime("01/01/9999", "%m/%d/%Y").date()),
                ("Acquisitions", "date", dt.strptime("01/01/9999", "%m/%d/%Y").date()),
            ],
            {
                "Funding Rounds": pd.DataFrame({"year": [9999, 2020, 9999], "amount": [100, 200, 300]}),
                "Partnerships": pd.DataFrame(
                    {
                        "date": [
                            pd.Timestamp("2021-01-01").date(),
                            pd.Timestamp("9999-01-01").date(),
                            pd.Timestamp("2022-01-01").date(),
                        ],
                        "partner": ["A", "B", "C"],
                    }
                ),
                "Acquisitions": pd.DataFrame(
                    {
                        "date": [
                            pd.Timestamp("9999-01-01").date(),
                            pd.Timestamp("2023-01-01").date(),
                            pd.Timestamp("9999-01-01").date(),
                        ],
                        "company": ["X", "Y", "Z"],
                    }
                ),
            },
        ),
    ],
)

test_drop_nan_rows = (
    "input_dfs, expected_dfs",
    [
        (
            {
                "sheet1": pd.DataFrame({"A": [1, 2, None, 4], "B": [5, 6, 7, 8]}),
                "sheet2": pd.DataFrame({"A": [None, 2, 3, 4], "B": [5, None, 7, 8]}),
            },
            {
                "sheet1": pd.DataFrame({"A": [1, 2, 4], "B": [5, 6, 8]}),
                "sheet2": pd.DataFrame({"A": [2, 3, 4], "B": [None, 7, 8]}),
            },
        )
    ],
)

test_drop_rows_with_strings = (
    "sheet_df_map, expected_sheet_df_map",
    [
        (
            {
                "Sheet1": pd.DataFrame({"A": ["data1", DRAUP_NOTES[0], "data3"], "B": [1, 2, 3]}),
                "Sheet2": pd.DataFrame({"A": ["data4", "data5", DRAUP_NOTES[1]], "B": [4, 5, 6]}),
            },
            {
                "Sheet1": pd.DataFrame({"A": ["data1", "data3"], "B": [1, 3]}).reset_index(drop=True),
                "Sheet2": pd.DataFrame({"A": ["data4", "data5"], "B": [4, 5]}).reset_index(drop=True),
            },
        )
    ],
)

test_normalise_date_cols = (
    "sheet_name, date_values, expected",
    [
        ("Partnerships", ["2024-01-01", "2024-02-15"], [dt(2024, 1, 1).date(), dt(2024, 2, 15).date()]),
        ("Acquisitions", ["2023-06-30", "2023-12-25"], [dt(2023, 6, 30).date(), dt(2023, 12, 25).date()]),
        ("Mergers", [pd.NaT, "2022-11-10"], [pd.NaT, dt(2022, 11, 10).date()]),
        ("Investments", [pd.NaT, "2021-07-04"], [pd.NaT, dt(2021, 7, 4).date()]),
        ("Additional Information", [], []),
    ],
)

test_expand_column_values = (
    "df, column_delimiter, expected",
    [
        (
            pd.DataFrame({"investors": ["John, Doe"], "amount": [100]}),
            {"investors": ","},
            pd.DataFrame({"investors": ["John", "Doe"], "amount": [100, 100]}),
        ),
        (
            pd.DataFrame({"investors": ["John Doe;  Jane"], "amount": [100]}),
            {"investors": ";"},
            pd.DataFrame({"investors": ["John Doe", "Jane"], "amount": [100, 100]}),
        ),
        (
            pd.DataFrame({"investors": [""], "amount": [100]}),
            {"investors": ","},
            pd.DataFrame({"investors": [""], "amount": [100]}),
        ),
        (
            pd.DataFrame({"investors": [None], "amount": [100]}),
            {"investors": ","},
            pd.DataFrame({"investors": [None], "amount": [100]}),
        ),
        (
            pd.DataFrame({"investors": ["John; Doe", "Jane, Doe"], "amount": [100, 200]}),
            {"investors": ","},
            pd.DataFrame({"investors": ["John; Doe", "Jane", "Doe"], "amount": [100, 200, 200]}),
        ),
    ],
)
