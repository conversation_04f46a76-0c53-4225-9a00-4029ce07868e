import pandas as pd
import pytest

from tests.unit import params


class TestTransformationMixin:

    @pytest.fixture(autouse=True)
    def setup(self, transformation_mixin):
        self.transformation_mixin = transformation_mixin

    @pytest.mark.parametrize(*params.test_expand_column_values)
    def test_expand_column_values(self, df, column_delimiter, expected):
        test_instance = self.transformation_mixin(df)
        actual = test_instance._expand_column_values(df, column_delimiter)
        pd.testing.assert_frame_equal(actual, expected)
