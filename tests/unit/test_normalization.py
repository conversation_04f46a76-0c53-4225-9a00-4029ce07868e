import pandas as pd
import pytest

from se_data_pipeline.component.constants import NOT_RESEARCHED, NO_INFO
from tests.unit import params


class TestNormalizationMixin:

    @pytest.fixture(autouse=True)
    def setup(self, normalization_mixin):
        self.normalization_mixin = normalization_mixin

    def test_set_column_names(self):
        sheet_df_map = {
            "Sheet1": pd.DataFrame([["A", "B", "C"], [1, 2, 3], [4, 5, 6]], columns=[0, 1, 2]),
            "Miscellaneous": pd.DataFrame([["X", "Y", "Z"], [7, 8, 9]], columns=[0, 1, 2]),
            "Sheet2": pd.DataFrame([["D", "E", "F"], [10, 11, 12], [13, 14, 15]], columns=[0, 1, 2]),
        }
        test_instance = self.normalization_mixin(sheet_df_map)
        test_instance.set_column_names()

        assert list(test_instance.sheet_df_map["Sheet1"].columns) == ["A", "B", "C"]
        assert test_instance.sheet_df_map["Miscellaneous"].equals(pd.DataFrame([["X", "Y", "Z"], [7, 8, 9]]))
        assert list(test_instance.sheet_df_map["Sheet2"].columns) == ["D", "E", "F"]

    def test_derive_table_status(self):
        sheet_df_map = {
            "Sheet1": pd.DataFrame(),
            "Sheet2": pd.DataFrame({"A": ["n/a", "-"], "B": ["-", "n/a"]}),
            "Sheet3": pd.DataFrame({"A": ["1", "2"], "B": ["3", "4"]}),
            "Sheet4": pd.DataFrame({"A": ["n/a", "2"], "B": ["3", "-"]}),
            "Data Availability": pd.DataFrame(
                {"table": ["Sheet1", "Sheet2", "Sheet3", "Sheet4"], "status": [None, None, None, None]}
            ),
        }

        test_instance = self.normalization_mixin(sheet_df_map, sheet_df_map["Data Availability"])
        test_instance.derive_table_status()

        assert (
            test_instance.status_table.loc[test_instance.status_table["table"] == "Sheet1", "status"].values[0]
            == NOT_RESEARCHED
        )
        assert (
            test_instance.status_table.loc[test_instance.status_table["table"] == "Sheet2", "status"].values[0]
            == NO_INFO
        )
        assert test_instance.sheet_df_map["Sheet2"].empty
        assert test_instance.status_table.loc[test_instance.status_table["table"] == "Sheet3", "status"].isnull().all()
        assert test_instance.status_table.loc[test_instance.status_table["table"] == "Sheet4", "status"].isnull().all()

    @pytest.mark.parametrize(*params.test_replace_with_nan_values)
    def test_replace_with_nan_values(self, sheet_df_map, expected_sheet1, expected_sheet2):
        test_instance = self.normalization_mixin(sheet_df_map)
        test_instance.replace_with_nan_values()

        pd.testing.assert_frame_equal(sheet_df_map["Sheet1"], expected_sheet1)
        pd.testing.assert_frame_equal(sheet_df_map["Sheet2"], expected_sheet2)

    @pytest.mark.parametrize(*params.test_fill_currency_code)
    def test_fill_currency_code(self, input_sheet_df_map, financial_tables, expected_sheet_df_map):
        test_instance = self.normalization_mixin(input_sheet_df_map)
        test_instance.fill_currency_code(financial_tables)

        for sheet_name in input_sheet_df_map:
            pd.testing.assert_frame_equal(test_instance.sheet_df_map[sheet_name], expected_sheet_df_map[sheet_name])

    @pytest.mark.parametrize(*params.test_apply_financial_to_int_conversion)
    def test_apply_financial_to_int_conversion(self, sheet_df_map, fin_sheets, expected_sheet_df_map):
        test_instance = self.normalization_mixin(sheet_df_map)
        test_instance.apply_financial_to_int_conversion(fin_sheets=fin_sheets)

        for sheet_name in expected_sheet_df_map.keys():
            pd.testing.assert_frame_equal(test_instance.sheet_df_map[sheet_name], expected_sheet_df_map[sheet_name])

    @pytest.mark.parametrize(*params.test_normalise_column_names)
    def test_normalise_column_names(self, input_columns, expected_columns):
        df = pd.DataFrame({col: [] for col in input_columns})
        sheet_df_map = {"sheet1": df}
        test_instance = self.normalization_mixin(sheet_df_map)
        test_instance.normalise_column_names()
        assert list(df.columns) == expected_columns

    @pytest.mark.parametrize(*params.test_set_project_dates)
    def test_set_project_dates(self, sheet_df_map, sheets, expected_sheet1, expected_sheet2):
        project_instance = self.normalization_mixin(sheet_df_map)
        project_instance.set_project_dates(sheets)

        pd.testing.assert_frame_equal(sheet_df_map["Sheet1"], expected_sheet1)
        pd.testing.assert_frame_equal(sheet_df_map["Sheet2"], expected_sheet2)

    @pytest.mark.parametrize(*params.test_set_top_date)
    def test_set_top_date(self, sheet_df_map, sheets, expected_sheet_df_map):
        project_instance = self.normalization_mixin(sheet_df_map)
        project_instance._set_top_date(sheets)

        for sheet_name, expected_df in expected_sheet_df_map.items():
            if "date" in expected_df.columns:
                expected_df["date"] = expected_df["date"].apply(
                    lambda x: x.date() if isinstance(x, pd.Timestamp) else x
                )
            if "date" in sheet_df_map[sheet_name].columns:
                sheet_df_map[sheet_name]["date"] = sheet_df_map[sheet_name]["date"].apply(
                    lambda x: pd.to_datetime(x).date() if isinstance(x, str) else x
                )

            pd.testing.assert_frame_equal(sheet_df_map[sheet_name], expected_df, check_dtype=False, check_exact=True)

    @pytest.mark.parametrize(*params.test_drop_nan_rows)
    def test_drop_nan_rows(self, input_dfs, expected_dfs):
        test_instance = self.normalization_mixin(input_dfs)
        test_instance.drop_nan_rows()

        for sheet_name in input_dfs.keys():
            pd.testing.assert_frame_equal(
                input_dfs[sheet_name].reset_index(drop=True),
                expected_dfs[sheet_name].reset_index(drop=True),
                check_index_type=False,
                check_dtype=False,
            )

    @pytest.mark.parametrize(*params.test_drop_rows_with_strings)
    def test_drop_rows_with_strings(self, sheet_df_map, expected_sheet_df_map):
        test_instance = self.normalization_mixin(sheet_df_map)
        test_instance.drop_rows_with_strings()

        for sheet_name in sheet_df_map.keys():
            pd.testing.assert_frame_equal(sheet_df_map[sheet_name], expected_sheet_df_map[sheet_name])

    @pytest.mark.parametrize(*params.test_normalise_date_cols)
    def test_normalise_date_cols(self, sheet_name, date_values, expected):

        sheet_df_map = {sheet_name: pd.DataFrame({"date": date_values})}
        test_instance = self.normalization_mixin(sheet_df_map)

        format_mixed = {"format": "mixed"}
        date_sheets = {
            ("Partnerships", "date"): format_mixed,
            ("Acquisitions", "date"): format_mixed,
            ("Mergers", "date"): format_mixed,
            ("Investments", "date"): format_mixed,
            ("Additional Information", "date"): format_mixed,
        }

        test_instance._normalise_date_cols(date_sheets)

        assert test_instance.sheet_df_map[sheet_name]["date"].tolist() == expected
