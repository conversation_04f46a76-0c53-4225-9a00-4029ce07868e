import pytest

from se_data_pipeline.component.base import NormalizationMixin, TransformationMixin


@pytest.fixture
def normalization_mixin():
    """Fixture for a test class using NormalizationMixin with mocked LLMDataHandler"""

    class TestClass(NormalizationMixin):
        def __init__(self, sheet_df_map, status_table=None):
            self.sheet_df_map = sheet_df_map
            self.status_table = status_table

        def set_table_status(self, category_name, status):
            table_match = self.status_table["table"] == category_name
            status_not_set = self.status_table["status"].isnull()
            self.status_table.loc[table_match & status_not_set, "status"] = status
            return None

    return TestClass


@pytest.fixture
def transformation_mixin():
    """Fixture for a test class using TransformationMixin with mocked LLMDataHandler"""

    class TestClass(TransformationMixin):
        def __init__(self, sheet_df_map):
            self.sheet_df_map = sheet_df_map

    return TestClass
