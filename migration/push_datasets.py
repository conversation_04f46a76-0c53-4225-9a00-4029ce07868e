import pandas as pd
from se_data_pipeline.component.utils import init_data_bridge
from quanthub.transformations.utils import init_local_config
from quanthub_pipeline.system import data_api as qh
from typing import Dict

data_bridge = init_data_bridge()

WORKSPACE = "Presales:SHADOW_TESTING"
processed_path = "migration/processed_datasets/"

init_local_config(WORKSPACE)
pandas_reader = qh.create_pandas_reader()
pandas_writer = qh.create_pandas_writer()


def attach_data_from_files(new_to_old_dataset_ids: Dict[str, str]) -> Dict[str, pd.DataFrame]:
    df_map = {}
    for new, old in new_to_old_dataset_ids.items():
        df_old = pd.read_csv(f"{processed_path}{old}.csv")
        df_map[new] = df_old
    return df_map


new_to_old_dataset_ids = {
    "SE_A:ADDITIONAL_INFORMATION(1.0.0)": "SR-ADDITIONAL_INFORMATION(3.0.0)",
    "SE_A:BUSINESS_STRUCTURE(1.0.0)": "SR-BUSINESS_STRUCTURE(7.0.0)",
    "SE_A:COMPANY_ENTITIES(1.0.0)": "SR-COMPANY_ENTITIES(1.0.0)",
    "SE_A:COMPETITORS(1.0.0)": "SR-COMPETITORS(9.1.0)",
    "SE_A:CURRENT_ENGAGEMENTS(1.0.0)": "SR-CURRENT_ENGAGEMENTS(9.0.0)",
    "SE_A:DATA_AVAILABILITY(1.0.0)": "SR-DATA_AVAILABILITY(2.0.0)",
    "SE_A:DRIVERS_AND_PRIORITIES(1.0.0)": "SR-DRIVERS_AND_PRIORITIES(3.0.0)",
    "SE_A:EVENTS(1.0.0)": "SR-EVENTS(9.0.0)",
    # "SE_A:FINANCIALS_BY_COMPANY(1.0.0)": "SR-FINANCIALS(8.0.2)",
    # "SE_A:FINANCIALS_BY_COMPANY_AND_BUSINESS_VERTICAL(1.0.0)": "SR-FINANCIALS(8.0.2)",
    # "SE_A:FINANCIALS_BY_COMPANY_AND_LOCATION(1.0.0)": "SR-FINANCIALS(8.0.2)",
    "SE_A:FINANCIALS_RAW(1.0.0)": "SR-FINANCIALS(8.0.2)",
    "SE_A:FEATURED_CLIENTS(1.0.0)": "SR-FEATURED_CLIENTS(3.0.0)",
    "SE_A:FUNDING_ROUNDS(1.0.0)": "SR-FUNDINGS(3.0.0)",
    "SE_A:GENERAL_OVERVIEW(1.0.0)": "SR-GENERAL_OVERVIEW(15.0.0)",
    "SE_A:INFONGEN(1.0.0)": "SR-INFONGEN(1.0.0)",
    "SE_A:IT_JOB_POSTINGS(1.0.0)": "SR-IT_JOB_POSTINGS(10.0.0)",
    "SE_A:IT_WORKFORCE_LOCATIONS(1.0.0)": "SR-IT_WORKFORCE_LOCATIONS(10.0.0)",
    "SE_A:IT_WORKFORCE_SKILLS(1.0.0)": "SR-IT_WORKFORCE_SKILLS(7.0.0)",
    "SE_A:JOB_TITLES_IN_DEMAND(1.0.0)": "SR-JOB_TITLES_IN_DEMAND(6.0.0)",
    "SE_A:KEY_PEOPLE(1.0.0)": "SR-KEY_PEOPLE(11.0.0)",
    "SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)": "SR-MANAGEMENT_TEAM_CHANGES(8.0.0)",
    "SE_A:PROJECTS(1.0.0)": "SR-PROJECTS(11.0.0)",
    "SE_A:SWOT_ANALYSIS(1.0.0)": "SR-SWOT_ANALYSIS(1.0.0)",
    "SE_A:TECHNOLOGY_STACK(1.0.0)": "SR-TECHNOLOGY_STACK(9.0.0)",
    # "SE_A:TOP_OUTSOURCING_METRICS_LOCATIONS(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
    # "SE_A:TOP_OUTSOURCING_METRICS_PROVIDERS(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
    "SE_A:TOP_OUTSOURCING_METRICS_RAW(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
}

df_map = attach_data_from_files(new_to_old_dataset_ids)


def write_to_workspace():
    for dataset_id, df in df_map.items():
        print(f"Writing {dataset_id} to {WORKSPACE}...")
        pandas_writer.write(WORKSPACE, dataset_id, df)
    print("All datasets written to workspace successfully")


if __name__ == "__main__":
    write_to_workspace()
