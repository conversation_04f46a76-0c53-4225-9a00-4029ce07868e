from quanthub.utils.config import configure_quanthub

configure_quanthub()
from quanthub.structures import ArtefactsMigrator, create_version_increment_client
from quanthub.transformations.utils import init_local_config

from se_data_pipeline.component.utils import init_data_bridge

SHADOW_TESTING = "Presales:SHADOW_TESTING"
init_local_config(SHADOW_TESTING)

if __name__ == "__main__":
    version_client = create_version_increment_client()
    data_bridge = init_data_bridge()

    migrations_module_path = "migration"

    migrator = ArtefactsMigrator(SHADOW_TESTING, migrations_module_path, data_bridge, version_client)
    migrator.run()
