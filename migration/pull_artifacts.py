from quanthub.structures import Arte<PERSON>cts<PERSON><PERSON><PERSON><PERSON>ator, QuantHubDataBridge, create_version_increment_client
from quanthub.transformations.utils import init_local_config
from se_data_pipeline.component.utils import init_data_bridge

__CS_NAMES = {
    "SDMX:CROSS_DOMAIN_CONCEPTS": "sdmx",
    "EPAM:SALES_RESEARCH": "sales_research",
}

__DSD_URNS = {
    "general_overview": "SR:GENERAL_OVERVIEW(15.0.0)",
    "funding_rounds": "SR:FUNDING_ROUNDS(3.0.0)",
    "current_engagements": "SR:CURRENT_ENGAGEMENTS(7.0.0)",
    "financials": "SR:FINANCIALS(8.0.0)",
    "business_struct": "SR:BUSINESS_STRUCTURE(6.0.0)",
    "featured_clients": "SR:FEATURED_CLIENTS(3.0.0)",
    "company_entities": "SR:COMPANY_ENTITIES(1.0.0)",
    "events": "SR:EVENTS(9.0.0)",
    "tech_stack": "SR:TECHNOLOGY_STACK(6.0.0)",
    "projects": "SR:PROJECTS(13.0.0)",
    "it_job_postings": "SR:IT_JOB_POSTINGS(15.0.0)",
    "job_titles_demand": "SR:JOB_TITLES_IN_DEMAND_SR_ACCOUNT(6.0.0)",
    "it_workforce_locations": "SR:IT_WORKFORCE_LOCATIONS(11.0.0)",
    "it_workforce_skills": "SR:IT_WORKFORCE_SKILLS(6.0.0)",
    "competitors": "SR:COMPETITORS(7.1.0)",
    "key_people": "SR:KEY_PEOPLE(16.0.0)",
    "management_changes": "SR:MANAGEMENT_TEAM_CHANGES(9.0.0)",
    "top_outsourcing": "SR:TOP_OUTSOURCING_METRCIS(2.0.0)",
    "drivers_priorities": "SR:DRIVERS_AND_PRIORITIES(3.0.0)",
    "additional_info": "SR:ADDITIONAL_INFORMATION_SR_ACCOUNT(4.0.0)",
    "swot_analysis": "SR:SWOT_ANALYSIS(1.0.0)",
    "infongen": "SR:INFONGEN(1.0.0)",
    "data_availability": "SR:DATA_AVAILABILITY_SR_ACCOUNT(2.0.0)",
}

if __name__ == "__main__":
    workspace = "Presales:Account_Report"
    init_local_config(workspace)

    data_bridge = init_data_bridge()
    base_path = "migration/v0_2_0"
    with (
        open(f"{base_path}/glossaries.py", "w") as glossaries_io,
        open(f"{base_path}/concept_schemes.py", "w") as cs_io,
        open(f"{base_path}/dsds.py", "w") as dsd_io,
        open(f"{base_path}/glossaries_items.py", "w") as glossaries_items,
        open(f"{base_path}/hierarchies.py", "w") as hierarchies_io,
    ):
        path_to_assets = f"{base_path}/assets/"

        code_generator = ArtefactsCodeGenerator(
            workspace,
            data_bridge,
            glossaries_io,
            cs_io,
            dsd_io,
            glossaries_items,
            hierarchies_io,
            path_to_assets,
        )
        code_generator.generate_code(__DSD_URNS, __CS_NAMES, {})
