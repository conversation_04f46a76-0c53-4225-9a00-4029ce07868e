from quanthub.structures import GlossaryAttributeType as Type
from quanthub.structures import MigrationArtefacts as Artefacts

csr_awarding_agency = Artefacts.create_glossary(
    agency="CSR",
    artefact_id="AWARDING_AGENCY",
    name="Awarding Agency",
    description="",
)

csr_competitor_account = Artefacts.create_glossary(
    agency="CSR",
    artefact_id="COMPETITOR_ACCOUNT",
    name="Competitor Account",
    description="",
)

csr_contractor = Artefacts.create_glossary(
    agency="CSR",
    artefact_id="CONTRACTOR",
    name="Contractor",
    description="",
)

epam_awards_and_recognitions_csr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="AWARDS_AND_RECOGNITIONS_CSR",
    name="Awards & Recognitions CSR",
    description="",
)

epam_business_unit_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="BUSINESS_UNIT_SR",
    name="Business Unit SR",
    description="",
)

epam_category_of_financial_metrics_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="CATEGORY_OF_FINANCIAL_METRICS_SR",
    name="Category of Financial Metrics SR",
    description="",
)

epam_company_names_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="COMPANY_NAMES_SR",
    name="Company Names SR",
    description="",
)

epam_company_type_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="COMPANY_TYPE_SR",
    name="Company Type SR",
    description="",
)

epam_current_engagement_status = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="CURRENT_ENGAGEMENT_STATUS",
    name="Current Engagement Status",
    description="",
)

epam_customer_code_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="CUSTOMER_CODE_SR",
    name="Customer Code SR",
    description="",
)

epam_events_subcategory_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="EVENTS_SUBCATEGORY_SR",
    name="Events Subcategory SR",
    description="",
)

epam_events_title_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="EVENTS_TITLE_SR",
    name="Events Title SR",
    description="",
)

epam_executive_movement_type_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="EXECUTIVE_MOVEMENT_TYPE_SR",
    name="Executive Movement Type SR",
    description="",
)

epam_financial_metrics_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="FINANCIAL_METRICS_SR",
    name="Financial Metrics SR",
    description="",
)

epam_funding_rounds_transaction_name_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="FUNDING_ROUNDS_TRANSACTION_NAME_SR",
    name="Funding Rounds Transaction Name SR",
    description="",
)

epam_gbu = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="GBU",
    name="GBU",
    description="",
)

epam_industries_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="INDUSTRIES_SR",
    name="Industries SR",
    description="",
)

epam_it_initiatives = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="IT_INITIATIVES",
    name="IT Initiatives",
    description="",
)

epam_it_workforce_details_name_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="IT_WORKFORCE_DETAILS_NAME_SR",
    name="IT Workforce Details Name SR",
    description="",
)

epam_it_workforce_details_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="IT_WORKFORCE_DETAILS_SR",
    name="IT Workforce Details_SR",
    description="",
)

epam_job_titles_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="JOB_TITLES_SR",
    name="Job Titles SR",
    description="",
)

epam_locations_hierarchy_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="LOCATIONS_HIERARCHY_SR",
    name="Locations Hierarchy SR",
    description="",
)

epam_locations_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="LOCATIONS_SR",
    name="Locations SR",
    description="",
)

epam_news_category_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="NEWS_CATEGORY_SR",
    name="News Category SR",
    description="",
)

epam_news_titles_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="NEWS_TITLES_SR",
    name="News Titles SR",
    description="",
)

epam_offerings_by_category = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OFFERINGS_BY_CATEGORY",
    name="Offerings by Category CSR",
    description="",
)

epam_offerings_by_category_hierarchy = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OFFERINGS_BY_CATEGORY_HIERARCHY",
    name="Offerings by Category hierarchy",
    description="",
)

epam_offerings_by_industry = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OFFERINGS_BY_INDUSTRY",
    name="Offerings by Industry CSR",
    description="",
)

epam_offerings_by_services = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OFFERINGS_BY_SERVICES",
    name="Offerings by Services CSR",
    description="",
)

epam_outsourcing_metrcicssr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OUTSOURCING_METRCICSSR",
    name="Outsourcing Metrcics SR",
    description="",
)

epam_outsourcing_workloads_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="OUTSOURCING_WORKLOADS_SR",
    name="Outsourcing Workloads SR",
    description="",
)

epam_partnerships_and_acquisitions_category = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="PARTNERSHIPS_AND_ACQUISITIONS_CATEGORY",
    name="Partnerships and Acquisitions Category",
    description="",
)

epam_products_and_services_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="PRODUCTS_AND_SERVICES_SR",
    name="Products and Services SR",
    description="",
)

epam_project_category_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="PROJECT_CATEGORY_SR",
    name="Project Category SR",
    description="",
)

epam_project_type_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="PROJECT_TYPE_SR",
    name="Project Type SR deactivated",
    description="",
)

epam_record_type_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="RECORD_TYPE_SR",
    name="Record Type SR",
    description="",
)

epam_risks_and_challenges_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="RISKS_AND_CHALLENGES_SR",
    name="Risks and Challenges SR",
    description="",
)

epam_skills_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="SKILLS_SR",
    name="Skills SR",
    description="",
)

epam_solutions___capabilities = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="SOLUTIONS___CAPABILITIES",
    name="Solutions & Capabilities CSR",
    description="",
)

epam_strategy_risks_and_initiatives_category_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="STRATEGY_RISKS_AND_INITIATIVES_CATEGORY_SR",
    name="Strategy Risks and Initiatives Category SR",
    description="",
)

epam_strategy_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="STRATEGY_SR",
    name="Strategy SR",
    description="",
)

epam_strategy__risks_and_initiatives_subcategory_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="STRATEGY__RISKS_AND_INITIATIVES_SUBCATEGORY_SR",
    name="Strategy, Risks and Initiatives Subcategory SR",
    description="",
)

epam_target_account_list_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="TARGET_ACCOUNT_LIST_SR",
    name="Target Account List SR",
    description="",
)

epam_technology_tools_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="TECHNOLOGY_TOOLS_SR",
    name="Technology Tools SR",
    description="",
)

epam_tech_stack_category_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="TECH_STACK_CATEGORY_SR",
    name="Tech Stack Category SR",
    description="",
)

epam_top_metrics_breakdown_sr = Artefacts.create_glossary(
    agency="EPAM",
    artefact_id="TOP_METRICS_BREAKDOWN_SR",
    name="Top Metrics Breakdown SR",
    description="",
)

sr_events_hierarchy_test = Artefacts.create_glossary(
    agency="SR",
    artefact_id="EVENTS_HIERARCHY_TEST",
    name="Events Hierarchy test",
    description="",
)

sr_industries_hierarchy = Artefacts.create_glossary(
    agency="SR",
    artefact_id="INDUSTRIES_HIERARCHY",
    name="Industries Hierarchy",
    description="The hierarchy of Industry consists of industries and sub-industries in which companies operate.",
)

sr_industry_function = Artefacts.create_glossary(
    agency="SR",
    artefact_id="INDUSTRY_FUNCTION",
    name="Industry Function",
    description="",
)

sr_job_titles_id = Artefacts.create_glossary(
    agency="SR",
    artefact_id="JOB_TITLES_ID",
    name="Job Titles ID",
    description="",
)

sr_locations_hierarachy = Artefacts.create_glossary(
    agency="SR",
    artefact_id="LOCATIONS_HIERARACHY",
    name="Locations Hierarchy",
    description="Hierarchy of locations, that includes Country, Region & City",
    attributes=[
        Artefacts.create_glossary_attribute("type", Type.Text),
        Artefacts.create_glossary_attribute("name_original", Type.Text),
        Artefacts.create_glossary_attribute("iso2", Type.Text),
        Artefacts.create_glossary_attribute("iso3", Type.Text),
    ],
)

sr_locations_hierarachy = Artefacts.create_glossary(
    agency="SR",
    artefact_id="LOCATIONS_HIERARACHY",
    name="Locations Hierarchy",
    description="Hierarchy of locations, that includes Country, Region & City",
    attributes=[
        Artefacts.create_glossary_attribute("type", Type.Text),
        Artefacts.create_glossary_attribute("name_original", Type.Text),
        Artefacts.create_glossary_attribute("iso2", Type.Text),
        Artefacts.create_glossary_attribute("iso3", Type.Text),
    ],
)

sr_opportunity = Artefacts.create_glossary(
    agency="SR",
    artefact_id="OPPORTUNITY",
    name="Opportunity",
    description="",
)

sr_problem_statement_driver = Artefacts.create_glossary(
    agency="SR",
    artefact_id="PROBLEM_STATEMENT_DRIVER",
    name="Problem Statement/Driver",
    description="",
)

sr_project_type = Artefacts.create_glossary(
    agency="SR",
    artefact_id="PROJECT_TYPE",
    name="Project Type",
    description="",
)

sr_status_of_the_research_sr_account = Artefacts.create_glossary(
    agency="SR",
    artefact_id="STATUS_OF_THE_RESEARCH_SR_ACCOUNT",
    name="Status of the research SR Account",
    description="",
)

sr_tabs_sr_account = Artefacts.create_glossary(
    agency="SR",
    artefact_id="TABS_SR_ACCOUNT",
    name="Tabs SR Account",
    description="",
)

