from quanthub.structures import AttachmentLevelType as Type
from quanthub.structures import MigrationArtefacts as Artefacts

from . import concept_schemes as cs

general_overview = Artefacts.create_dsd(
    agency="SR",
    artefact_id="GENERAL_OVERVIEW",
    name="General Overview",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("PARENT_COMPANY", cs.sales_research["PARENT_COMPANY"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("ABOUT", cs.sales_research["ABOUT"], Type.Observation),
        Artefacts.create_attribute("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"], Type.Observation),
        Artefacts.create_attribute("INDUSTRY", cs.sales_research["INDUSTRY"], Type.Observation),
        Artefacts.create_attribute("COMPANY_TYPE", cs.sales_research["COMPANY_TYPE"], Type.Observation),
        Artefacts.create_attribute("WEBSITE", cs.sales_research["WEBSITE"], Type.Observation),
        Artefacts.create_attribute("LOGO_URL", cs.sales_research["LOGO_URL"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("NUMBER_OF_EMPLOYEES", cs.sales_research["NUMBER_OF_EMPLOYEES"]),
        Artefacts.create_measure("REVENUE_USD", cs.sales_research["REVENUE_USD"]),
        Artefacts.create_measure("VALUATION_USD", cs.sales_research["VALUATION_USD"]),
        Artefacts.create_measure("IT_SPEND_USD", cs.sales_research["IT_SPEND_USD"]),
        Artefacts.create_measure("NUMBER_OF_FUNDING_ROUNDS", cs.sales_research["NUMBER_OF_FUNDING_ROUNDS"]),
        Artefacts.create_measure("TOTAL_FUNDING_AMOUNT_USD", cs.sales_research["TOTAL_FUNDING_AMOUNT_USD"]),
    ],
)

funding_rounds = Artefacts.create_dsd(
    agency="SR",
    artefact_id="FUNDING_ROUNDS",
    name="Funding Rounds",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("TRANSACTION_NAME", cs.sales_research["TRANSACTION_NAME"]),
        Artefacts.create_dimension("INVESTORS", cs.sales_research["INVESTORS"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Custom, attachment_level=["TARGET_ACCOUNT"]),
    ],
    measures=[
        Artefacts.create_measure("VALUATION_USD", cs.sales_research["VALUATION_USD"]),
        Artefacts.create_measure("MONEY_RAISED_USD", cs.sales_research["MONEY_RAISED_USD"]),
    ],
)

current_engagements = Artefacts.create_dsd(
    agency="SR",
    artefact_id="CURRENT_ENGAGEMENTS",
    name="Current Engagements",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("COMPANY_NAME", cs.sales_research["COMPANY_NAME"]),
        Artefacts.create_dimension("CUSTOMER_CODE", cs.sales_research["CUSTOMER_CODE"]),
        Artefacts.create_dimension("GBU", cs.sales_research["GBU"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("TELESCOPE_LINK", cs.sales_research["TELESCOPE_LINK"], Type.Observation),
        Artefacts.create_attribute("CRM_LINK", cs.sales_research["CRM_LINK"], Type.Observation),
        Artefacts.create_attribute("ACCOUNT_MANAGER", cs.sales_research["ACCOUNT_MANAGER"], Type.Observation),
        Artefacts.create_attribute("SALES_MANAGER", cs.sales_research["SALES_MANAGER"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("STATUS", cs.sales_research["STATUS"]),
    ],
)

financials = Artefacts.create_dsd(
    agency="SR",
    artefact_id="FINANCIALS",
    name="Financials",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("FINANCIAL_METRICS", cs.sales_research["FINANCIAL_METRICS"]),
        Artefacts.create_dimension("CATEGORY_OF_FINANCIAL_METRICS", cs.sales_research["CATEGORY_OF_FINANCIAL_METRICS"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Custom, attachment_level=["TARGET_ACCOUNT"]),
    ],
    measures=[
        Artefacts.create_measure("REVENUE_USD", cs.sales_research["REVENUE_USD"]),
        Artefacts.create_measure("IT_SPEND_USD", cs.sales_research["IT_SPEND_USD"]),
        Artefacts.create_measure("EBITDA_USD", cs.sales_research["EBITDA_USD"]),
    ],
)

business_struct = Artefacts.create_dsd(
    agency="SR",
    artefact_id="BUSINESS_STRUCTURE",
    name="Business Structure",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("BUSINESS_UNIT", cs.sales_research["BUSINESS_UNIT"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("PRODUCTS_AND_SERVICES", cs.sales_research["PRODUCTS_AND_SERVICES"]),
        Artefacts.create_measure("ADDITIONAL_INFORMATION", cs.sales_research["ADDITIONAL_INFORMATION"]),
    ],
)

featured_clients = Artefacts.create_dsd(
    agency="SR",
    artefact_id="FEATURED_CLIENTS",
    name="Featured Clients",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("COMPANY_NAME", cs.sales_research["COMPANY_NAME"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("INDUSTRY", cs.sales_research["INDUSTRY"], Type.Observation),
        Artefacts.create_attribute("ADDITIONAL_INFORMATION", cs.sales_research["ADDITIONAL_INFORMATION"], Type.Observation),
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("DATA_AS_OF", cs.sales_research["DATA_AS_OF"]),
    ],
)

company_entities = Artefacts.create_dsd(
    agency="SR",
    artefact_id="COMPANY_ENTITIES",
    name="Company Entities",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("SUBSIDIARY_NAME", cs.sales_research["SUBSIDIARY_NAME"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("CRM_LINK", cs.sales_research["CRM_LINK"], Type.Observation),
        Artefacts.create_attribute("ACCOUNT_MANAGER", cs.sales_research["ACCOUNT_MANAGER"], Type.Observation),
        Artefacts.create_attribute("SALES_MANAGER", cs.sales_research["SALES_MANAGER"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
        Artefacts.create_attribute("DESCRIPTION", cs.sales_research["DESCRIPTION"], Type.Observation),
        Artefacts.create_attribute("WEBSITE", cs.sales_research["WEBSITE"], Type.Observation),
        Artefacts.create_attribute("LINKEDIN_URL", cs.sales_research["LINKEDIN_URL"], Type.Observation),
        Artefacts.create_attribute("LINK_TO_THE_REPORT", cs.sales_research["LINK_TO_THE_REPORT"], Type.Observation),
        Artefacts.create_attribute("COMMENTS", cs.sales_research["COMMENTS"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("STATUS", cs.sales_research["STATUS"]),
    ],
)

events = Artefacts.create_dsd(
    agency="SR",
    artefact_id="EVENTS",
    name="Events",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("EVENTS_CATEGORY", cs.sales_research["EVENTS_CATEGORY"]),
        Artefacts.create_dimension("COMPANY_NAME", cs.sales_research["COMPANY_NAME"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("DESCRIPTION", cs.sales_research["DESCRIPTION"]),
        Artefacts.create_measure("EVENTS_TITLE_S", cs.sales_research["EVENTS_TITLE_S"]),
        Artefacts.create_measure("EPAM_CLIENTS", cs.sales_research["EPAM_CLIENTS"]),
    ],
)

tech_stack = Artefacts.create_dsd(
    agency="SR",
    artefact_id="TECHNOLOGY_STACK",
    name="Technology Stack",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("TECH_STACK_CATEGORY", cs.sales_research["TECH_STACK_CATEGORY"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("TECHNOLOGY_TOOLS", cs.sales_research["TECHNOLOGY_TOOLS"]),
    ],
)

projects = Artefacts.create_dsd(
    agency="SR",
    artefact_id="PROJECTS",
    name="Projects",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("PROJECT_CATEGORY", cs.sales_research["PROJECT_CATEGORY"]),
        Artefacts.create_dimension("COMPANY_NAME", cs.sales_research["COMPANY_NAME"]),
        Artefacts.create_dimension("PROJECT_TYPE1", cs.sales_research["PROJECT_TYPE1"]),
        Artefacts.create_dimension("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
        Artefacts.create_attribute("TECHNOLOGY_TOOLS", cs.sales_research["TECHNOLOGY_TOOLS"], Type.Observation),
        Artefacts.create_attribute("PROJECT_DESCRIPTION", cs.sales_research["PROJECT_DESCRIPTION"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("START_DATE", cs.sales_research["START_DATE"]),
        Artefacts.create_measure("END_DATE", cs.sales_research["END_DATE"]),
    ],
)

it_job_postings = Artefacts.create_dsd(
    agency="SR",
    artefact_id="IT_JOB_POSTINGS",
    name="IT Job Postings",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("SKILLS", cs.sales_research["SKILLS"]),
        Artefacts.create_measure("JOB_TITLE_S", cs.sales_research["JOB_TITLE_S"]),
    ],
)

job_titles_demand = Artefacts.create_dsd(
    agency="SR",
    artefact_id="JOB_TITLES_IN_DEMAND_SR_ACCOUNT",
    name="Job Titles in Demand SR Account",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS", cs.sales_research["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"]),
        Artefacts.create_measure("JOB_TITLE_S", cs.sales_research["JOB_TITLE_S"]),
    ],
)

it_workforce_locations = Artefacts.create_dsd(
    agency="SR",
    artefact_id="IT_WORKFORCE_LOCATIONS",
    name="IT Workforce Locations",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("EMPLOYEES", cs.sales_research["EMPLOYEES"]),
        Artefacts.create_measure("ONE_YEAR_GROWTH_PERCENT", cs.sales_research["ONE_YEAR_GROWTH_PERCENT"]),
        Artefacts.create_measure("DEPARTURES", cs.sales_research["DEPARTURES"]),
        Artefacts.create_measure("HIRES", cs.sales_research["HIRES"]),
        Artefacts.create_measure("RATIO", cs.sales_research["RATIO"]),
        Artefacts.create_measure("NET_CHANGE", cs.sales_research["NET_CHANGE"]),
        Artefacts.create_measure("NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS", cs.sales_research["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"]),
    ],
)

it_workforce_skills = Artefacts.create_dsd(
    agency="SR",
    artefact_id="IT_WORKFORCE_SKILLS",
    name="IT Workforce Skills",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("SKILLS", cs.sales_research["SKILLS"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("EMPLOYEES", cs.sales_research["EMPLOYEES"]),
        Artefacts.create_measure("ONE_YEAR_GROWTH_PERCENT", cs.sales_research["ONE_YEAR_GROWTH_PERCENT"]),
        Artefacts.create_measure("NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS", cs.sales_research["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"]),
    ],
)

competitors = Artefacts.create_dsd(
    agency="SR",
    artefact_id="COMPETITORS",
    name="Competitors",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("COMPETITOR_NAME", cs.sales_research["COMPETITOR_NAME"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("SOURCE", cs.sales_research["SOURCE"]),
    ],
)

key_people = Artefacts.create_dsd(
    agency="SR",
    artefact_id="KEY_PEOPLE",
    name="Key People",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("RECORD_TYPE", cs.sales_research["RECORD_TYPE"]),
        Artefacts.create_dimension("FIRST_LEVEL_CONNECTIONS_NAME", cs.sales_research["FIRST_LEVEL_CONNECTIONS_NAME"]),
        Artefacts.create_dimension("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("JOB_TITLE_ID", cs.sales_research["JOB_TITLE_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("LINKEDIN_URL", cs.sales_research["LINKEDIN_URL"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("EXECUTIVE_NAME", cs.sales_research["EXECUTIVE_NAME"]),
        Artefacts.create_measure("JOB_TITLE_S", cs.sales_research["JOB_TITLE_S"]),
    ],
)

management_changes = Artefacts.create_dsd(
    agency="SR",
    artefact_id="MANAGEMENT_TEAM_CHANGES",
    name="Management Team Changes",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("MOVEMENT_TYPE", cs.sales_research["MOVEMENT_TYPE"]),
        Artefacts.create_dimension("LOCATIONS_HIERARCHY_NEW2", cs.sales_research["LOCATIONS_HIERARCHY_NEW2"]),
        Artefacts.create_dimension("PREVIOUS_COMPANY", cs.sales_research["PREVIOUS_COMPANY"]),
        Artefacts.create_dimension("JOINING_COMPANY", cs.sales_research["JOINING_COMPANY"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("LINKEDIN_URL", cs.sales_research["LINKEDIN_URL"], Type.Observation),
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("EXECUTIVE_NAME", cs.sales_research["EXECUTIVE_NAME"]),
        Artefacts.create_measure("JOB_TITLE_S", cs.sales_research["JOB_TITLE_S"]),
        Artefacts.create_measure("PREVIOUS_POSITION_S", cs.sales_research["PREVIOUS_POSITION_S"]),
    ],
)

top_outsourcing = Artefacts.create_dsd(
    agency="SR",
    artefact_id="TOP_OUTSOURCING_METRCIS",
    name="Top Outsourcing Metrcis",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("TOP_METRICS_BREAKDOWN", cs.sales_research["TOP_METRICS_BREAKDOWN"]),
        Artefacts.create_dimension("METRCICS", cs.sales_research["METRCICS"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
    ],
    measures=[
        Artefacts.create_measure("NUMBER_OF_ACTIVE_WORKFLOWS", cs.sales_research["NUMBER_OF_ACTIVE_WORKFLOWS"]),
        Artefacts.create_measure("DATA_AS_OF", cs.sales_research["DATA_AS_OF"]),
    ],
)

drivers_priorities = Artefacts.create_dsd(
    agency="SR",
    artefact_id="DRIVERS_AND_PRIORITIES",
    name="Drivers and Priorities",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("PRIORITY_DRIVER", cs.sales_research["PRIORITY_DRIVER"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DESCRIPTION", cs.sales_research["DESCRIPTION"], Type.Observation),
        Artefacts.create_attribute("JUSTIFICATION", cs.sales_research["JUSTIFICATION"], Type.Observation),
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("DATA_AS_OF", cs.sales_research["DATA_AS_OF"]),
    ],
)

additional_info = Artefacts.create_dsd(
    agency="SR",
    artefact_id="ADDITIONAL_INFORMATION_SR_ACCOUNT",
    name="Additional Information SR Account",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("ADDITIONAL_MATERIALS", cs.sales_research["ADDITIONAL_MATERIALS"], Type.Observation),
        Artefacts.create_attribute("SOURCE", cs.sales_research["SOURCE"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("DATA_AS_OF", cs.sales_research["DATA_AS_OF"]),
    ],
)

swot_analysis = Artefacts.create_dsd(
    agency="SR",
    artefact_id="SWOT_ANALYSIS",
    name="SWOT Analysis",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("EVENTS_CATEGORY", cs.sales_research["EVENTS_CATEGORY"]),
        Artefacts.create_dimension("ROW_ID", cs.sales_research["ROW_ID"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.Observation),
    ],
    measures=[
        Artefacts.create_measure("DESCRIPTION", cs.sales_research["DESCRIPTION"]),
    ],
)

infongen = Artefacts.create_dsd(
    agency="SR",
    artefact_id="INFONGEN",
    name="InfoNgen",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
    ],
    measures=[
        Artefacts.create_measure("DESCRIPTION", cs.sales_research["DESCRIPTION"]),
    ],
)

data_availability = Artefacts.create_dsd(
    agency="SR",
    artefact_id="DATA_AVAILABILITY_SR_ACCOUNT",
    name="Data Availability SR Account",
    description="",
    dimensions=[
        Artefacts.create_dimension("FREQ", cs.sdmx["FREQ"]),
        Artefacts.create_dimension("TARGET_ACCOUNT", cs.sales_research["TARGET_ACCOUNT"]),
        Artefacts.create_dimension("TABS_INFORMATION", cs.sales_research["TABS_INFORMATION"]),
        Artefacts.create_dimension("TIME_PERIOD", cs.sdmx["TIME_PERIOD"], time_dimension=True),
    ],
    attributes=[
        Artefacts.create_attribute("DATA_AS_OF", cs.sales_research["DATA_AS_OF"], Type.DataSet),
    ],
    measures=[
        Artefacts.create_measure("STATUS_OF_THE_RESEARCH", cs.sales_research["STATUS_OF_THE_RESEARCH"]),
    ],
)

