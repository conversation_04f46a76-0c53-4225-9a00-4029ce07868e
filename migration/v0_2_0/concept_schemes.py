from quanthub_pipeline.system import data_api as __qh
from quanthub_pipeline.system.interface.runner_config import the_runner_config

from quanthub.structures import MigrationArtefacts as Artefacts
from quanthub.structures import QuantHubDataBridge
from quanthub.structures import ValueType as Type
from quanthub.structures import create_version_increment_client

from . import glossaries as gl

__workspace = the_runner_config.workspace_id
__structure_client = __qh.create_sdmxplus_structure_client()
__version_client = create_version_increment_client()
__data_bridge = QuantHubDataBridge(__structure_client, __version_client)

sales_research = Artefacts.create_concept_scheme(
    agency="EPAM",
    artefact_id="SALES_RESEARCH",
    name="Sales Research",
    description="",
    concepts=[
        Artefacts.create_concept("ABOUT", "About", "", Type.String),
        Artefacts.create_concept("ACCOUNT", "Account", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("ACCOUNT_MANAGER", "Account Manager", "", Type.String),
        Artefacts.create_concept("ADDITIONAL_INFORMATION", "Additional Information", "", Type.String),
        Artefacts.create_concept("ADDITIONAL_MATERIALS", "Additional Materials", "", Type.String),
        Artefacts.create_concept("AWARDING_AGENCY", "Awarding Agency", "", Type.Glossary, gl.csr_awarding_agency),
        Artefacts.create_concept("AWARDS_AND_RECOGNITIONS", "Awards & Recognitions deprecated", "", Type.Glossary, gl.epam_awards_and_recognitions_csr),
        Artefacts.create_concept("AWARDS_RECOGNITIONS", "Awards & Recognitions ", "", Type.String),
        Artefacts.create_concept("BUSINESS_UNIT", "Business Unit", "", Type.Glossary, gl.epam_business_unit_sr),
        Artefacts.create_concept("CATEGORY", "Category", "", Type.String),
        Artefacts.create_concept("CATEGORY_OF_FINANCIAL_METRICS", "Category of Financial Metrics", "", Type.Glossary, gl.epam_category_of_financial_metrics_sr),
        Artefacts.create_concept("CLIENT_PERSONA", "Client Persona", "", Type.String),
        Artefacts.create_concept("COMMENT", "Comment", "", Type.String),
        Artefacts.create_concept("COMMENTS", "Comments", "", Type.String),
        Artefacts.create_concept("COMPANY_NAME", "Company name", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("COMPANY_TYPE", "Company Type", "", Type.Glossary, gl.epam_company_type_sr),
        Artefacts.create_concept("COMPETITOR_ACCOUNT", "Competitor Account", "", Type.Glossary, gl.csr_competitor_account),
        Artefacts.create_concept("COMPETITOR_NAME", "Competitor Name", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("CONTRACTOR", "Contractor", "", Type.Glossary, gl.csr_contractor),
        Artefacts.create_concept("CRM_ACCOUNT", "CRM Account", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("CRM_LINK", "CRM Link", "", Type.String),
        Artefacts.create_concept("CRM_STATUS", "CRM Status", "", Type.Glossary, gl.epam_current_engagement_status),
        Artefacts.create_concept("CURRENT_COMPANY", "Current Company", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("CURRENT_EMAIL", "Current Email", "", Type.String),
        Artefacts.create_concept("CURRENT_TITLE", "Current Title", "", Type.String),
        Artefacts.create_concept("CUSTOMER_CODE", "Customer code", "", Type.Glossary, gl.epam_customer_code_sr),
        Artefacts.create_concept("DATA_AS_OF", "Data as of", "", Type.ObservationalTimePeriod),
        Artefacts.create_concept("DATE", "Date", "", Type.ObservationalTimePeriod),
        Artefacts.create_concept("DEPARTURES", "Departures", "", Type.Integer),
        Artefacts.create_concept("DESCRIPTION", "Description", "", Type.String),
        Artefacts.create_concept("DETAILED_DESCRIPTION", "Detailed Description", "", Type.String),
        Artefacts.create_concept("EBITDA_USD", "EBITDA (USD)", "", Type.Integer),
        Artefacts.create_concept("EMPLOYEES", "Employees", "", Type.Integer),
        Artefacts.create_concept("END_DATE", "End Date", "", Type.ObservationalTimePeriod),
        Artefacts.create_concept("EPAM_CLIENT", "EPAM Client", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("EPAM_CLIENTS", "EPAM Clients", "", Type.String),
        Artefacts.create_concept("EPAM_VALUE_PROPOSITION", "EPAM Value Proposition", "", Type.String),
        Artefacts.create_concept("EVENTS_CATEGORY", "Events Category", "", Type.Glossary, gl.sr_events_hierarchy_test),
        Artefacts.create_concept("EVENTS_SUBCATEGORY", "Events Subcategory", "", Type.Glossary, gl.epam_events_subcategory_sr),
        Artefacts.create_concept("EVENTS_TITLE", "Events Title", "", Type.Glossary, gl.epam_events_title_sr),
        Artefacts.create_concept("EVENTS_TITLE_S", "Events Title", "", Type.String),
        Artefacts.create_concept("EXECUTIVE_NAME", "Executive Name", "", Type.String),
        Artefacts.create_concept("FINANCIAL_IMPACT_SCORE", "Financial Impact Score", "", Type.String),
        Artefacts.create_concept("FINANCIAL_METRICS", "Financial Metrics", "", Type.Glossary, gl.epam_financial_metrics_sr),
        Artefacts.create_concept("FIRST_LEVEL_CONNECTIONS_NAME", "1st level connections / Name", "", Type.String),
        Artefacts.create_concept("FUNCTIONAL_OBJECTIVES", "Functional Objectives", "", Type.String),
        Artefacts.create_concept("GBU", "GBU", "", Type.Glossary, gl.epam_gbu),
        Artefacts.create_concept("HIRES", "Hires", "", Type.Integer),
        Artefacts.create_concept("HQ_LOCATION", "HQ Location", "", Type.Glossary, gl.epam_locations_sr),
        Artefacts.create_concept("HQ_LOCATIONS_HIERARCHY", "HQ Location", "", Type.Glossary, gl.epam_locations_hierarchy_sr),
        Artefacts.create_concept("IMPACT_TO_BUSINESS_FUTURE_STATE", "Impact to Business (future state)", "", Type.String),
        Artefacts.create_concept("INDUSTRIES_HIERARCHY", "Industries Hierarchy", "", Type.Glossary, gl.sr_industries_hierarchy),
        Artefacts.create_concept("INDUSTRY", "Industry", "", Type.Glossary, gl.epam_industries_sr),
        Artefacts.create_concept("INDUSTRY_FUNCTION", "Industry Function", "", Type.Glossary, gl.sr_industry_function),
        Artefacts.create_concept("INVESTORS", "Investors", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("IT_SPEND_USD", "IT Spend (USD)", "", Type.Integer),
        Artefacts.create_concept("IT_WORKFORCE_DETAILS", "IT Workforce Details", "", Type.Glossary, gl.epam_it_workforce_details_sr),
        Artefacts.create_concept("IT_WORKFORCE_DETAILS_NAME", "IT Workforce Details Name", "", Type.Glossary, gl.epam_it_workforce_details_name_sr),
        Artefacts.create_concept("JOB_TITLE", "Job Title", "", Type.Glossary, gl.epam_job_titles_sr),
        Artefacts.create_concept("JOB_TITLE_ID", "Job Title ID", "", Type.Glossary, gl.sr_job_titles_id),
        Artefacts.create_concept("JOB_TITLE_S", "Job Title", "", Type.String),
        Artefacts.create_concept("JOINING_COMPANY", "Joining Company", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("JUSTIFICATION", "Justification", "", Type.String),
        Artefacts.create_concept("KPIS", "KPIs", "", Type.String),
        Artefacts.create_concept("LINKEDIN_URL", "LinkedIn URL", "", Type.String),
        Artefacts.create_concept("LINK_TO_CONTRACT", "Link to Contract", "", Type.String),
        Artefacts.create_concept("LINK_TO_THE_REPORT", "Link to the Report", "", Type.String),
        Artefacts.create_concept("LINK_TO_USASPENDING_GOV", "Link to USASpending.gov", "", Type.String),
        Artefacts.create_concept("LOCATION", "Location", "", Type.Glossary, gl.epam_locations_sr),
        Artefacts.create_concept("LOCATIONS_HIERARCHY", "Locations", "", Type.Glossary, gl.epam_locations_hierarchy_sr),
        Artefacts.create_concept("LOCATIONS_HIERARCHY_NEW", "Location Hierarchy inactive", "", Type.Glossary, gl.sr_locations_hierarachy),
        Artefacts.create_concept("LOCATIONS_HIERARCHY_NEW2", "Locations Hierarchy", "", Type.Glossary, gl.sr_locations_hierarachy),
        Artefacts.create_concept("LOGO_URL", "Logo URL", "", Type.String),
        Artefacts.create_concept("METRCICS", "Metrcics", "", Type.Glossary, gl.epam_outsourcing_metrcicssr),
        Artefacts.create_concept("MONEY_RAISED_USD", "Money Raised (USD)", "", Type.Integer),
        Artefacts.create_concept("MOVEMENT_TYPE", "Movement Type", "", Type.Glossary, gl.epam_executive_movement_type_sr),
        Artefacts.create_concept("NET_CHANGE", "Net change", "", Type.Integer),
        Artefacts.create_concept("NEWS_CATEGORY", "News Category", "", Type.Glossary, gl.epam_news_category_sr),
        Artefacts.create_concept("NEWS_TITLE", "News Title", "", Type.Glossary, gl.epam_news_titles_sr),
        Artefacts.create_concept("NEWS_TITLE_S", "News Title", "", Type.String),
        Artefacts.create_concept("NEW_POSITION", "New Position", "", Type.Glossary, gl.epam_job_titles_sr),
        Artefacts.create_concept("NO_OF_EPAMERS_INTERACTED_AS_PER_LOG_DATA", "No. of EPAMers Interacted (as per Log data)", "", Type.Integer),
        Artefacts.create_concept("NUMBER", "Number", "", Type.Integer),
        Artefacts.create_concept("NUMBER_OF_ACTIVE_WORKFLOWS", "# of Active Workflows", "", Type.Integer),
        Artefacts.create_concept("NUMBER_OF_EMPLOYEES", "Number of Employees", "", Type.Integer),
        Artefacts.create_concept("NUMBER_OF_FUNDING_ROUNDS", "# Of Funding Rounds", "", Type.Integer),
        Artefacts.create_concept("NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS", "# of Job Postings (Last 12 Months)", "", Type.Integer),
        Artefacts.create_concept("OBS_VALUE", "Observation Value", "", Type.Integer),
        Artefacts.create_concept("OFFERINGS_BY_CATEGORY", "Offerings by Category", "", Type.Glossary, gl.epam_offerings_by_category),
        Artefacts.create_concept("OFFERINGS_BY_CATEGORY_HIERARCHY", "Offerings by Category hierarchy", "", Type.Glossary, gl.epam_offerings_by_category_hierarchy),
        Artefacts.create_concept("ONE_YEAR_GROWTH_PERCENT", "1y growth, %", "", Type.Decimal),
        Artefacts.create_concept("OPPORTUNITY", "Opportunity", "", Type.Glossary, gl.sr_opportunity),
        Artefacts.create_concept("PARENT_COMPANY", "Parent Company", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("PARTNERSHIPS_AND_ACQUISITIONS_CATEGORY", "Partnerships and Acquisitions Category", "", Type.Glossary, gl.epam_partnerships_and_acquisitions_category),
        Artefacts.create_concept("PAST_EMAIL_AT_EPAM_CLIENT_COMPANY_USE_IT_TO_CHECK_LOG_DATA", "Past email  (at EPAM client company-use it to check log data)", "", Type.String),
        Artefacts.create_concept("PREVIOUS_COMPANY", "Previous Company", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("PREVIOUS_POSITION", "Previous Position", "", Type.Glossary, gl.epam_job_titles_sr),
        Artefacts.create_concept("PREVIOUS_POSITION_S", "Previous Position", "", Type.String),
        Artefacts.create_concept("PRIORITY_DRIVER", "Priority / Driver", "", Type.Glossary, gl.sr_problem_statement_driver),
        Artefacts.create_concept("PROBLEM_STATEMENT_DRIVER", "Problem Statement / Driver", "", Type.Glossary, gl.sr_problem_statement_driver),
        Artefacts.create_concept("PRODUCTS_AND_SERVICES", "Products and Services", "", Type.Glossary, gl.epam_products_and_services_sr),
        Artefacts.create_concept("PROJECT_CATEGORY", "Project Category", "", Type.Glossary, gl.epam_project_category_sr),
        Artefacts.create_concept("PROJECT_DESCRIPTION", "Project Description", "", Type.String),
        Artefacts.create_concept("PROJECT_TYPE", "Project Type", "", Type.Glossary, gl.epam_project_type_sr),
        Artefacts.create_concept("PROJECT_TYPE1", "Project Type", "", Type.Glossary, gl.sr_project_type),
        Artefacts.create_concept("QUARTER", "Quarter", "", Type.String),
        Artefacts.create_concept("RATIO", "Ratio", "", Type.Decimal),
        Artefacts.create_concept("REASONING_FOR_FINANCIAL_IMPACT", "Reasoning for Financial Impact", "", Type.String),
        Artefacts.create_concept("RECORD_TYPE", "Record Type", "", Type.Glossary, gl.epam_record_type_sr),
        Artefacts.create_concept("REVENUE_USD", "Revenue (USD)", "", Type.Integer),
        Artefacts.create_concept("ROW_ID", "Row ID", "", Type.Integer),
        Artefacts.create_concept("ROW_INVESTORS", "Row Investors", "", Type.String),
        Artefacts.create_concept("ROW_TRANSACTION_NAME", "Row Transaction Name", "", Type.String),
        Artefacts.create_concept("SALES_MANAGER", "Sales Manager", "", Type.String),
        Artefacts.create_concept("SKILLS", "Skills", "", Type.Glossary, gl.epam_skills_sr),
        Artefacts.create_concept("SOLUTIONS_AND_CAPABILITIES", "Solutions & Capabilities", "", Type.Glossary, gl.epam_solutions___capabilities),
        Artefacts.create_concept("SOURCE", "Source", "", Type.String),
        Artefacts.create_concept("START_DATE", "Start Date", "", Type.ObservationalTimePeriod),
        Artefacts.create_concept("STATUS", "Status", "", Type.Glossary, gl.epam_current_engagement_status),
        Artefacts.create_concept("STATUS_OF_THE_RESEARCH", "Status of the research", "", Type.Glossary, gl.sr_status_of_the_research_sr_account),
        Artefacts.create_concept("STRATEGY_RISKS_AND_INITIATIVES_CATEGORY", "Strategy, Risks and Initiatives Category", "", Type.Glossary, gl.epam_strategy_risks_and_initiatives_category_sr),
        Artefacts.create_concept("STRATEGY_RISKS_AND_INITIATIVES_SUBCATEGORY", "Strategy, Risks and Initiatives Subategory", "", Type.Glossary, gl.epam_strategy__risks_and_initiatives_subcategory_sr),
        Artefacts.create_concept("SUBSIDIARY_NAME", "Subsidiary Name", "", Type.Glossary, gl.epam_company_names_sr),
        Artefacts.create_concept("S_DATE", "Start Date", "", Type.Integer),
        Artefacts.create_concept("TABS_INFORMATION", "Tabs Information", "", Type.Glossary, gl.sr_tabs_sr_account),
        Artefacts.create_concept("TARGET_ACCOUNT", "Target Account", "", Type.Glossary, gl.epam_target_account_list_sr),
        Artefacts.create_concept("TECHNOLOGY_TOOLS", "Technology Tools", "", Type.Glossary, gl.epam_technology_tools_sr),
        Artefacts.create_concept("TECH_STACK_CATEGORY", "Tech Stack Category", "", Type.Glossary, gl.epam_tech_stack_category_sr),
        Artefacts.create_concept("TELESCOPE_LINK", "Telescope Link", "", Type.String),
        Artefacts.create_concept("TOP_METRICS_BREAKDOWN", "Top Metrics Breakdown", "", Type.Glossary, gl.epam_top_metrics_breakdown_sr),
        Artefacts.create_concept("TOTAL_FUNDING_AMOUNT_USD", "Total Funding Amount (USD)", "", Type.Integer),
        Artefacts.create_concept("TRANSACTION_NAME", "Transaction Name", "", Type.Glossary, gl.epam_funding_rounds_transaction_name_sr),
        Artefacts.create_concept("USER_JOURNEY_FUTURE_STATE", "User Journey (future state)", "", Type.String),
        Artefacts.create_concept("VALUATION_USD", "Valuation (USD)", "", Type.Integer),
        Artefacts.create_concept("VALUE_USD", "Value (USD)", "", Type.Integer),
        Artefacts.create_concept("WEBSITE", "Website", "", Type.String),
        Artefacts.create_concept("WORKLOADS", "Workloads", "", Type.Glossary, gl.epam_outsourcing_workloads_sr),
        Artefacts.create_concept("YEAR", "Year", "", Type.Integer),
    ]
)

sdmx = __data_bridge.read_concept_scheme(__workspace, urn="SDMX:CROSS_DOMAIN_CONCEPTS(2.1)")

