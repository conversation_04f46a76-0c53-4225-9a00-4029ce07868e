from typing import Optional

import pandas as pd
from migration.constants import SALES_ENABLEMENT_ACCOUNT
from quanthub.structures.representation_map import RepresentationMap
from quanthub.transformations.utils import init_local_config
from se_data_pipeline.component.utils import init_data_bridge

SHADOW_TESTING = "Presales:SHADOW_TESTING"
REPR_MAP_ID = f"{SALES_ENABLEMENT_ACCOUNT}:LOCATION_CACHE(0+.0.0)"
init_local_config(SHADOW_TESTING)

data_bridge = init_data_bridge()
repr_map: Optional[RepresentationMap] = data_bridge.read_representation_map(SHADOW_TESTING, REPR_MAP_ID)
base_values = pd.read_csv("migration/v1_0_0/assets/se_a_base_approved_location.csv")
repr_map.extend_from_df(base_values)
data_bridge.write_representation_map(
    workspace=SHADOW_TESTING,
    repr_map=repr_map,
    draft=False,
)
