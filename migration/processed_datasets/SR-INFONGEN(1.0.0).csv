FREQ,TARGET_ACCOUNT,TIME_PERIOD,DESCRIPTION
Q,INTELLUM,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=68bfd4d4-a1b1-4e63-8265-bc450e4ca395&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=4i%2BLlR3ERWaIFLs7%2BDjI55YuVrM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,GUITAR_CENTER_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""
https://service.infongen.com/11/Webpart/Show?id=7c2ab40a-0e09-427a-9e8d-bbdf32a3b3ee&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=DLuwg2fNlmIT1ltrZAMXzwQBD8U%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,HARBR,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7a14c93a-4822-4aaa-8b1d-8f394d751c1a&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=pe3wUcfELal4TNE8R9K71uq49bQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,H_I_G_CAPITAL,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=e9c98f50-fc46-4689-a416-fed3709eb7d4&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=HDcU9HJuK6BJNH36pFDMVHQ1ktw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,HEARTHSIDE_FOOD_SOLUTIONS,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=762cec88-e561-44a7-bdcd-4c1d54a8d47b&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=eczL1DxREJS5Bgvae3haXgRe9Co%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,THE_GAP_INC,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=c4bce805-040f-4d76-8d00-6f498425f264&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=4Ith%2FHm320fy3HBElHj24Mrsk1k%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,SOUTHERN_CALIFORNIA_GAS_COMPANY,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=ff7fdfc8-9598-42e5-9b90-0966521aa5e7&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=T4MVcWfuYMMpdbQ%2Bd%2BR0D3k0Aq4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,EBSCO_INFORMATION_SERVICES,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=3a0b4431-ae0b-4fe8-babb-e8ff8bb2f8b3&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=p8kLRGNvH3p5YjPKwukHpVYDOwA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,LEVI_STRAUSS_AND_CO,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=a906dfd2-8157-4f1f-aa13-2e3af994d774&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=t832q488MDaHwjYLv6KeuYlKYMw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,AGILENT_TECHNOLOGIES_INC,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=e938d6b2-f1d8-4924-b455-50a89fce184b&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=g3CZx0Ii8wSNdndbVtMivLu613U%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,SPORTSMAN_S_WAREHOUSE_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=32598571-a2d2-4417-bcd9-41a7d125f7db&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=BhxOLWikZJ92lQIgKo%2BLQrgHddw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,PANASONIC_AVIONICS_CORPORATION,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d9126879-4345-41dc-bc5e-4caca2865dcc&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=pxSCcN2osqyYcgAu7CXhO9ygCkc%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,STRIDE_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=00f32225-99c5-4deb-8db1-91c1b45b5300&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=Dhi%2FvhfSBXqQnA%2FaICzNPukrGYU%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ALTANA_TECHNOLOGIES,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7ffe9619-fd4a-4142-8d40-8b90e9886c39&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=jdeRFcP722KdkFtMhbh8XzWWJzA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ALTANA_TECHNOLOGIES,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7ffe9619-fd4a-4142-8d40-8b90e9886c39&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=jdeRFcP722KdkFtMhbh8XzWWJzA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,BENJAMIN_MOORE_AND_CO,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=f01858ff-c905-494f-b9f7-882c1efcb109&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=gMDqfJ2UgwvoW4%2FONXuKTBweLLs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,CHEGG_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=24730782-9e82-4d9f-9e50-ae0cdeb82d56&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=sF%2F4jd1yrk2nCEmZhJbORG6zVQI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,WOLTERS_KLUWER_N_V,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=406024f1-08a6-4497-90ee-f33846711217&ui=%2B6NqJ%2Bs4v8ukqhM7B44WwyjsNsVLJyW0BAhgDFxWcbtfYmornLTtTeSYy0OVguVb&h=A2fN0zM9Wyhh0V%2F60SkB7NKO8u4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,JOHNSON_AND_JOHNSON_SERVICES_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:100%"" src=""https://service.infongen.com/11/Webpart/Show?id=aac77ed0-b791-46db-a0ae-93c790d0cd98&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=1nGgxH%2BGlx4gdOkmU4hN57vlcao%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,SKECHERS_USA_INC,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=dd41adc4-9a2a-4cfd-8a73-e4daceecb1c6&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=XNs%2BCy0OBom7u%2FWPr31HOf4yof4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,SHUTTERFLY_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:100%"" src=""https://service.infongen.com/11/Webpart/Show?id=1a9ef62f-c18d-46e6-8daa-5a3d136ce067&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=zttz1FhcAoaHrLeuZA2ccDGor4k%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,LITHIA_MOTORS_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=9d1f6b39-961d-41b3-b0ef-c2058af6908f&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=g0zwxvsMBP3lhpXTGXt8qjIEQTA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,PEARSON_PLC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=30b6a959-e2af-44a6-b997-1af35a6ac768&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=opi92xAY7galw0U39zvVogN%2FA9Y%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,TOKIO_MARINE_HCC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4c9cab0c-d934-4720-a11a-d7b03d4e69b5&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=pZd%2FktXhOW5szKFgpUVtR2tAKu8%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,AT_AND_T,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:100%"" src=""https://service.infongen.com/11/Webpart/Show?id=8aa7186c-3f59-4e29-b658-c29b09a691d6&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=g%2FQyiiHeZQrG7e3XQlMNajVGg3M%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,IPSY,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1027b9cd-42aa-4bbe-8731-d7ac53a13c67&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=%2Britvpr5Ycvig3%2FEDE1p%2F%2F7r%2FQ8%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,PREMIUM_CREDIT,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=5a7e319e-2c56-45c8-942f-db477b431a14&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=DqSOLt6%2B2Oc3NgERxQGWO89Rnhk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,LITMOS,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1fd4a7d2-0950-4e16-b3d3-b61e17c1e282&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=h2SU01nOB7Pxt5A%2BRsZ%2FTSeLT1g%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ACCOR_SA,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=0caa50d7-86d7-46e7-ba9d-bd6d3ff0c46c&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=eE%2FQ4jnoX7tMlK%2F1HSVNunGJJvo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ACCOR_SA,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=51d4d6dd-97c2-487f-ae58-2a85ba6de582&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=I4x3cnH%2FdxOHABIXuiXvqJp1jBg%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ELECTRONIC_CAREGIVER_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=81566c06-ef0f-4daa-9e38-15a9d1f66406&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=VxZfWLzivYRI6UqUu9c8VWvD7r4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,T_MOBILE_US_INC,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=0eb32176-989a-4bdc-b7e5-d9281fe1013c&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=5dOklkfnBApj2IoPHOIg8A2SG8g%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ITJUANA,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7d0b0e9d-ceb6-4f48-b444-d289eba27cf2&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=2ZxTy2aoCyXhBy2aldYlYFXY%2FTQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,GULFSTREAM_AEROSPACE_CORPORATION,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=8b6e7ad2-49f3-46fd-8e9c-9d62008a89d4&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=2chvq78KaTwBE7rYAtjmsDBJO%2F0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,DISPRZ,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=74934a5c-3e5e-4871-990f-d62a443dc835&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=yskCrrhKmc4gADMg8Iuk8%2BpHQIA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,LESLIES_POOLMART_INC,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4157822f-1b1a-48d7-9d49-2f64d2657694&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=7S%2FJlEOJKPisHRKUZtkajKbX%2Bis%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,CCA_GLOBAL_PARTNERS,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6832f29d-c3fc-468e-b178-df2ee83e8975&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=KUZQ6PPsVTWjfJKUzEy2YNB3D8o%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,MANGO,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=20760092-6629-4bf3-b776-e3f835f784e8&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=WuNEiztu4TLYJmZiywvAvvC9OQY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,RENAISSANCE_LEARNING_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4a90a0e3-4cf0-4b08-977b-85dff73d8e91&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=Mq5Xd9StYC7OU9y8zf5ZZEnVgBY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,WOOD_MACKENZIE,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=029117f8-8df1-44e0-b216-43c3af78961b&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=jYpt0%2BDq%2FZFWIcyevPJbdfysZXk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,TELEFLORA,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=b01a8362-5706-44eb-aa8e-6e2d41a90e49&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=TQC5noIXz2oqvEqVcw4TAfGXvOk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,DECKERS_BRANDS,2024-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=030356f5-bfe8-4890-a517-47fd3b03ca02&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=gcqX4kUY4Rjl1eF3MrtfPi%2FnfV0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,VERRA_MOBILITY,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=b5cc3bd5-3466-4ff4-b2cc-3954985d9c2e&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=hg1XIHB4cZQ9K9ZvgLE4ZsdZlkw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,WELL_CA,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:100%"" src=""https://service.infongen.com/11/Webpart/Show?id=d504f61d-44bb-42a5-83e5-db7cb23ef7a8&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=%2FzOAWqv6T9be0%2Bcr4xRbhq0CwqM%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,GOOGLE_INC,2024-Q3,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=2f60c958-f2e4-4a13-9176-b2bcad5a61e5&ui=Gae9E6Nn%2FPKVb1ySvVBXFZZKLk1zVEXW6IQwYsEFWLtO%2BkFXti6ibC6ORAO3YiGc&h=oxr6fiaT8vwTMp5kFH3lGbANrOQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,GOOGLE_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=56238c22-12ae-4b18-bf89-3fa28ea8cb56&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=PCSmyweSFA%2FhuFnJtXdSlUquqys%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,FOLLETT,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=de2266bb-a3a7-40cb-94b5-7d3e56ad63f6&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=OGckGhNlTcNG4Wac%2FMdTCgH0V1U%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,FOLLETT_SOFTWARE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=fb5112d1-4c6c-4f10-9ab2-b6271327f184&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=ozr2EVb0D8WjyaB9rOW1jqlZE%2BI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,NIELSEN_HOLDINGS,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1602d7ec-35ba-4a08-ad82-1419e4db2c62&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=GjgtBxMSHLC5hNWsMF0SiM1oFlI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,QUESTEX_MEDIA_GROUP,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=44ce6527-b5ad-4a92-b278-8c22164a3fd9&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=ZDmPaLY7SfnYOLContUwCN6jgxQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,MOODY_S_CORPORATION,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=c12df2d5-192d-48cf-a858-146f2f1b0b5e&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=LxHN8Xsa8rNPmXSgtPHyyouQKfQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ZILLOW_GROUP,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=825ae2b9-0e81-4a89-8b31-c2c50e9a9820&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=vL%2BvjpQ7YJr0MlpBaYKWiZjTPZ0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ZILLOW_GROUP,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=825ae2b9-0e81-4a89-8b31-c2c50e9a9820&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=vL%2BvjpQ7YJr0MlpBaYKWiZjTPZ0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,EVEREST_INSURANCE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""
https://service.infongen.com/11/Webpart/Show?id=9880ade5-4492-4f19-949b-b919cd34be01&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=aZbghbTquXENNtKYzMIeIKDtU4o%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,VERTEX_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=bff56235-99e3-496a-8778-ffdaad097a1d&ui=%2B6NqJ%2Bs4v8ukqhM7B44WwyjsNsVLJyW0BAhgDFxWcbtfYmornLTtTeSYy0OVguVb&h=R0Gs%2FsbkdSax6WdN1E%2FI3WaG%2FgE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,NEWS_CORPORATION,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d47d4eca-fc48-4b34-8f61-9c8deee8469b&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=G4524vPJciHtM54QDfBmKgSHkgk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,HACHETTE_LIVRE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d513a7dc-99b2-490c-a23f-1ffb6ceb0928&ui=JtqdhcmcAuUbBV6PuuP0UsKJp2EPH3ZnB0%2FHHegERrk5EVw4V%2FRcAsV%2F1j9T%2F%2BN7&h=fB7g6f%2BH%2Fcwm%2BRTzeq1NMZoHhhA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,GOTO,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=20112eff-128f-4b76-8254-7d77222ca8d7&ui=%2B6NqJ%2Bs4v8ukqhM7B44WwyjsNsVLJyW0BAhgDFxWcbtfYmornLTtTeSYy0OVguVb&h=1JbGImYuF%2F%2BMp9AT4NkY4qmNC0s%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,TRIANZ,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6e686e85-138f-4991-9eb4-537851b5d286&ui=dhh6nNjacJuAGKoOnPM8mfZOlARNQFYIhpTbsjONPefjh6D2yMTb3h1ImZ%2BGMsmK&h=twHmH5gWhteLs0tkDCaPXdMa9yE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MONDELEZ_INTERNATIONAL_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=17ccc558-7857-404b-bba6-cf403e97022a&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=F0j%2BGNCPJXu9oJ%2FaDz9N%2FVm%2Btxg%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,HAVI,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=007fcbe1-e027-473d-beb8-06713c6a8a37&ui=JtqdhcmcAuUbBV6PuuP0UsKJp2EPH3ZnB0%2FHHegERrk5EVw4V%2FRcAsV%2F1j9T%2F%2BN7&h=Hy5E7sSymohkBxxgTtXaFm2wPpk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,FRESENIUS_KABI,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=761acff3-823f-4e89-aa8a-feec7e9d1a4b&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=xunwGlNgHScCM8yRHuU%2FKiuUXnA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MACMILLAN_EDUCATION,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=858aeafd-f54a-4a4b-b982-8736ae418b1e&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=yFGQZr%2BFeqrQrnIA4CyDqcpSB48%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MARS_INCORPORATED,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=0cff8fe0-c47c-4c1c-8129-c77dfad5b074&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=xT2cbM0KFICt6PsT1X5GpxsDSBs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,AVANTOR_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=105db183-ad77-453b-9cbe-b6cb9d18ff22&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=uR4VNILp7fb2GZ5sgSDUr3aR1KY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,EF_EDUCATION_FIRST,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6d361cf8-d01f-4281-a1b9-e99805fee43b&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=XLjCDv5NCZIGXb7lDULVQlJpM2E%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,AMERICAN_EXPRESS_COMPANY,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1baa013f-3d40-4a08-aa60-f4c607004032&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=oN2p1OkknBtfd0P3FzOCvDEPcjs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,SEPHORA,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d0afd0de-0714-49c7-b49b-066a87f7f0a5&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=ygGOg8rmQjPteBf56Mgq2qlFR1o%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,LEVIAT,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800 px"" src=""https://service.infongen.com/11/Webpart/Show?id=72c7aab1-415e-47c9-839f-f46f4ff4ada6&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=SJBK8YOPKi8OQp6d9dACd6GJVEs%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,WORLD_KINECT_CORPORATION,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=a9ad71ce-b2b5-4ae3-b837-c6003e3bc18e&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=iNSTUbYdAyCkwiX6TPZ3b8edPkE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,THE_NEW_YORK_TIMES_COMPANY,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=f9a63644-572a-4a8e-ba80-f1082205fd58&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=d9YAJK5Jk9lL%2Bqs92sorCxnUaKc%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,PLAID,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=b9a221bd-baa0-4435-bc8c-8141b234138a&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=X6Eid5zsKU2SjEHe5Gku9EhXVEU%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,HEARST_COMMUNICATIONS_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6487d88d-4071-4b3b-887d-9a98831d232e&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=2wn53IpmWZfHZrfI3k%2FLXcVWcbI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,MERCK_KGAA,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=f528f4ff-4ec7-4da8-8706-cd7d6a4b1e22&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=dCruvn8pPUz%2FWlXwLQsuWuX2a%2BQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,TEVA_PHARMACEUTICAL_INDUSTRIES_LTD,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=83beb506-54d7-4aa4-b749-51c843fd14d8&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=rVolo3XF1vDGVjAw9p769oqmzuk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,ORTHOBULLETS,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=5345be2b-a3e1-43e1-b07c-8bb75019ee4d&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=OcnGkZATif0KoBhHB9mpgrXyJE4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,ATLAS_AIR,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=25502579-51cc-437f-adee-b989d21edd3f&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=zXivAjqHyuZalbDqgueee36wxZo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,BIONTECH_AG,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=59286d04-8f9d-4de4-a36b-4d59aceacf92&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=X7ftw0%2BMkXdyzolOcST%2FTvvXEOg%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CITY_NATIONAL_BANK_OF_FLORIDA,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=84e44186-e523-46c1-8efa-de5addbf13b5&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=pzNlwUlXvbJ00wvqBRyAvhDbhRo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,WATSCO_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=2ca7b924-2ffd-4f5e-8328-cf53c6a10da3&ui=%2B6NqJ%2Bs4v8ukqhM7B44WwyjsNsVLJyW0BAhgDFxWcbtfYmornLTtTeSYy0OVguVb&h=yn6zs0iy196pFoZ9oytoTBtb7%2BU%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,THE_STEPSTONE_GROUP_GMBH,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4a44ee61-9510-4269-8299-5cc5887cdca4&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=FMDW0JWQkXXePsxEe9D7azi5jNo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,THE_WASHINGTON_POST,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d8f5ac14-13df-4f5e-b74b-42160f71305c&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=y9vtumX%2FKQsJgxRMofG9Lid3eN4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,HONEYWELL_INTERNATIONAL,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6b60ba67-86b4-4fd6-a695-bd6870b6972f&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=BB5a8yYLptuHDWmmB1iIBXOJS8M%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,C_H_BOEHRINGER_SOHN_AG_AND_CO_KG,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=3a0a1026-4792-4bd5-8521-49709537b967&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=3I1LphRxIvQRDd25ysyxhvs50Zs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,JOHNS_HOPKINS_UNIVERSITY_PRESS,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=70348a9b-24fa-4f2c-b4d4-16fdde87029c&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=BJQHTpBWvwPSfJmrGvh8we6YzZw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,VERIZON_COMMUNICATIONS_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=fe7b3e2f-06cd-4bb7-afa4-14522d323d76&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=ystP13oMchPJQFlQHywW1I2amoE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,FOUNDATION_MEDICINE_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7bda67f8-b48a-41a5-bb8e-b41fd2402b0c&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=WG8cvUiSGNB4bfW1Sj0syUx35lM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,ANALOG_DEVICES,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=f426bfa0-03ce-4255-8c28-3959b8325141&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=W28ln9SuPdKR5JkvmT%2FFb2vNZW4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,BUSINESS_INSIDER,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=48918129-f785-4b43-b5f9-6648454cdd13&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=jl2nERO2ZY6kYr8lwlWLjpIdzjw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,TARMAC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=cd799118-0263-449f-a904-9a14522b73cd&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=pp0UAx8lq2jrAnXUOPswKwJVsvY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CRH_AMERICAS_MATERIALS,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=cf029607-0cf1-45d3-aee3-674665990802&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=x1w3lLN%2BTaYhwzV4Xn9lQOQ3%2BOs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CENGAGE_GROUP,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6c91f7e5-2c22-45a1-9eef-3cb364173e0a&ui=LV%2FNMMeRUyx2M0Z7NeMYfDX32LPGNn6E8%2BEucOLUgoM80Sjrra3OhQvUJrIKj43M&h=6ntrkqlBMa504ryAeesoFFLii58%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,IRON_MOUNTAIN_INCORPORATED,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=e0a85df2-60ca-4473-864f-a77a4c85f6d2&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=eFTVYuUg%2BhnHCcgdcF5URdz5mJ0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,HACHETTE_BOOK_GROUP,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d573f4c7-d9b1-4a47-a6e0-222243c47724&ui=JtqdhcmcAuUbBV6PuuP0UsKJp2EPH3ZnB0%2FHHegERrk5EVw4V%2FRcAsV%2F1j9T%2F%2BN7&h=hukOp2Gg8x9GIp7zCkpMernn%2Fq4%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,CONDE_NAST_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=457b3c73-fdd7-442f-b0bf-27cc92d8c5c9&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=rPC5j0cqRuq2Cz4bXG41HWcXq%2BY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,GULF_AIR,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=93991c45-4676-4a6c-93cb-3ce83226b9ef&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=lXeikzBf7DY2PQmy7MFLISomldM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,DAIICHI_SANKYO_COMPANY_LIMITED,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=dc766499-8e19-4b82-931c-2558273f6b49&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=Sx%2BPPKIYiIc3gxzx7iJLf6Hd%2BQk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,GROWTHSPACE,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6bf9425e-f081-420e-bfd3-e3c58589502c&ui=JtqdhcmcAuUbBV6PuuP0UsKJp2EPH3ZnB0%2FHHegERrk5EVw4V%2FRcAsV%2F1j9T%2F%2BN7&h=bfmjyPpmyJfLb0sTXnpZFZrfAqk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,ELSEVIER,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6dc09c67-9430-4f7c-890f-cbf19e460b6a&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=3DrqdGsxONmt4ohlaP4HTROLzeE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,GRAMMARLY_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=2430bb24-ab89-492a-a3d4-b91334e0fabc&ui=JtqdhcmcAuUbBV6PuuP0UsKJp2EPH3ZnB0%2FHHegERrk5EVw4V%2FRcAsV%2F1j9T%2F%2BN7&h=E%2BOqbjoYiVYIdG4avWbdPnohGP0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CONVERSE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=511f5424-9170-423a-bbe9-0d6527da0fa5&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=sByFL7wgW70bqkT%2F7gZGzh6i%2BMA%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,MARATHON_HEALTH_LLC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d4308aa1-ddea-48a6-9b37-3708d2ec78dc&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=b2Cuo1FQumeMHGgpjDvPyEHRyf8%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,ASCEND_LEARNING,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4908d9b7-0bf1-4619-9c8d-0869cddd6394&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=KiHnbY0j1r686baShQ8Igaeg8MY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,DAIICHI_SANKYO_EUROPE,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=33cbc1e8-3b1a-40d8-a3cb-bd26732c10bd&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=g6pe6rnfU0mbT67UJwfZ9Hva0Yk%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,LEARNINGMATE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=61462a0e-130e-443c-8009-37d5c291e4c7&ui=9e%2F%2BJE6h%2BVVoVGD5gQQdjjiKYQTW%2Bqp8c1ywMOr685LVmCKhYCjEGyKl7rp2LLFT&h=pAmPge8YoQDbLBJLkxm8tca231k%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,DAIICHI_SANKYO_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=7f41d726-5fbc-47b0-b2cc-2def6eb7dc4b&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=T0hgmSDxFj2i1jAHb6LdTIOY3qo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,QIAGEN_N_V,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=246a4c1f-ba9e-4d5e-98d6-b404cf99401e&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=svNTRkjdCF5iYjl0gWT9fLKCt7s%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MACMILLAN,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=a006ddb5-763a-40e9-9955-032ccc76009f&ui=fsa0mICy8dmi9%2FCrEfokNPc%2BZwk%2FAr9jw8MXDFDc30CnjkkcnBYbTMOOl5vaB85L&h=Z4v89tl64Rit3eAv81n1KRni%2FWI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,EMIRATES_GROUP,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=39c07568-291a-4c0b-a9b8-37c458fc4f72&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=uPyiHkC2aSg445KQfihNIV81GLI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CRH_PLC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=0715eb30-a683-45e3-b5f1-45a9789e2974&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=vPBBiLkgajk4e3W8gCB0q6M6DxE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,DOW_JONES_AND_COMPANY_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6905ebae-4252-4699-801c-35c132fa3b8a&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=%2Bg%2FU7bu8SqoqhoXiq5WlYOwsakY%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,SAUDI_ARABIAN_OIL_COMPANY,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=11484d8e-250f-4c9d-944c-e34bc1a74566&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=wwhaUDl3oDqXeMvJ1Komkhb3Q84%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,A_S_WATSON_GROUP,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d183a337-4766-4c5c-b5b8-3767ad9ab9aa&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=lruxAJY7Ok%2BwpoaJSM2E%2BF28mkI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,PENGUIN_RANDOM_HOUSE,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=26db117f-6367-4831-a2cb-d90b158c9637&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=Q8m5id8leoi7zthSWASsIxlrcT0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,DANONE_S_A,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=b19e6e2e-6d32-4720-8fab-085ebb43bc7b&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=%2BwCiKZ91aOEbSTDQy8VMlRslSVM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,TEXTRON_INC,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1ed1c3c4-cd3b-4cb7-bde3-82194b637643&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=%2FWIh0qLbZi41qu9CQ4k8fhDQniU%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,JM_FAMILY_ENTERPRISES,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=c730839a-335e-4db0-ba59-f5f978812a2f&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=f1nd1iNakwDgEQkxF0L2%2FAakR%2BQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,S_AND_P_GLOBAL_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=ac22d7c8-4463-4519-84b9-1daba7c5e9f8&ui=w20vyTg6MJEHvR%2BCt4DOby1oRWyyAisGxUWO1R%2BOQU0I9eBjKriS%2FP2xKHOziUex&h=mV3yLEwtm83ZBw3aCK6PED0H9cw%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CISCO_SYSTEMS_INC,2025-Q1,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=f7db6ce1-b404-4978-8000-5e0380e344da&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=YfWVoxzF8y8J88NDGzLWHNtLEiM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MACMILLAN_PUBLISHERS,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1bb511ef-f32c-4a86-b136-8a3adf7a32ea&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=tL5bM%2FBPtWQWNNJ1UbknHINSUeU%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,LOGICMONITOR,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=2e1ab063-a972-498b-8629-c7d267c30985&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=TF8PkD1d2Pli8Hf72mKjbyDU284%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,SERVICE_CORPORATION_INTERNATIONAL,2024-Q4,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=2cd8c8c4-223a-4f5f-b54f-e01a6e16c119&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=uWwDt4yogw8yXIhwpUbbocFh0F8%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
	var iframes = document.querySelectorAll(""iframe"");
	if (iframes && iframes.length) {
	for (var i = 0; i < iframes.length; i++) {
		var iframe = iframes[i];
		if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
		console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
		iframe.style.height = e.data.height + ""px""; 
		}
	}
	}
}, false);
}
</script>"
Q,DE_BEERS_GROUP,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=526aefdb-d56c-42a8-8fe2-fd74d4bbbdbb&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=aYBM0DIYbbdw%2FTkwzK3Tkg9zPr0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MURPHY_HOFFMAN_COMPANY_MHC_KENWORTH,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=4ed01a80-9b8c-4bfb-ac9c-b0fd456b27ea&ui=LV%2FNMMeRUyx2M0Z7NeMYfDX32LPGNn6E8%2BEucOLUgoM80Sjrra3OhQvUJrIKj43M&h=6tVIMuU%2FWcGQxUKmzJOVnxwt6lQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,NATIONWIDE_PET_INSURANCE,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=fb42431e-2a9f-418b-afc2-652411daf411&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=grV7iSE2CwE0IfbJIVzbwT5Ccvs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,VERTEX_PHARMACEUTICALS_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=cf597ece-6d51-48cb-b5c2-aa199f004e31&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=upUDJBh9Yed1paIcMAow3UxTNj8%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,SWISS_RE,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=fb4a29b0-69f0-4ab9-b468-3d4e93c6f6dd&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=EVBU5aHNyFmBesWk7%2Fwc890bPHM%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MILLIMAN_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=32c68b9c-3a28-4bdb-bbd7-7b9e8d076b6a&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=q8BMEYrizMmhjTcUaxYzDijpmtg%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,NATIONWIDE_FINANCIAL,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=a1c15c1b-36a8-4e51-8db6-d02156924320&ui=JG%2B73SoKTDj2Fhj5J5PUk1gVjQoSpeMlFwHdODKBQBdzp5fK%2Fol2y3yqf1bTkIDH&h=lSD3Ey4mIDxXHehrr52%2BBgFl%2BtQ%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,NIKE_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height:800px"" src=""https://service.infongen.com/11/Webpart/Show?id=e793c5fa-a4f7-424f-b7f6-48d17085341b&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=%2BvT5fU903RRau0EZvrmtwpKg5EE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,NATIONWIDE_MUTUAL_INSURANCE_COMPANY,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=5f8b4a0d-5e60-4751-acfe-85ad37b7c036&ui=OoW4w3CzeNhXML%2Bc8DFX8RzzvdyGwJrDBM%2Fwr3FEb9vs6CvkHWfON7HTTR1uoXfJ&h=QK%2FF5xD2QwnZzjL84HnhsxvNJuE%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,MASTERCARD,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800 px"" src=""https://service.infongen.com/11/Webpart/Show?id=f15da279-0db3-4ae4-b45b-6834a4ae292e&ui=zb3t82E3odQ13ecMDe%2FQXFHePL%2BmrKX4iDQLLCXeMAs4%2Bobh3o8k4pTjahYKWeno&h=R12x9p1Y8AhYREiEqfABP3Q0oL8%3D""></iframe><script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
  var iframes = document.querySelectorAll(""iframe"");
  if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
      var iframe = iframes[i];
      if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
      }
    }
  }
}, false);
}
</script>"
Q,COCHLEAR_LIMITED,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=004a09cd-decf-487c-b845-ce2e3d2ac3cb&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=8cCvbF3vPVafVELLeFPwczPJ3yI%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,SOUTHWEST_AIRLINES,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=fa435a07-7882-4dab-b19b-9391c07061ac&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=Up72%2BDcfqs61ph5urV%2FKQnMZacs%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CONSUMER_REPORTS_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=d40eb4e2-a37d-4409-8afc-2219ea9d5c91&ui=PoEYDWbWLWUDBmwsNYWbfx0tZgNZArBRjZnZy7J5gZJEhDvY9o6TMj%2FziF2bNmv1&h=cN7p0hlnaN5VZTxo7gmqdA1oI38%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,ARTICULATE_GLOBAL_LLC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=6fc3cfcf-b0fa-453e-af35-11f72f21216b&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=iJniHwSiHNgQ6oFtLfckMjgKNRo%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,KOAN,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=be391be2-8870-449c-a890-5d1704fd6ad5&ui=h05veNonaiOg9nEo3GHMjzyL8u8x2dJPSWvVhxSmCRvPwjqNy4pe21gYAyf%2FU0lU&h=R7ROrTLLHY43WhAqhtImMZIL8d0%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,IMDEX,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=300e7c56-44fe-4e64-8f96-3b5efc57e13e&ui=sVS7lLM%2FS5ojCVmvp4jjXkNqF4z7ZIR6JiQL37Q3RruqNdECeNa0HoIewJ9AJmZY&h=jsfRyyhkEIcicfW9QP8RieHY6ic%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,CITIGROUP_INC,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=1627caad-7935-4e74-a5db-bc6a7e5e989e&ui=K1gI8ZmPiLwWgyYvHWURcab9ETwuUt7itiOOyKBYipLfsUFYCRyLl%2BwTCYlSwVKh&h=Pm2yfKJ0GYgPCU6XDLCPsF1OaDg%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
Q,TESCO_BANK,2025-Q2,"<iframe frameborder=""0"" style=""display: block; width: 100%; height: 800px"" src=""https://service.infongen.com/11/Webpart/Show?id=84f4c0ab-55da-460a-981e-a5d1b3d0af4e&ui=0vUVn85c%2B6OIX1qX0hmyy1ugQDZAHVpCqefCFXhrHXlzLAFXlfc%2FzZScaLx8ZNEP&h=8wWaSXvhKvPNGaYOly1ZC7J922o%3D""></iframe>
<script language=""javascript"" type=""text/javascript"">
if (typeof addedEventListener === 'undefined') {
var addedEventListener = true;
// the variable is defined
var eventMethod = window.addEventListener ? ""addEventListener"" : ""attachEvent"";
var eventer = window[eventMethod];
var messageEvent = eventMethod == ""attachEvent"" ? ""onmessage"" : ""message"";
eventer(messageEvent,function(e) {
    var iframes = document.querySelectorAll(""iframe"");
    if (iframes && iframes.length) {
    for (var i = 0; i < iframes.length; i++) {
        var iframe = iframes[i];
        if (iframe.src && iframe.src.length && (iframe.src.indexOf(""Webpart/Show?id="" + e.data.widgetId) != -1) && (iframe.offsetHeight != e.data.height)){ 
        console.log(""id="" + e.data.widgetId + "" h="" + e.data.height + "" oldheight="" + iframe.offsetHeight);
        iframe.style.height = e.data.height + ""px""; 
        }
    }
    }
}, false);
}
</script>"
