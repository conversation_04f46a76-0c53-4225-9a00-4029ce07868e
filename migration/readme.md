# Migration Guide - v1.0.0

This document outlines the migration process from version 0.2.0 to version 1.0.0.

## Migration Steps

### 1. Pull Artifacts
Pull the artifacts from the source workspace (v0.2.0) and generate the code for the new version (v1.0.0).

**Script:** `pull_artifacts.py`

### 2. Pull Datasets
Pull the datasets from the source workspace (v0.2.0) and save them to the local directory.

**Script:** `pull_datasets.py`

### 3. Update Datasets
Update the datasets to the new format.

**Script:** `update_datasets.py`

### 4. Update Assets
Update the assets such as codelists and glossaries with values derived from the datasets.

**Script:** `update_assets.py`

### 5. Push Artifacts
Push the artifacts to the target workspace (v1.0.0).

**Script:** `push_artifacts.py`

### 6. Push Datasets
Push the datasets to the target workspace (v1.0.0).

**Script:** `push_datasets.py`

### 7. Create Location Representation Map
Create representation map for location identification. Push the approved values from the old representation map.

**Script:** `push_repr_map.py`

### 8. Create Job Title Representation Map
Create representation map for job title identification.

**Note:** No approved values from the old representation map available.

### 9. Create Ingestion Pipelines
Create ingestion pipelines.

**Note:** No specific script mentioned.

### 10. Create Transformations
Create transformations at QuantHub and run them for financials and top outsource metrics. The data in the raw financials and top outsource metrics datasets needs to be transformed and split into multiple datasets.

**Scripts:**
- `transformations/financials.py`
- `transformations/top_outsource_metrics.py`
