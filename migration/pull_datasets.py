import os

# from quanthub.structures.clients import create_version_increment_client
# from quanthub.structures.databridge import QuantHubDataBridge
from se_data_pipeline.component.utils import init_data_bridge
from quanthub.transformations.utils import init_local_config
from quanthub_pipeline.system import data_api as qh

WORKSPACE = "Presales:Account_Report"
init_local_config(WORKSPACE)

data_bridge = init_data_bridge()
pandas_reader = qh.create_pandas_reader()
pandas_writer = qh.create_pandas_writer()

directory_path = "migration/source_datasets/"
if not os.path.exists(directory_path):
    os.makedirs(directory_path)

if __name__ == "__main__":
    dataflows = (
        "SR:ADDITIONAL_INFORMATION(3.0.0)",
        "SR:BUSINESS_STRUCTURE(7.0.0)",
        "SR:COMPANY_ENTITIES(1.0.0)",
        "SR:COMPETITORS(9.1.0)",
        "SR:CURRENT_ENGAGEMENTS(9.0.0)",
        "SR:DATA_AVAILABILITY(2.0.0)",
        "SR:DRIVERS_AND_PRIORITIES(3.0.0)",
        "SR:EVENTS(9.0.0)",
        "SR:FEATURED_CLIENTS(3.0.0)",
        "SR:FINANCIALS(8.0.2)",
        "SR:FINANCIALS_QUARTERLY(1.0.0)",
        "SR:FUNDINGS(3.0.0)",
        "SR:GENERAL_OVERVIEW(15.0.0)",
        "SR:INFONGEN(1.0.0)",
        "SR:IT_JOB_POSTINGS(10.0.0)",
        "SR:IT_WORKFORCE_LOCATIONS(10.0.0)",
        "SR:IT_WORKFORCE_SKILLS(7.0.0)",
        "SR:JOB_TITLES_IN_DEMAND(6.0.0)",
        "SR:KEY_PEOPLE(11.0.0)",
        "SR:MANAGEMENT_TEAM_CHANGES(8.0.0)",
        # "SR:OPPORTUNITIES(1.0.0)",
        "SR:PROJECTS(11.0.0)",
        "SR:SWOT_ANALYSIS(1.0.0)",
        "SR:TECHNOLOGY_STACK(9.0.0)",
        "SR:TOP_OUTSOURCING_METRCIS(2.0.0)",
    )
    for dataflow in dataflows:
        df = pandas_reader.read(WORKSPACE, dataflow)
        normalized_name = dataflow.replace(":", "-")
        df.to_csv(f"{directory_path}{normalized_name}.csv", index=False)
        print(f"Saved {normalized_name}")
