id,name,description,__parent_id,__annotations
AZUR<PERSON>,Azure,,,{}
MICROSOFT_365,Microsoft 365,,,{}
AWS,AWS,,,{}
FIVE9,Five9,,,{}
SALESFORCE,Salesforce,,,{}
FIELD_SERVICE,Field Service,,,{}
APEX,Apex,,,{}
LIGHTNING_APPS,Lightning Apps,,,{}
POWER_BI,Power BI,,,{}
ISTIO,Istio,,,{}
HELM,Helm,,,{}
ARGO,Argo,,,{}
DOCKER,Docker,,,{}
RED_HAT,Red Hat,,,{}
CONNECTWISE_AUTOMATE,Connectwise Automate,,,{}
WORKDAY,Workday,,,{}
ORACLE,Oracle,,,{}
CDK,CDK,,,{}
ADVENT,Advent,,,{}
STERLING_BACKGROUND_CHECK,Sterling Background Check ,,,{}
SAP_CONCUR,SAP Concur,,,{}
ADOBE_WORKFRONT_FUSION,Adobe Workfront Fusion,,,{}
CLOVER,<PERSON><PERSON>,,,{}
PROFISEE,Profisee,,,{}
POSTGRESQL,PostgreSQL,,,{}
MONGODB,MongoDB,,,{}
DATABRICKS,Databricks,,,{}
KOTLIN_BASED_REST_MICROSERVICE_INFRASTRUCTURE,Kotlin-based REST microservice infrastructure,,,{}
REACT,React,,,{}
NEXTJS,NextJS,,,{}
AZURE_KUBERNETES_SERVICE,Azure Kubernetes Service,,,{}
MERN_STACK,MERN stack,,,{}
SAP,SAP,,,{}
HADOOP,Hadoop,,,{}
SPARK,Spark,,,{}
MICROSOFT_SQL_SERVER,Microsoft SQL Server,,,{}
ORACLE_DB,Oracle DB,,,{}
ARANGODB,ArangoDB,,,{}
ENOVIA_SMARTEAM,ENOVIA SmarTeam,,,{}
MICROSOFT_PROJECT_SERVER,Microsoft Project Server,,,{}
FLEXLM,FlexLM,,,{}
LMX,LMX,,,{}
REPRISE,Reprise,,,{}
VMWARE,Vmware,,,{}
IBM_VIRTUALIZATION,IBM Virtualization,,,{}
SERVICENOW,ServiceNow,,,{}
CISCO_ISE,Cisco ISE,,,{}
ASA,ASA,,,{}
PALO_ALTO,Palo Alto,,,{}
CHECKPOINT,Checkpoint,,,{}
JUNIPER,Juniper,,,{}
FIREMON,Firemon,,,{}
ARISTA,Arista,,,{}
MANAGEENGINE,ManageEngine,,,{}
SOLARWINDS,Solarwinds,,,{}
INTUNE,Intune,,,{}
NETSCOUT,NetScout,,,{}
CATIA,CATIA,,,{}
SOLIDWORKS,SolidWorks,,,{}
GOOGLE_CLOUD_PLATFORM,Google Cloud Platform,,,{}
TABLEAU,Tableau,,,{}
DOMO,Domo,,,{}
TERADATA,Teradata,,,{}
MULESOFTS,MuleSofts,,,{}
BALSAMIQ,Balsamiq,,,{}
CURATED,Curated,,,{}
AMAZON_NEPTUNE,Amazon Neptune,,,{}
ADOBE_CAMPAIGN,Adobe Campaign,,,{}
AMAZON_CLOUDWATCH,Amazon CloudWatch,,,{}
ZABBIX,Zabbix,,,{}
VICTOROPS,VictorOps,,,{}
AMAZON_REDSHIFT,Amazon Redshift,,,{}
HIVE_WAREHOUSES,Hive Warehouses,,,{}
SQL,SQL,,,{}
PORTFOLIO_BI_OMS,Portfolio BI OMS,,,{}
IVP_SRM,IVP SRM,,,{}
KYRIBA,Kyriba,,,{}
MIMECAST,Mimecast,,,{}
ADP,ADP,,,{}
ARGUS,Argus,,,{}
COSTAR,CoStar,,,{}
NEO4J,Neo4j,,,{}
ELASTICSEARCH,Elasticsearch,,,{}
CHATGPT,ChatGPT,,,{}
SWAGGER,Swagger,,,{}
OPENAPI,OpenAPI,,,{}
TENSORFLOW,Tensorflow,,,{}
PYTORCH,PyTorch,,,{}
MLFLOW,MLflow,,,{}
XGBOOST,XGBoost,,,{}
SCIKIT_LEARN,Scikit-Learn,,,{}
PYSPARK,PySpark,,,{}
KERAS,Keras,,,{}
PULUMI,Pulumi,,,{}
GRABJOBS,GrabJobs,,,{}
APPCAST,Appcast,,,{}
NETSUITE,NetSuite,,,{}
ACCELQ,accelQ,,,{}
AWS_CLOUDFORMATION,AWS CloudFormation,,,{}
BITBUCKET,Bitbucket,,,{}
CIRCLECI,CircleCI,,,{}
GRAFANA,Grafana,,,{}
IBM_RATIONAL_CLEARCASE,IBM Rational ClearCase,,,{}
JENKINS,Jenkins,,,{}
GRADLE,Gradle,,,{}
GOCD,GoCD,,,{}
GITLAB,Gitlab,,,{}
TEAMCITY,Teamcity,,,{}
AZURE_RESOURCE_MANAGER,Azure Resource Manager,,,{}
AWS_CODEDEPLOY,AWS CodeDeploy,,,{}
AWS_CODEPIPELINE,AWS CodePipeline,,,{}
DOCKER_SWARM,Docker Swarm,,,{}
RED_HAT_ANSIBLE_AUTOMATION_PLATFORM,Red Hat Ansible Automation Platform,,,{}
HARNESS,Harness,,,{}
MICRO_FOCUS_ACCUREV,Micro Focus AccuRev,,,{}
SAUCE_LABS,Sauce Labs,,,{}
JFROG_PLATFORM,JFrog Platform,,,{}
CUTOVER,Cutover,,,{}
CRUISECONTROL,CruiseControl,,,{}
ROBOT_FRAMEWORK,Robot Framework,,,{}
DATADOG,Datadog,,,{}
ANGULARJS,AngularJS,,,{}
APACHE_JMETER,Apache Jmeter,,,{}
APPIUM,Appium,,,{}
ATLASSIAN_JIRA,Atlassian JIRA,,,{}
JUNIT,Junit,,,{}
NODE_JS,Node.js,,,{}
SELENIUM,Selenium,,,{}
SONARQUBE,SonarQube,,,{}
XCODE,Xcode,,,{}
ANDROID_STUDIO,android studio,,,{}
AWS_COMMAND_LINE_INTERFACE,AWS Command Line Interface,,,{}
ASP_NET,ASP.NET,,,{}
MATERIAL_UI,Material UI,,,{}
DJANGO,Django,,,{}
RUBY_ON_RAILS,Ruby on Rails,,,{}
ANGULAR,Angular,,,{}
LARAVEL,Laravel,,,{}
NUNIT,Nunit,,,{}
MICROSOFT_AZURE_DEVOPS,Microsoft Azure DevOps,,,{}
VUE_JS,Vue.js,,,{}
REACT_NATIVE,React Native,,,{}
VIEWBOX,Viewbox,,,{}
VISUALFORCE,Visualforce,,,{}
TESTNG,TestNG,,,{}
WEBDYNPRO,Webdynpro,,,{}
DRUPAL,Drupal,,,{}
CRAFT_CMS,Craft CMS,,,{}
MYSQL,MySQL,,,{}
SAP_HANA,SAP HANA,,,{}
AMAZON_DYNAMODB,Amazon DynamoDB,,,{}
REDIS,Redis,,,{}
MICROSOFT_SQL,Microsoft SQL,,,{}
MATLAB,Matlab,,,{}
QUICKBOOKS,QuickBooks,,,{}
VMWARE_VCENTER,VMWare vCenter,,,{}
SCALABLE_SOFTWARE,Scalable Software,,,{}
SAP_ARIBA,SAP Ariba,,,{}
SAP_GRC,SAP GRC,,,{}
COVEO,Coveo,,,{}
AWS_WAF,AWS WAF,,,{}
FLUENTD,fluentd,,,{}
OKTA,Okta,,,{}
RAPID7,Rapid7,,,{}
SAILPOINT,SailPoint,,,{}
CONTRAST_SECURITY,Contrast Security,,,{}
MICROSOFT_DEFENDER,Microsoft Defender,,,{}
HOUSECALL_PRO,Housecall Pro,,,{}
WORKSOFT,Worksoft,,,{}
VALGENESIS_VLMS,ValGenesis VLMS,,,{}
INVENTORY_PLANNER,Inventory Planner,,,{}
SAP_ECC,SAP ECC,,,{}
OPENSTACK,OpenStack,,,{}
ZOHO_CRM,Zoho CRM,,,{}
MICROSTRATEGY,MicroStrategy,,,{}
OCTOPUS_DEPLOY,Octopus Deploy,,,{}
SYSTEM_CENTER_OPERATIONS_MANAGER,System Center Operations Manager,,,{}
XERO,Xero,,,{}
SMARTRECRUITERS,SmartRecruiters,,,{}
PRESCREEN,Prescreen,,,{}
CIRCULA,Circula,,,{}
TASKWORLD,Taskworld,,,{}
TRELLO,Trello,,,{}
SQUARE_FOR_RESTAURANTS,Square for Restaurants,,,{}
INFORMATICA,Informatica,,,{}
AWS_BATCH,AWS Batch,,,{}
IOS_SDK,iOS SDK,,,{}
RACKSPACE_PUBLIC_CLOUD,Rackspace Public Cloud,,,{}
PYCHARM,PyCharm,,,{}
ORACLE_ERP_CLOUD,Oracle ERP Cloud,,,{}
MICROSOFT_DYNAMICS_NAV,Microsoft Dynamics NAV,,,{}
CLOUDERA,Cloudera,,,{}
DATASTAX,DataStax,,,{}
LINQ_PLATFORM,LINQ Platform,,,{}
ORACLE_EBS_PROJECTS,Oracle EBS Projects,,,{}
MICROSOFT_HYPER_V,Microsoft Hyper-V,,,{}
VIRTUALBOX,VirtualBox,,,{}
SERVICENOW_IT_SERVICE_MANAGEMENT,ServiceNow IT Service Management,,,{}
FIREEYE,FireEye,,,{}
METASPLOIT,Metasploit,,,{}
PRTG_NETWORK_MONITOR,PRTG Network Monitor,,,{}
CISCO_MERAKI,Cisco Meraki,,,{}
AUTOCAD,AutoCAD,,,{}
REVIT,Revit,,,{}
CERIDIAN_DAYFORCE,Ceridian Dayforce,,,{}
BAMBOOHR,BambooHR,,,{}
BUILDOUT,Buildout,,,{}
AMAZON_API_GATEWAY,Amazon API Gateway,,,{}
CAFFE,Caffe,,,{}
JETBRAINS,JetBrains,,,{}
SITECORE,Sitecore,,,{}
CONTENIDO,Contenido,,,{}
WIX,Wix,,,{}
INTOUCH,InTouch,,,{}
MANAGER_SE,Manager SE,,,{}
OPTUS,Optus,,,{}
TREND_MICRO,Trend Micro,,,{}
ZENDESK,Zendesk,,,{}
HELPDESK,HelpDesk,,,{}
AUTOMATION_ANYWHERE,Automation Anywhere,,,{}
PROACT,ProAct,,,{}
TIVE,Tive,,,{}
GOOGLE_MARKETING_PLATFORM,Google Marketing Platform,,,{}
SEMRUSH,SEMrush,,,{}
KENSHOO,Kenshoo,,,{}
VYOND,Vyond,,,{}
VIMEO,Vimeo,,,{}
AUDACITY,Audacity,,,{}
HIPTEST,Hiptest,,,{}
TESTLINK,TestLink,,,{}
TWILIO,Twilio,,,{}
ATTENDANCE_ON_DEMAND,Attendance on Demand,,,{}
SALARY_FINANCE,Salary Finance,,,{}
ROOMMASTER,roomMaster,,,{}
OMNIBEES,Omnibees,,,{}
PROLIANT,Proliant,,,{}
REQUEST_TRACKER,Request Tracker,,,{}
OPTIMIZELY,Optimizely,,,{}
CLOUDFOUNDRY,Cloudfoundry,,,{}
OPENSHIFT,OpenShift,,,{}
OPENCV,OpenCV,,,{}
PEGA_PLATFORM,Pega Platform,,,{}
MAVENLINK,Mavenlink,,,{}
ENTERPRISE_ARCHITECT,Enterprise Architect,,,{}
ULTIPRO,UltiPro,,,{}
PLANSOURCE,PlanSource,,,{}
MARKETPAY,MarketPay,,,{}
HASHICORP_TERRAFORM,HashiCorp Terraform,,,{}
SOAPUI,SoapUI,,,{}
VREALIZE_AUTOMATION,vRealize Automation,,,{}
URBANCODE,UrbanCode,,,{}
SONARCLOUD,SonarCloud,,,{}
ELASTICSEARCH_LOGSTASH,Elasticsearch Logstash,,,{}
ORACLE_GOLDENGATE,Oracle GoldenGate,,,{}
VEEAM,Veeam,,,{}
EMC_AVAMAR,EMC Avamar,,,{}
MICROSOFT_POWER_QUERY,Microsoft Power Query,,,{}
COUCHDB,CouchDB,,,{}
KNOCKOUT_JS,Knockout.js,,,{}
MICROSOFT_VISUAL_STUDIO,Microsoft Visual Studio,,,{}
VERSIONONE,VersionOne,,,{}
XAMARIN,Xamarin,,,{}
EASYMOCK,EasyMock,,,{}
REACT_NATIVE_VUE_JS,React Native Vue.js,,,{}
CONFIGURE_IT,Configure.IT,,,{}
RXJS,RxJS,,,{}
APPDYNAMICS,AppDynamics,,,{}
IBM_WEBSPHERE,IBM WebSphere,,,{}
HTML5,HTML5,,,{}
MICROSOFT_SYSTEM_CENTER,Microsoft System Center,,,{}
NAGIOS,Nagios,,,{}
VMWARE_VSPHERE,VMware vSphere,,,{}
WIRESHARK,Wireshark,,,{}
PAPERTRAIL,PaperTrail,,,{}
CLIENT_PORTAL,Client Portal,,,{}
FEDEX_SHIP_MANAGER,FedEx Ship Manager,,,{}
GOOGLE_ANALYTICS,Google Analytics,,,{}
HOTJAR,Hotjar,,,{}
LEAD_LIAISON,Lead Liaison,,,{}
MARKETO,Marketo,,,{}
CONSTANT_CONTACT,Constant Contact,,,{}
KNAK,knak.,,,{}
PARDOT,Pardot,,,{}
INVISION,InVision,,,{}
MICROSOFT_VISIO,Microsoft Visio,,,{}
ADOBE_CREATIVE_CLOUD,Adobe Creative Cloud,,,{}
MOCKFLOW,Mockflow,,,{}
CALUMO,CALUMO,,,{}
PLANFUL,Planful,,,{}
CROWDSTRIKE,CrowdStrike,,,{}
CRADLEPOINT,Cradlepoint,,,{}
DYNAMIX,DynaMiX,,,{}
VMWARE_NSX,VMware NSX,,,{}
PLANVIEW,PlanView,,,{}
MONDAY_COM,Monday.com,,,{}
AMAZON_S3,Amazon S3,,,{}
AMAZON_VPC,Amazon VPC,,,{}
AWS_CDK,AWS CDK,,,{}
AMAZON_DYNAMOBD,Amazon DynamoBD,,,{}
AZURE_SITE_RECOVERY,Azure Site Recovery,,,{}
AMAZON_RDS,Amazon RDS,,,{}
AWS_CODEBUILD,AWS CodeBuild,,,{}
AWS_GREENGRRASS,AWS Greengrrass,,,{}
AWS_KEY_MANAGEMENT_SERVICE,AWS Key Management Service,,,{}
CORTANA,Cortana,,,{}
ALEXA,Alexa,,,{}
6SENSE,6sense,,,{}
SALESFORCE_CRM,Salesforce CRM,,,{}
CHURNZERO,ChurnZero,,,{}
SUMO_LOGIC,Sumo Logic,,,{}
RUNDECK,Rundeck,,,{}
INFOR_M3,Infor M3,,,{}
AMAZON_EC2_SYSTEMS_MANAGER,Amazon EC2 Systems Manager,,,{}
GOOGLE_APIGEE_API_MANAGEMENT_PLATFORM,Google Apigee API Management Platform,,,{}
AWS_DATA_PIPELINE,AWS Data Pipeline,,,{}
AMAZON_EBS,Amazon EBS,,,{}
SQLALCHEMY,SQLAlchemy,,,{}
JAVA,Java,,,{}
PYTHON,Python,,,{}
CSHARP,C#,,,{}
JAVASCRIPT,JavaScript,,,{}
HTML,HTML,,,{}
AMAZON_ECTWO,Amazon EC2,,,{}
APPSHEET,AppSheet,,,{}
KTWELVE,K12,,,{}
HADOOP_HDFS,Hadoop HDFS,,,{}
CITRIX,Citrix,,,{}
OPSGENIE,OpsGenie,,,{}
NGINX,NGINX,,,{}
AMAZON_ROUTE_FIFTYTHREE,Amazon Route 53,,,{}
ADOBE_CREATIVE_SUITE,Adobe Creative Suite,,,{}
SUCCESSFACTORS_LEARNING,SuccessFactors Learning,,,{}
BABELWAY,Babelway,,,{}
DSPACE,Dspace,,,{}
ACQUIA,Acquia,,,{}
CONTENTDM,CONTENTdm,,,{}
ACQUIA_CLOUD_SITE_FACTORY,Acquia Cloud Site Factory,,,{}
AUTH0,Auth0,,,{}
SALESFORCE_MARKETING_CLOUD,Salesforce Marketing Cloud,,,{}
BING_ADS,Bing Ads,,,{}
GOOGLE_ADS,Google Ads,,,{}
GOOGLE_SEARCH_CONSOLE,Google Search Console,,,{}
ADOBE_PREMIERE,Adobe Premiere,,,{}
CAMTASIA,Camtasia,,,{}
MICROSOFT_WORD,Microsoft Word,,,{}
DOCUSIGN,DocuSign,,,{}
ADOBE_CAPTIVATE,Adobe Captivate,,,{}
WOOCOMMERCE,WooCommerce,,,{}
ADOBE_COMMERCE_CLOUD,Adobe Commerce Cloud,,,{}
KOHA,Koha,,,{}
MEMCACHED,Memcached,,,{}
BUILDERTREND,Buildertrend,,,{}
MARKETO_SALES_CONNECT_FORMERLY_TOUTAPP,Marketo Sales Connect (formerly ToutApp),,,{}
HOOTSUITE,Hootsuite,,,{}
SPRING_CLOUD,Spring Cloud,,,{}
IMAGERIGHT,ImageRight,,,{}
JPROFILER,jprofiler,,,{}
ECTIVE,Ective,,,{}
STREAMSETS,StreamSets,,,{}
EHOST,eHost,,,{}
APACHE_SOLR,Apache Solr,,,{}
AMAZON_EVENTBRIDGE,Amazon EventBridge,,,{}
OPTINMONSTER,OptinMonster,,,{}
CENTOS,CentOS,,,{}
DEBIAN,Debian,,,{}
MS_SQL,MS SQL,,,{}
WORDPRESS,Wordpress,,,{}
ATLASSIAN_CLOUD,Atlassian Cloud,,,{}
HIVE,Hive,,,{}
SURVEYMONKEY,SurveyMonkey,,,{}
ALCHEMER,Alchemer,,,{}
QUALAROO,Qualaroo,,,{}
CPLUSPLUS,C++,,,{}
SCALA,Scala,,,{}
TYPESCRIPT,Typescript,,,{}
BASH,Bash,,,{}
LOOKER,Looker,,,{}
CSS,CSS,,,{}
KOTLIN,Kotlin,,,{}
RDBS,RDBs,,,{}
SAGEMAKER,Sagemaker,,,{}
GUROBI,Gurobi,,,{}
CPLEX,Cplex,,,{}
GOOGLE_ORTOOLS,Google ORTools,,,{}
AIRFLOW,Airflow,,,{}
BUDDY,Buddy,,,{}
ADOBE_ANALYTICS,Adobe Analytics,,,{}
HUBSPOT,HubSpot,,,{}
ADOBE_PHOTOSHOP,Adobe Photoshop,,,{}
VEEM,Veem,,,{}
MCAFEE,McAfee,,,{}
GOOGLE_BIGQUERY,Google BigQuery,,,{}
MICROSOFT_ACCESS,Microsoft Access,,,{}
SMARTSHEET,Smartsheet,,,{}
BLUECORE,Bluecore,,,{}
ORACLE_COMMERCE,Oracle Commerce,,,{}
SITESPECT,SiteSpect,,,{}
ECLIPSE_IDE,Eclipse IDE,,,{}
ATLASSIAN_CONFLUENCE,Atlassian Confluence,,,{}
ACTIVE_DIRECTORY,Active Directory,,,{}
SQUARESPACE,Squarespace,,,{}
FUSEBOX,Fusebox,,,{}
CAREERBUILDER_APPLICANT_TRACKING,CareerBuilder Applicant Tracking,,,{}
DAISY_POS,Daisy POS,,,{}
RTI_TOTAL_MANAGEMENT,RTI Total Management,,,{}
DOVE_POS,Dove POS,,,{}
RED_HAT_SATELLITE,Red Hat Satellite,,,{}
WINDOWS_10,Windows 10,,,{}
WINDOWS_SERVER,Windows Server,,,{}
RED_HAT_ENTERPRISE_LINUX,Red Hat Enterprise Linux,,,{}
CASCADING_STYLE_SHEETS_CSS,Cascading Style Sheets (CSS),,,{}
ORACLE_ESSBASE,Oracle Essbase,,,{}
TRAVERSE_ERP,Traverse ERP,,,{}
PL_SQL,PL/SQL,,,{}
YAML_AIN_T_MARKUP_LANGUAGE,YAML Ain't Markup Language,,,{}
KLAVIYO,Klaviyo,,,{}
SAILTHRU,Sailthru,,,{}
VIRGIN_PULSE,Virgin Pulse,,,{}
NICE_CXONE,NICE Cxone,,,{}
KRONOS,Kronos,,,{}
CASHFLOW_ACCOUNTING_SOFTWARE,Cashflow Accounting Software,,,{}
NINTEX_K2_SOFTWARE,Nintex K2 Software,,,{}
INFOR_USER_ADOPTION_PLATFORM,Infor User Adoption Platform,,,{}
FIXED_ASSET_DEPRECIATION,Fixed Asset Depreciation,,,{}
XILINX_MACHINE_LEARNING,Xilinx Machine Learning,,,{}
WINSPC,WinSPC,,,{}
PAYLOCITY,Paylocity,,,{}
CONTENTFUL,Contentful,,,{}
SHARPSPRING,SharpSpring,,,{}
ACTIVECAMPAIGN,ActiveCampaign,,,{}
RAPLEAF,Rapleaf,,,{}
LIVERAMP,LiveRamp,,,{}
BRAZE,Braze,,,{}
AKENEO,Akeneo,,,{}
SHOPIFY,Shopify,,,{}
SALESFORCE_COMMERCE_CLOUD,Salesforce Commerce Cloud,,,{}
SHIPPO,Shippo,,,{}
NEW_RELIC_ONE,New Relic One,,,{}
AMAZON_EMR,Amazon EMR,,,{}
APACHE_HADOOP,Apache Hadoop,,,{}
ADA_PLATFORM,Ada Platform,,,{}
DEMANDWARE,Demandware,,,{}
CROWDSTRIKE_FALCON_ENDPOINT_PROTECTION,CrowdStrike Falcon: Endpoint Protection,,,{}
CISCO_ASA_FIREWALL,Cisco Asa Firewall,,,{}
CISCO_NX_OS,Cisco NX-OS,,,{}
ADOBE,Adobe,,,{}
FINAL_CUT_PRO_X,Final Cut Pro X,,,{}
DELL_SERVER,Dell Server,,,{}
ORACLE_DISCOVERER,Oracle Discoverer,,,{}
SAP_HYBRIS,SAP Hybris,,,{}
AZURE_ACTIVE_DIRECTORY,Azure Active Directory,,,{}
RETAIL_POS,Retail POS,,,{}
UKG_PRO,UKG Pro,,,{}
SAP_COMMERCE_CLOUD,SAP Commerce Cloud,,,{}
BOPIS,BOPIS,,,{}
HIGHJUMP_TMS,HighJump TMS,,,{}
UIPATH,UiPath,,,{}
AMELIA_AI,Amelia AI,,,{}
CLOUDFLARE,Cloudflare,,,{}
BOOKSTACK,Bookstack,,,{}
AI_OPS,AI Ops,,,{}
_,-,,,{}
VISIO,Visio,,,{}
GA4_ANALYTICS,GA4 Analytics,,,{}
MICROSOFT_CLARITY,Microsoft Clarity,,,{}
ORION,Orion,,,{}
MICROSOFT_EXCHANGE,Microsoft Exchange,,,{}
CIS_BENCHMARK_GROUP_POLICY,CIS Benchmark Group Policy,,,{}
DELL_TECHSUPPORT_IMAGE_CAPTURING_TOOLS,Dell TechSupport Image Capturing Tools,,,{}
UIPATH_STUDIO,UiPath Studio,,,{}
UIPATH_ORCHESTRATOR,UiPath Orchestrator,,,{}
QUICKSIGHT,QuickSight,,,{}
AWS_APPSYNC,AWS AppSync,,,{}
AWS_CODEPIPELINE_CODECOMMIT,AWS CodePipeline/CodeCommit,,,{}
GITLAB_RUNNER,GitLab Runner,,,{}
ANSIBLE,Ansible,,,{}
PAYPAL,PayPal,,,{}
STRIPE,Stripe,,,{}
SQUARE,Square,,,{}
AUTHORIZE_NET,Authorize.Net,,,{}
NODEJS,NodeJS,,,{}
QML,QML,,,{}
QT4_QT5,QT4/QT5,,,{}
R,R,,,{}
TSQL,TSQL,,,{}
ORACLE_EBS_R12,Oracle EBS R12,,,{}
NEPTUNE,Neptune,,,{}
NORTON_SECURITY,Norton Security,,,{}
KASPERSKY_LAB,Kaspersky Lab,,,{}
AVAST,Avast,,,{}
PALO_ALTO_NETWORKS,Palo Alto Networks,,,{}
DNS,DNS,,,{}
TCP_IP,TCP/IP,,,{}
ZENOSS,Zenoss,,,{}
WI_FI_6E,Wi-Fi 6E,,,{}
BLUETOOTH,Bluetooth,,,{}
JAMA,Jama,,,{}
RESTFUL_APIS,RESTful APIs,,,{}
OPENAPI_SPECIFICATION,OpenAPI Specification,,,{}
AWS_API_GATEWAY,AWS API Gateway,,,{}
H2O_AI,H2O.ai,,,{}
SAGE_INTACCT,Sage Intacct,,,{}
AWS_RDS_AURORA,AWS RDS/Aurora,,,{}
SQLITE,SQLite,,,{}
RF_INTERFERENCE,RF interference,,,{}
PERL,Perl,,,{}
YAML,YAML,,,{}
JSON,JSON,,,{}
XML,XML,,,{}
HASHICORP_VAULT,Hashicorp Vault,,,{}
ACLS,ACLs,,,{}
SSO,SSO,,,{}
SAML,SAML,,,{}
MICROSOFT_OFFICE,Microsoft Office,,,{}
GITLAB_CI_CD,GitLab CI/CD,,,{}
AWS_ECR,AWS ECR,,,{}
SVN,SVN,,,{}
PYTEST,Pytest,,,{}
JTEST,Jtest,,,{}
SWAGGER_CODEGEN,Swagger Codegen,,,{}
JIRA,Jira,,,{}
SYNERGY,Synergy,,,{}
ANDROID,Android,,,{}
LINUX_OS,Linux OS,,,{}
LINUX,Linux,,,{}
IOS,iOS,,,{}
AWS_STEP_FUNCTIONS,AWS Step Functions,,,{}
DYNAMODB,DynamoDB,,,{}
EM_STICK,Em-stick,,,{}
REST_ASSURED,Rest Assured,,,{}
RACK,Rack,,,{}
5G,5G,,,{}
IOT,IoT,,,{}
SALESFORCE_SALES_CLOUD,Salesforce Sales Cloud,,,{}
ADVENDIO,Advendio,,,{}
CONFLUENCE,Confluence,,,{}
MODO,Modo,,,{}
CXPRO,CXpro,,,{}
RECONNET,ReconNET,,,{}
SWAGGER_UI,Swagger UI,,,{}
NEDGRAPHICS_SOFTWARE,NedGraphics Software,,,{}
AVATURE,Avature,,,{}
WORKDAY_RECRUITING,Workday Recruiting,,,{}
DYNATRACE,Dynatrace,,,{}
APACHE_TOMCAT,Apache Tomcat,,,{}
ORACLE_TUXEDO,Oracle Tuxedo,,,{}
ORACLE_WEBLOGIC,Oracle WebLogic,,,{}
WEBSPHERE_APPLICATION_SERVER,WebSphere Application Server,,,{}
DATA_FACTS,Data Facts,,,{}
SPLUNK_ENTERPRISE,Splunk Enterprise,,,{}
AZURE_DATA_LAKE_ANALYTICS,Azure Data Lake Analytics,,,{}
IBM_INFOSPHERE_DATASTAGE,IBM InfoSphere DataStage,,,{}
HP_BLADES,HP Blades,,,{}
CHATBOT,ChatBot,,,{}
MICROSOFT_TEAMS,Microsoft Teams,,,{}
MICROSOFT_OUTLOOK,Microsoft Outlook,,,{}
ORACLE_CLOUD,Oracle Cloud,,,{}
INSIGHTVM_NEXPOSE,InsightVM (Nexpose),,,{}
NUMPY_DOWNLOAD,numpy download,,,{}
CONSTRU,Constru,,,{}
AZURE_KUBERNETES_SERVICE_AKS,Azure Kubernetes Service (AKS),,,{}
MOVABLE_INK,Movable Ink,,,{}
CODEFRESH,Codefresh,,,{}
WORKDAY_HCM,Workday HCM,,,{}
ANAPLAN,Anaplan,,,{}
APTTUS_CPQ,Apttus CPQ,,,{}
REDPOINT_CUSTOMER_DATA_PLATFORM,Redpoint Customer Data Platform,,,{}
CUSTOMER_EXPERIENCE_MANAGEMENT,Customer Experience Management,,,{}
AZURE_COSMOS_DB,Azure Cosmos DB,,,{}
SQL_DEVELOPER,SQL Developer,,,{}
TABLEAU_PREP,Tableau Prep,,,{}
TALEND_OPEN_STUDIO,Talend Open Studio,,,{}
IMAC,iMac,,,{}
DELL_OPTIPLEX,Dell OptiPlex,,,{}
AZURE_WINDOWS_VIRTUAL_DESKTOP,Azure Windows Virtual Desktop,,,{}
ADOBE_INDESIGN,Adobe InDesign,,,{}
GLIFFY,Gliffy,,,{}
WALKME,WalkMe,,,{}
AMPLIENCE,Amplience,,,{}
BLOOMREACH_FORMERLY_EXPONEA,Bloomreach (formerly Exponea),,,{}
CANVA,Canva,,,{}
DMARCIAN,dmarcian,,,{}
DOWNSTREAM,Downstream,,,{}
CERTONA,Certona,,,{}
FINDMINE,FindMine,,,{}
QUEST_ARCHIVE_MANAGER,Quest Archive Manager,,,{}
MAILCHIMP,MailChimp,,,{}
GMAIL,Gmail,,,{}
SQL_SERVER_ANALYSIS_SERVICES_SSAS,SQL Server Analysis Services (SSAS),,,{}
IDENTITY_THEFT_PROTECTION,Identity Theft Protection,,,{}
MICROSOFT_SHAREPOINT,Microsoft SharePoint,,,{}
DOCUSHARE,DocuShare,,,{}
TURBONOMIC,Turbonomic,,,{}
AIRWATCH,AirWatch,,,{}
APACHE_CAMEL,Apache Camel,,,{}
EQUITY_EDGE_ONLINE,Equity Edge Online,,,{}
CVENT,Cvent,,,{}
SOCIAL_TABLES,Social Tables,,,{}
SPARK_STREAMING,Spark Streaming,,,{}
TANGOE,Tangoe,,,{}
MEDALLIA_CUSTOMER_EXPERIENCE,Medallia Customer Experience,,,{}
CHRONOSYNC,ChronoSync,,,{}
AZURE_FIREWALL,Azure Firewall,,,{}
SKETCHUP,SketchUp,,,{}
OPENSTREETMAP,OpenStreetMap,,,{}
AUDITBOARD,AuditBoard,,,{}
SALESFORCE_SERVICE_CLOUD,Salesforce Service Cloud,,,{}
HR_DIRECTOR,HR Director,,,{}
MICROSOFT_AZURE_STORAGE,Microsoft Azure Storage,,,{}
PAGERDUTY,PagerDuty,,,{}
THE_ROOM,The Room,,,{}
AFTERPAY,Afterpay,,,{}
WEBTMS,WebTMS,,,{}
APACHE_KAFKA,Apache Kafka,,,{}
AMAZON_SIMPLE_NOTIFICATION_SERVICE_SNS,Amazon Simple Notification Service (SNS),,,{}
STACKSTORM,StackStorm,,,{}
AZURE_APPLICATION_GATEWAY,Azure Application Gateway,,,{}
AISERA,Aisera,,,{}
HBASE,Hbase,,,{}
STORE_LEADS,Store Leads,,,{}
AZURE_LOAD_BALANCER,Azure Load Balancer,,,{}
LOG4J,Log4j,,,{}
MICROSOFT_POWER_PLATFORM,Microsoft Power Platform,,,{}
OPEN_LOYALTY,Open Loyalty,,,{}
AZURE_DNS,Azure DNS,,,{}
ORM_TECHNOLOGIES,ORM Technologies,,,{}
AYTM,AYTM,,,{}
GIGYA,Gigya,,,{}
IBM_INFOSPHERE_MASTER_DATA_MANAGEMENT,IBM InfoSphere Master Data Management,,,{}
PROVIDE_ENTERPRISE,Provide Enterprise,,,{}
IBM_MQ,IBM MQ,,,{}
ORACLE_JD_EDWARDS_ENTERPRISEONE,Oracle JD Edwards EnterpriseOne,,,{}
SAP_S_4HANA,SAP S/4HANA,,,{}
TESTFLIGHT,TestFlight,,,{}
APACHE_CORDOVA,Apache Cordova,,,{}
JAMF,JAMF,,,{}
SOLARWINDS_MOBILE_ADMIN,SolarWinds Mobile Admin,,,{}
PINGID,PingID,,,{}
AZURE_MULTI_FACTOR_AUTHENTICATION,Azure Multi-Factor Authentication,,,{}
INFOBLOX,Infoblox,,,{}
APPNETA,AppNeta,,,{}
AZURE_NETWORK_WATCHER,Azure Network Watcher,,,{}
MICROSOFT_OFFICE_365,Microsoft Office 365,,,{}
APIGEE,Apigee,,,{}
MICROSOFT_FORMS,Microsoft Forms,,,{}
GREAT_LEARNING,Great Learning,,,{}
PAGEPROOF,PageProof,,,{}
INFORMATICA_POWERCENTER,Informatica PowerCenter,,,{}
SOFTWARE_AG_WEBMETHODS,Software AG webMethods,,,{}
PERSADO,Persado,,,{}
AZURE_COMMAND_LINE_INTERFACE_CLI,Azure Command-Line Interface (CLI),,,{}
I_9_ADVANTAGE,I-9 Advantage,,,{}
GENERATION_DIGITAL,Generation Digital,,,{}
XLSTAT,XLSTAT,,,{}
TSYS,TSYS,,,{}
ALIPAY,AliPay,,,{}
ADYEN,Adyen,,,{}
RICHRELEVANCE,RichRelevance,,,{}
COHERENT_PATH,Coherent Path,,,{}
BAMBOO_ROSE,Bamboo Rose,,,{}
AZURE_PORTAL,Azure Portal,,,{}
MICROSOFT_POWERPOINT,Microsoft PowerPoint,,,{}
DELINEA_SECRET_SERVER,Delinea Secret Server,,,{}
AMAZON_SQS,Amazon SQS,,,{}
APEX_PROGRAMMING,Apex Programming,,,{}
HYPER_TEXT_MARKUP_LANGUAGE_HTML,Hyper Text Markup Language (HTML),,,{}
PYTHON_PROGRAMMING_LANGUAGE,Python (Programming Language),,,{}
VBSCRIPT,VBScript,,,{}
CLARITY_PPM,Clarity PPM,,,{}
WRIKE,Wrike,,,{}
AIRTABLE,Airtable,,,{}
STESSA,Stessa,,,{}
FIELD_AGENT,Field Agent,,,{}
MERCHANDISE_ANALYTICS,Merchandise Analytics,,,{}
CB4,CB4,,,{}
ZIPLINE,Zipline,,,{}
MICROSOFT_DEFENDER_ADVANCED_THREAT_PROTECTION,Microsoft Defender Advanced Threat Protection,,,{}
BLUE_PRISM,Blue Prism,,,{}
CISCO_ROUTER,Cisco Router,,,{}
TABLEAU_CRM_FORMERLY_EINSTEIN_ANALYTICS,Tableau CRM (Formerly Einstein Analytics),,,{}
MANAGR,managr,,,{}
DSX,DSX,,,{}
EDUCANDO,Educando,,,{}
VERITAS_NETBACKUP,Veritas NetBackup,,,{}
ITOP,iTop,,,{}
CLICKTALE,ClickTale,,,{}
CONTENTSQUARE,ContentSquare,,,{}
ACOUSTIC_EXPERIENCE_ANALYTICS_TEALEAF,Acoustic Experience Analytics (Tealeaf),,,{}
STROMASYS,Stromasys,,,{}
PTC_CREO,PTC Creo,,,{}
DASH_HUDSON,Dash Hudson,,,{}
DATAMINR,Dataminr,,,{}
MICROSOFT_EXCEL,microsoft excel,,,{}
SECTIGO,Sectigo,,,{}
JIRA_ALIGN_FORMERLY_AGILECRAFT,Jira Align (formerly AgileCraft),,,{}
AZURE_EVENT_HUBS,Azure Event Hubs,,,{}
INFOR_NEXUS,Infor Nexus,,,{}
GOOGLE_TAG_MANAGER,Google Tag Manager,,,{}
HACKERRANK,HackerRank,,,{}
APPLITOOLS,Applitools,,,{}
VISUAL_STUDIO_CODE,Visual Studio Code,,,{}
SOURCE_INTELLIGENCE,Source Intelligence,,,{}
CORE_HCM,Core HCM,,,{}
MICROSOFT_INTUNE,Microsoft InTune,,,{}
FEEDBACK_LOOP,Feedback Loop,,,{}
ADOBE_ILLUSTRATOR,Adobe Illustrator,,,{}
SKYPE_FOR_BUSINESS,Skype for Business,,,{}
MANHATTAN_LABOR_MANAGEMENT,Manhattan Labor Management,,,{}
NETWORK_SOLUTIONS,Network Solutions,,,{}
MONITORO,Monitoro,,,{}
NICE_IEX,NICE IEX,,,{}
KRONOS_WORKFORCE_CENTRAL,Kronos Workforce Central,,,{}
YARD_MANAGEMENT_SYSTEM,Yard Management System,,,{}
AI_MACHINE_LEARNING,AI/Machine Learning,,,{}
SPRING_BOOT,Spring Boot,,,{}
GRAPHQL,GraphQL,,,{}
KUBERNETES,Kubernetes,,,{}
LLM,LLM,,,{}
META_LLAMA,Meta LLAMA,,,{}
CODE_ALPACA,Code Alpaca,,,{}
DATABRICKS_DOLLY,Databricks Dolly,,,{}
PIVOTAL_CLOUD_FOUNDRY,Pivotal Cloud Foundry,,,{}
GOLANG,Golang,,,{}
RUST,Rust,,,{}
WEBASSEMBLY,WebAssembly,,,{}
ANGULAR_11,Angular 11,,,{}
AZURE_SQL_DB,Azure SQL DB,,,{}
AZURE_STORAGE_SERVICES,Azure Storage Services,,,{}
CSHARP_NET,C# .NET,,,{}
BLAZOR_NET,Blazor.Net,,,{}
ASP_NET_CORE_3_1,Asp.Net Core 3.1,,,{}
ASP_NET_CORE,ASP.NET Core,,,{}
AZURE_DATA_FACTORY,Azure Data Factory,,,{}
AZURE_DATABRICKS,Azure Databricks,,,{}
POSTGRES_SQL,Postgres (SQL),,,{}
SNOWFLAKE,Snowflake,,,{}
MONGO_DB,Mongo DB,,,{}
APACHE_CXF,Apache CXF,,,{}
CASSANDRA,Cassandra,,,{}
KAFKA,Kafka,,,{}
ZOOKEEPER,Zookeeper,,,{}
MOCKITO,Mockito,,,{}
EASY_MOCK,Easy Mock,,,{}
POWERMOCKITO,PowerMockito,,,{}
JACKSON,Jackson,,,{}
AWS_ELASTIC_BEANSTALK,AWS Elastic Beanstalk,,,{}
AWS_LAMBDA,AWS Lambda,,,{}
CLOUD_WATCH,Cloud Watch,,,{}
AZURE_KEY_VAULT,Azure Key Vault,,,{}
SPLUNK,Splunk,,,{}
NEW_RELIC,New Relic,,,{}
MICROSERVICES,Microservices,,,{}
FIGMA,Figma,,,{}
AZURE_ARM_TEMPLATES,Azure ARM templates,,,{}
TERRAFORM,Terraform,,,{}
GIT,Git,,,{}
GITHUB,GitHub,,,{}
AZURE_DEVOPS,Azure DevOps,,,{}
GOOGLE_DRIVE,Google Drive,,,{}
MICROSOFT_POWER_BI,Microsoft Power BI,,,{}
MICROSOFT_DYNAMICS_365,Microsoft Dynamics 365,,,{}
MEDIA_PARTNERS,Media Partners,,,{}
GOOGLE_COMPUTE_ENGINE,Google Compute Engine,,,{}
DELL_BOOMI,Dell Boomi,,,{}
SALESFORCE_LIGHTNING_PLATFORM,Salesforce Lightning Platform,,,{}
MANHATTAN_ORDER_MANAGEMENT,Manhattan Order Management,,,{}
YOTPO,Yotpo,,,{}
HYPER_TEXT_MARKUP_LANGUAGE_HTML_JAVASCRIPT,"Hyper Text Markup Language (HTML), JavaScript",,,{}
PROTIVITI,Protiviti,,,{}
JDA_CLOUD,JDA Cloud,,,{}
MANHATTAN_WAREHOUSE_MANAGEMENT,Manhattan Warehouse Management,,,{}
AXURE,Axure,,,{}
UKG_DIMENSIONS,UKG Dimensions,,,{}
VERTEX_AI,Vertex AI,,,{}
BOOMI_INTEGRATIONS_PLATFORM,Boomi integrations platform,,,{}
GCP_COMPUTE_ENGINE,GCP Compute Engine,,,{}
ATLASSIAN_SUITE,Atlassian Suite,,,{}
SALESFORCE_CC,Salesforce CC,,,{}
ADOBE_TARGET,Adobe Target,,,{}
APTITUDE_ACCOUNTING_HUB,Aptitude Accounting Hub,,,{}
SPLUNK_IT_SERVICE_INTELLIGENCE_ITSI,Splunk IT Service Intelligence(ITSI),,,{}
VISUAL_STUDIO_TEAM_SERVICES,Visual Studio Team Services,,,{}
QLIK_SENSE,Qlik Sense,,,{}
QLIKVIEW,QlikView,,,{}
CLICKSTREAM,Clickstream,,,{}
MICROSOFT_POWER_BI_DESKTOP,Microsoft Power BI Desktop,,,{}
RAML,RAML,,,{}
APIGEE_EDGE,Apigee Edge,,,{}
IBM_API_CONNECT,IBM API Connect,,,{}
JOBVITE,Jobvite,,,{}
STREAMLIT,Streamlit,,,{}
AZURE_PIPELINES,Azure Pipelines,,,{}
ROAMBEE,Roambee,,,{}
SLIDO,Slido,,,{}
VERIZON_ACS,Verizon (ACS),,,{}
WORKIVA,Workiva,,,{}
ARCORE,ARCore,,,{}
BASTION_HOST_VPC,Bastion Host VPC,,,{}
APACHE_SPARK_FOR_AZURE_HDINSIGHT,Apache Spark for Azure HDInsight,,,{}
HP_C7000,HP C7000,,,{}
DELL_POWEREDGE_C,Dell PowerEdge C,,,{}
HP_SERVER,HP server,,,{}
HYPERLEDGER,Hyperledger,,,{}
APPSULATE,Appsulate,,,{}
BUGZILLA,Bugzilla,,,{}
APACHE_MAVEN,Apache Maven,,,{}
AZURE_AUTOMATION,Azure Automation,,,{}
MICROSOFT_POWER_AUTOMATE,Microsoft Power Automate,,,{}
NETBEANS,NetBeans,,,{}
MICROSOFT_CLOUD_APP_SECURITY,Microsoft Cloud App Security,,,{}
AWS_CLOUD,AWS Cloud,,,{}
DROPBOX,Dropbox,,,{}
EGNYTE,Egnyte,,,{}
SUGARSYNC,SugarSync,,,{}
DOCLINK,DocLink,,,{}
AMAZON_EFS,Amazon EFS,,,{}
AZURE_FILE_STORAGE,Azure File Storage,,,{}
VIRTANA_PLATFORM,Virtana Platform,,,{}
SALESFORCE_PLATFORM,Salesforce Platform,,,{}
AZURE_SECURITY_CENTER,Azure Security Center,,,{}
WIZ,Wiz,,,{}
PROFI,Profi,,,{}
PAYSCALE,PayScale,,,{}
SALARY_COM,Salary.com,,,{}
SELENIUM_WEBDRIVER,Selenium Webdriver,,,{}
AUTODESK,Autodesk,,,{}
SMARTHOME,Smarthome,,,{}
PROJECTMATES,Projectmates,,,{}
GENESYS_CONTACT_CENTER_SOFTWARE,Genesys Contact Center Software,,,{}
CISCO_UNIFIED_CONTACT_CENTER_ENTERPRISE,Cisco Unified Contact Center Enterprise,,,{}
GOOGLE_KUBERNETES_ENGINE_GKE,Google Kubernetes Engine (GKE),,,{}
TANZU,Tanzu,,,{}
APACHE_MESOS,Apache Mesos,,,{}
DOCKER_HUB,Docker hub,,,{}
KNOTCH,Knotch,,,{}
CONTENTIN,ContentIn,,,{}
AMAZON_CLOUDFRONT,Amazon CloudFront,,,{}
TABOOLA,Taboola,,,{}
ARGO_CD,Argo CD,,,{}
CONVERSOCIAL,Conversocial,,,{}
SAP_HCM,SAP HCM,,,{}
LESSONLY,Lessonly,,,{}
SAP_BPC,SAP BPC,,,{}
ONESTREAM,OneStream,,,{}
ECOVADIS,ecovadis,,,{}
ARTICULATE_360,Articulate 360,,,{}
SALESFORCE_CPQ,Salesforce CPQ,,,{}
JIVOX,Jivox,,,{}
MEDIAMATH_TERMINALONE_MARKETING_OS,MediaMath TerminalOne Marketing OS,,,{}
REVTRAX,RevTrax,,,{}
ADOBE_EXPERIENCE_PLATFORM,Adobe Experience Platform,,,{}
ASPECT_CXP_PRO,Aspect CXP Pro,,,{}
SALESFORCE_KNOWLEDGE,Salesforce Knowledge,,,{}
ER_STUDIO,ER/Studio,,,{}
TOAD_FOR_ORACLE,Toad For Oracle,,,{}
ORACLE_RAC,Oracle RAC,,,{}
EXTREME_NETWORKS,Extreme Networks,,,{}
FASTEXPORT,FastExport,,,{}
DENODO,Denodo,,,{}
APACHE_RANGER,Apache Ranger,,,{}
GITGUARDIAN,GitGuardian,,,{}
ADOBE_AUDIENCE_MANAGER,Adobe Audience Manager,,,{}
TOAD_DATA_POINT,Toad Data Point,,,{}
DATA_PRIVACY_MANAGER,Data Privacy Manager,,,{}
MATILLION,Matillion,,,{}
AMAZON_SAGEMAKER,Amazon SageMaker,,,{}
ELASTICSEARCH_KIBANA,Elasticsearch Kibana,,,{}
IBM_NETEZZA_PERFORMANCE_SERVER,IBM Netezza Performance Server,,,{}
PEGA_CUSTOMER_DECISION_HUB,Pega Customer Decision Hub,,,{}
SAP_ADVANCED_PLANNING_AND_OPTIMIZATION,SAP Advanced Planning and Optimization,,,{}
AMAZON_DSP,Amazon DSP,,,{}
PRAKTIKA,Praktika,,,{}
CITRIX_DAAS,Citrix DaaS,,,{}
QTEST,qTest,,,{}
ADOBE_EXPERIENCE_MANAGER,Adobe Experience Manager,,,{}
ENCASE_FORENSIC,EnCase Forensic,,,{}
LOUDCLOUD,LoudCloud,,,{}
ARUBA,aruba,,,{}
MICRO_FOCUS_DATA_PROTECTOR,Micro Focus Data Protector,,,{}
MXTOOLBOX,MXToolbox,,,{}
VERISIGN,Verisign,,,{}
DATAWEAVE,Dataweave,,,{}
ELASTIC_PATH,Elastic Path,,,{}
FRONTLINE_INSIGHTS_PLATFORM,Frontline Insights Platform,,,{}
SKILLSOFT,Skillsoft,,,{}
ELEARNING_PLATFORM,Elearning Platform,,,{}
OFFICE_365_ADVANCED_THREAT_PROTECTION,Office 365 Advanced Threat Protection,,,{}
SMARTFOCUS,SmartFocus,,,{}
POLITEMAIL,PoliteMail,,,{}
IBM_OPERATIONAL_DECISION_MANAGEMENT,IBM Operational Decision Management,,,{}
EVERYONESOCIAL,EveryoneSocial,,,{}
QUARTZ_JOB_SCHEDULER,Quartz Job Scheduler,,,{}
OPENSSH,OpenSSH,,,{}
GNU_PRIVACY_GUARD,GNU Privacy Guard,,,{}
AWS_CERTIFICATE_MANAGER,AWS Certificate Manager,,,{}
CISCO_ANYCONNECT,Cisco AnyConnect,,,{}
SYMANTEC_ENDPOINT_PROTECTION,Symantec Endpoint Protection,,,{}
IBM_MAXIMO,IBM Maximo,,,{}
QUALTRICS,Qualtrics,,,{}
APPTENTIVE,Apptentive,,,{}
VERITAS_ENTERPRISE_VAULT,Veritas Enterprise Vault,,,{}
IBM_NETCOOL_OPERATIONS_INSIGHT,IBM Netcool Operations Insight,,,{}
CAPACITY,Capacity,,,{}
EHS_COMPLIANCE,EHS Compliance,,,{}
SAP_SALES_AND_DISTRIBUTION_SAP_SD,SAP Sales and Distribution (SAP SD),,,{}
SAP_ERP_CENTRAL_COMPONENT_SAP_ECC,Sap ERP Central Component (SAP ECC),,,{}
AZURE_DEFENDER,Azure Defender,,,{}
ORACLE_SIEBEL_FIELD_SERVICE,Oracle Siebel Field Service,,,{}
CONVERSION_TOOLS,Conversion Tools,,,{}
WINSCP,WinSCP,,,{}
VANTAGEPOINT,VantagePoint,,,{}
SUPERVIZOR,Supervizor,,,{}
FORTINET_FORTIGATE,Fortinet FortiGate,,,{}
PURE1,Pure1,,,{}
ID_TECH,id Tech,,,{}
DEVELOP_DIVERSE,Develop Diverse,,,{}
MAPINFO,Mapinfo,,,{}
GOOGLE_EARTH,Google Earth,,,{}
GOOGLE_MAPS_API,Google Maps API,,,{}
RSA_ARCHER,RSA Archer,,,{}
CRAZY_EGG,Crazy Egg,,,{}
MKDOCS,MkDocs,,,{}
README,ReadMe,,,{}
INFORMATICA_MASTER_DATA_MANAGEMENT_IDENTITY_RESOLUTION,Informatica Master Data Management - Identity Resolution,,,{}
MARCHEX,Marchex,,,{}
XMATTERS,xMatters,,,{}
CREATORIQ,CreatorIQ,,,{}
ORACLE_COMMUNICATIONS_BILLING_AND_REVENUE_MANAGEMENT_BRM,Oracle Communications Billing and Revenue Management (BRM),,,{}
PALO_ALTO_NETWORKS_THREAT_PREVENTION,Palo Alto Networks Threat Prevention,,,{}
AZURE_IOT_HUB,Azure IoT Hub,,,{}
IBM_TIVOLI,IBM Tivoli,,,{}
VERITAS_INFOSCALE,Veritas InfoScale,,,{}
APACHE_POI,Apache POI,,,{}
SPRING_FRAMEWORK,Spring Framework,,,{}
APACHE_SLING,Apache Sling,,,{}
SPRING_BATCH,Spring Batch,,,{}
JDXPERT,JDXpert,,,{}
DUOLINGO,Duolingo,,,{}
APTITUDE_LEASE_ACCOUNTING_ENGINE,Aptitude Lease Accounting Engine,,,{}
WESTLAW,Westlaw,,,{}
LIVEENGAGE,LiveEngage,,,{}
LIVEPERSON,LivePerson,,,{}
WIRECAST,Wirecast,,,{}
HAPROXY,HAProxy,,,{}
NEOLOAD,NeoLoad,,,{}
GOOGLE_MY_BUSINESS,Google My Business,,,{}
AZURE_LOG_ANALYTICS,Azure Log Analytics,,,{}
OUTSYSTEMS,OutSystems,,,{}
INFORMATICA_ENTERPRISE_DATA_CATALOG,Informatica Enterprise Data Catalog,,,{}
VIRUSTOTAL,VirusTotal,,,{}
POWERDNS,PowerDNS,,,{}
GENTRAN,Gentran,,,{}
DELMIA,DELMIA,,,{}
PREDICTIVE_INSIGHTS_FORMERLY_MINTIGO,Predictive Insights (formerly Mintigo),,,{}
APRIMO,Aprimo,,,{}
SAP_NETWEAVER,SAP NetWeaver,,,{}
APACHE_KUDU,Apache Kudu,,,{}
SAP_MASTER_DATA_GOVERNANCE_MDG,SAP Master Data Governance (MDG),,,{}
ATACCAMA_ONE,Ataccama One,,,{}
MEETING_HUB,Meeting Hub,,,{}
BETTERUP,BetterUp,,,{}
APACHE_ACTIVEMQ,Apache ActiveMQ,,,{}
RABBITMQ,RabbitMQ,,,{}
CISCO_IDENTITY_SERVICES_ENGINE,Cisco Identity Services Engine,,,{}
HEADSPIN,HeadSpin,,,{}
QUALCOMM_EXTENSIBLE_DIAGNOSTIC_MONITOR_QXDM,Qualcomm Extensible Diagnostic Monitor (QXDM),,,{}
HASURA,Hasura,,,{}
CRASHLYTICS,Crashlytics,,,{}
KLIK,Klik,,,{}
DOFORMS,doForms,,,{}
APPLE_MOBILE_PHONE,Apple mobile phone,,,{}
IPHONE_14,iPhone 14,,,{}
ORACLE_PEOPLESOFT,Oracle PeopleSoft,,,{}
EXTRAHOP,ExtraHop,,,{}
NETACT,NetAct,,,{}
ALGOSEC,AlgoSec,,,{}
SOURCEFIRE,Sourcefire,,,{}
KENTIK,Kentik,,,{}
QUICKBASE,Quickbase,,,{}
MICROSOFT_ONENOTE,Microsoft OneNote,,,{}
ABBYY_FLEXICAPTURE_SDK,ABBYY FlexiCapture SDK,,,{}
UPSTREAM,UpStream,,,{}
ORACLE_DATA_INTEGRATOR,Oracle Data Integrator,,,{}
UBUNTU,Ubuntu,,,{}
APPLE_IOS,Apple iOS,,,{}
HP_UX,HP-UX,,,{}
CISCO_IOS,Cisco IOS,,,{}
IBM_STERLING_ORDER_MANAGEMENT,IBM Sterling Order Management,,,{}
AZURE_ANALYSIS_SERVICES,Azure Analysis Services,,,{}
AWS_TRUSTED_ADVISOR,AWS Trusted Advisor,,,{}
ISNG_PLATFORM,ISNG Platform,,,{}
ANALYSIS_SERVER,Analysis Server,,,{}
ZEPLIN,Zeplin,,,{}
APACHE_AIRFLOW,Apache Airflow,,,{}
DEXT_PRECISION,Dext Precision,,,{}
SQL_SERVER_MANAGEMENT_STUDIO,SQL Server Management Studio,,,{}
PGADMIN,pgAdmin,,,{}
LEAD_CONNECT,Lead Connect,,,{}
INNOVID,Innovid,,,{}
WINCC,WinCC,,,{}
WIDEO,Wideo,,,{}
JFROG_ARTIFACTORY,JFrog Artifactory,,,{}
WORKSPAN,WorkSpan,,,{}
CYBERSOURCE,CyberSource,,,{}
ADP_PAYROLL,ADP Payroll,,,{}
HACKERONE,HackerOne,,,{}
RELAY_NETWORK,Relay Network,,,{}
FLICKR,Flickr,,,{}
CENTRO,Centro,,,{}
FURSTPERSON,FurstPerson,,,{}
PRINTERON,PrinterOn,,,{}
EQUITRAC,Equitrac,,,{}
UIPATH_PROCESS_MINING,Uipath process mining,,,{}
GOLANG_CONTAINER_SOLUTION,Golang Container Solution,,,{}
UNIFIED_MODELING_LANGUAGE_UML,Unified Modeling Language (UML),,,{}
YAML_AINT_MARKUP_LANGUAGE,YAML Aint Markup Language,,,{}
EXTENSIBLE_STYLESHEET_LANGUAGE_TRANSFORMATIONS_XSLT,Extensible Stylesheet Language Transformations (XSLT),,,{}
WORKFRONT,Workfront,,,{}
REVOPS,RevOps,,,{}
ADOBE_XD,Adobe XD,,,{}
GOOGLE_AD_MANAGER,Google Ad Manager,,,{}
ARIBA_BUYER,Ariba Buyer,,,{}
IBM_POWER_SERVERS,IBM Power Servers,,,{}
CISCO_UCS,Cisco UCS,,,{}
LINKEDIN_RECRUITER,LinkedIn Recruiter,,,{}
ORACLE_DATABASE,Oracle Database,,,{}
NPM,NPM,,,{}
CONNECTED_BUSINESS,Connected Business,,,{}
APTITUDE_REVENUE_RECOGNITION_ENGINE,Aptitude Revenue Recognition Engine,,,{}
UIPATH_RPA,UiPath RPA,,,{}
GLUSTERFS,glusterfs,,,{}
OWNBACKUP,OwnBackup,,,{}
SAP_COMMISSIONS,SAP Commissions,,,{}
VPLAYBOOK,vPlaybook,,,{}
OUTREACH,Outreach,,,{}
ZOOMINFO,Zoominfo,,,{}
IBM_PLANNING_ANALYTICS,IBM Planning Analytics,,,{}
VARICENT,Varicent,,,{}
SCIPY,SciPy,,,{}
ZSCALER_CLOUD_SECURITY_PLATFORM,Zscaler Cloud Security Platform,,,{}
ZSCALER_INTERNET_ACCESS,Zscaler Internet Access,,,{}
SECURITY_MENTOR,Security Mentor,,,{}
IBM_QRADAR,IBM Qradar,,,{}
AZURE_SENTINEL,Azure Sentinel,,,{}
CA_TEST_DATA_MANAGER,CA Test Data Manager,,,{}
BOTIFY,Botify,,,{}
BMC_HELIX_ITSM,BMC Helix ITSM,,,{}
WIREMOCK,WireMock,,,{}
QUANTUM_METRIC,Quantum Metric,,,{}
ETAP,ETAP,,,{}
KEYCLOAK,Keycloak,,,{}
EGAIN,eGain,,,{}
NANIGANS,Nanigans,,,{}
NETBASE,NetBase,,,{}
TWEETDECK,TweetDeck,,,{}
REDDIT,reddit,,,{}
ILLUMIO,Illumio,,,{}
TRICENTIS_TOSCA,Tricentis Tosca,,,{}
OPENSSL,OpenSSL,,,{}
CHECKMARX,Checkmarx,,,{}
SAS_STAT,SAS/STAT,,,{}
SCOPEWORKER,Scopeworker,,,{}
AMAZON_KINESIS,Amazon Kinesis,,,{}
AZURE_STREAM_ANALYTICS,Azure Stream Analytics,,,{}
ARIBA_NETWORK,Ariba Network,,,{}
SAP_SCM,SAP SCM,,,{}
SURVEY_SYSTEM,Survey System,,,{}
JUNIPER_QFX5100_EM_BLNK,Juniper QFX5100-EM-BLNK,,,{}
ATERNITY,Aternity,,,{}
APPLE_TABLET,Apple Tablet,,,{}
APPLE_IPAD,Apple iPad,,,{}
TEALIUM,Tealium,,,{}
TASKRAY,TaskRay,,,{}
ACEYUS,Aceyus,,,{}
SECURECRT,SecureCRT,,,{}
HYPERACCESS,HyperACCESS,,,{}
ONETRUST,OneTrust,,,{}
RISKIQ,RiskIQ,,,{}
SPROUTLOUD,SproutLoud,,,{}
INFLUXDB,InfluxDB,,,{}
ANODOT,Anodot,,,{}
WINSHUTTLE,Winshuttle,,,{}
AMAZON_SES,Amazon SES,,,{}
MOBILEIRON,MobileIron,,,{}
ORACLE_IDENTITY_MANAGEMENT,Oracle Identity Management,,,{}
TASKTOP_INTEGRATION_HUB,Tasktop Integration Hub,,,{}
SOURCETREE,sourcetree,,,{}
PANOPTO,Panopto,,,{}
AKAMAI_NETSTORAGE,Akamai NetStorage,,,{}
MEDIAPLATFORM,MediaPlatform,,,{}
CISCO_WEBEX,Cisco WebEx,,,{}
WEBEX_MEETINGS,WebEx Meetings,,,{}
ADOBE_AFTER_EFFECTS,Adobe After Effects,,,{}
HIREVUE,HireVue,,,{}
ADOBE_CONNECT,Adobe Connect,,,{}
AZURE_VIRTUAL_NETWORK,Azure Virtual Network,,,{}
AZURE_VPN_GATEWAY,Azure VPN Gateway,,,{}
SSL_VPN,SSL VPN,,,{}
LEAP_MOTION,Leap Motion,,,{}
AMAZON_TRANSCRIBE,Amazon Transcribe,,,{}
CISCO_UNIFIED_COMMUNICATIONS_MANAGER,Cisco Unified Communications Manager,,,{}
NESSUS,Nessus,,,{}
STEELHEAD_CX_GX,Steelhead CX/GX,,,{}
SAP_EWM,SAP EWM,,,{}
IMPERVA_CLOUD_APPLICATION_SECURITY,Imperva Cloud Application Security,,,{}
ADOBE_DREAMWEAVER,Adobe Dreamweaver,,,{}
ON24,ON24,,,{}
CLOUDBEES,CloudBees,,,{}
UIKIT,UIkit,,,{}
FOGLIGHT,Foglight,,,{}
PLATFORMA,Platforma,,,{}
GUIDECX,GuideCX,,,{}
BMC_CONTROL_M,BMC Control-M,,,{}
ZSCALER_PRIVATE_ACCESS,Zscaler Private Access,,,{}
SEIKI,Seiki,,,{}
AWS_CLOUDTRAIL,AWS CloudTrail,,,{}
PROGRAMA,Programa,,,{}
VUFORIA_CHALK,Vuforia Chalk,,,{}
TRANSCEPTA,Transcepta,,,{}
BUX,BUX,,,{}
INTERNET_EXPLORER,Internet Explorer,,,{}
RATIONAL_CLEARQUEST,Rational ClearQuest,,,{}
MICROSOFT_AZURE,Microsoft Azure,,,{}
DEVEXPRESS,DevExpress,,,{}
ANSYS,Ansys,,,{}
NEXUS_REPOSITORY_MANAGER,Nexus Repository Manager,,,{}
CONTRATO,Contrato,,,{}
SAP_CRM,SAP CRM,,,{}
ORACLE_ENTERPRISE_MANAGER,Oracle Enterprise Manager,,,{}
DATA_GUARD,Data Guard,,,{}
SAP_ABAP_HANA,SAP ABAP HANA,,,{}
OPENLAB,OpenLab,,,{}
WEB_INTELLIGENCE_WEBI,Web Intelligence (WebI),,,{}
BLUE_MOUNTAIN_RAM,Blue Mountain RAM,,,{}
VREALIZE_OPERATIONS,vRealize Operations,,,{}
SAP_ERP,SAP ERP,,,{}
ADOBE_SIGN,Adobe Sign,,,{}
BLACKLINE,BlackLine,,,{}
CONEXES,Conexes,,,{}
MADCAP_FLARE,MadCap Flare,,,{}
JITTERBIT,Jitterbit,,,{}
PAGEMAKER,Pagemaker,,,{}
CASEMANAGER,CaseManager,,,{}
AGILENT_SLIMS,Agilent SLIMS,,,{}
MENDIX,Mendix,,,{}
SAP_MANUFACTURING_EXECUTION,SAP Manufacturing Execution,,,{}
FLOWJO,FlowJo,,,{}
ADP_GLOBALVIEW,ADP GlobalView,,,{}
ECORNELL,eCornell,,,{}
DUN_AND_BRADSTREET_REPORTS,Dun & Bradstreet Reports,,,{}
RSLOGIX,RSLogix,,,{}
SITE_AUDIT_PRO,Site Audit Pro,,,{}
CAPISTRANO,Capistrano,,,{}
IMAGEJ,imagej,,,{}
NUGET,nuget,,,{}
SYMFONY,Symfony,,,{}
ORACLE_AGILE,Oracle Agile,,,{}
SOUNDCLOUD,SoundCloud,,,{}
SIEMENS_NX,Siemens NX,,,{}
INSTALLSHIELD,InstallShield,,,{}
PORTFOLIO_PROJECT_MANAGER_BY_VIRTO,Portfolio Project Manager by Virto,,,{}
SALESLOFT,SalesLoft,,,{}
LINKEDIN_SALES_NAVIGATOR,LinkedIn Sales Navigator,,,{}
SOVOS,Sovos,,,{}
BRIGHTEDGE,BrightEdge,,,{}
JIRA_SERVICE_DESK,JIRA Service Desk,,,{}
TUMBLR,Tumblr,,,{}
SPROUT_SOCIAL,Sprout Social,,,{}
MINITAB,MINITAB,,,{}
IFIX,iFix,,,{}
CISCO_CATALYST_MICRO_SWITCHES,Cisco Catalyst Micro Switches,,,{}
LABVIEW,LabVIEW,,,{}
TESTCOMPLETE,TestComplete,,,{}
SAM_GOV,SAM.gov,,,{}
6CONNEX,6connex,,,{}
AMAZON_INSPECTOR,Amazon Inspector,,,{}
WEB_TIER,Web Tier,,,{}
HOME_CARE_STUDIO,Home Care Studio,,,{}
FLUIDA,Fluida,,,{}
ORACLE_API_PLATFORM_CLOUD,Oracle API Platform Cloud,,,{}
PRICESPIDER,PriceSpider,,,{}
SLACK,Slack,,,{}
ORACLE_BPM,Oracle BPM,,,{}
NETSKOPE,Netskope,,,{}
AWS_SECURITY_HUB,AWS Security Hub,,,{}
ISSO,Isso,,,{}
AMAZON_CONNECT,Amazon Connect,,,{}
VEV,Vev,,,{}
ORACLE_HYPERION,Oracle Hyperion,,,{}
IBM_CLOUDANT,IBM Cloudant,,,{}
NLYTE,Nlyte,,,{}
LUCIDCHART,Lucidchart,,,{}
LAZADA,Lazada,,,{}
SALESFORCE_EMAIL_STUDIO,Salesforce Email Studio,,,{}
SERVICENOW_PROJECT_PORTFOLIO_MANAGEMENT,ServiceNow Project Portfolio Management,,,{}
ORACLE_SERVICE_BUS,Oracle Service Bus,,,{}
ORACLE_ENTERPRISE_RESOURCE_PLANNING_ERP_CLOUD,Oracle Enterprise Resource Planning (ERP) Cloud,,,{}
DATALOADER_IO,dataloader.io,,,{}
ORACLE_MANAGED_FILE_TRANSFER_CLOUD_SERVICE_ORACLE_MFT_CS,Oracle Managed File Transfer Cloud Service (Oracle MFT CS),,,{}
ZENGRC,ZenGRC,,,{}
OPENTEXT,Opentext,,,{}
BOOMI,Boomi,,,{}
INTELLIJ_IDEA,IntelliJ IDEA,,,{}
JQUERY,jQuery,,,{}
AMAZON_ELASTICACHE,Amazon ElastiCache,,,{}
ALATION,Alation,,,{}
DEEPL,DeepL,,,{}
XMIND,XMind,,,{}
FEEDONOMICS,Feedonomics,,,{}
IBM_I,IBM i,,,{}
CADKEY,Cadkey,,,{}
AMAZON_ADVERTISING,Amazon Advertising,,,{}
READSOFT,Readsoft,,,{}
SERVSAFE,ServSafe,,,{}
SPECFLOW,SpecFlow,,,{}
STRAAL,Straal,,,{}
MONETATE,Monetate,,,{}
PTC_FLEX_PLM,PTC Flex PLM,,,{}
RIVERSAND_PLATFORM,Riversand Platform,,,{}
AMPSCRIPT,AMPScript,,,{}
KONCEPT,Koncept,,,{}
ANYDESK,Anydesk,,,{}
TOAST,Toast,,,{}
HIGH_IMPACT_ANALYTICS,High Impact Analytics,,,{}
SPS_COMMERCE_ASSORTMENT,SPS Commerce Assortment,,,{}
IBM_ROBOTIC_PROCESS_AUTOMATION,IBM Robotic Process Automation,,,{}
AVALARA,Avalara,,,{}
THOMSON_REUTERS_ONESOURCE,Thomson Reuters ONESOURCE,,,{}
PROOFPOINT,Proofpoint,,,{}
CORREOS,Correos,,,{}
FUSION_360,Fusion 360,,,{}
ORTEC,ORTEC,,,{}
CERTA_PLATFORM,Certa Platform,,,{}
WORKSPACE_ONE,Workspace ONE,,,{}
DAVINCI_RESOLVE,DaVinci Resolve,,,{}
THE_RECEPTIONIST,The Receptionist,,,{}
MODSECURITY,ModSecurity,,,{}
ADOBE_FONTS,Adobe Fonts,,,{}
GOOGLE_ALERTS,Google Alerts,,,{}
PINTEREST,Pinterest,,,{}
CJ_AFFILIATE,CJ Affiliate,,,{}
RAKUTEN_ADVERTISING,Rakuten Advertising,,,{}
ALTERYX,Alteryx,,,{}
LEVAR,Levar,,,{}
LYRA_HEALTH,Lyra Health,,,{}
NOIBU,Noibu,,,{}
MICROSOFT_AZURE_SECURITY,Microsoft Azure Security,,,{}
VREALIZE_SUITE,vRealize Suite,,,{}
SAP_BUSINESS_TECHNOLOGY_PLATFORM_SAP_BTP,SAP Business Technology Platform (SAP BTP),,,{}
EMAINT_CMMS,eMaint CMMS,,,{}
PROGRESS_KENDO_UI,Progress Kendo UI,,,{}
CONTRACTORMANAGE,ContractorManage,,,{}
SAP_CLOUD_FOR_CUSTOMER,SAP Cloud for Customer,,,{}
SAP_HYBRIS_SERVICE,SAP Hybris Service,,,{}
SAP_INFORMATION_STEWARD,SAP Information Steward,,,{}
RSYNC,Rsync,,,{}
SAP_BUSINESS_WAREHOUSE,SAP Business Warehouse,,,{}
MICROSOFT_PUBLISHER,Microsoft Publisher,,,{}
SAP_FIORI,SAP Fiori,,,{}
ELEVAR,Elevar,,,{}
VENDRE,Vendre,,,{}
SAP_DRC,SAP DRC,,,{}
TERCERO,Tercero,,,{}
AFS_ERP,AFS ERP,,,{}
TROVATA,Trovata,,,{}
UNREAL_ENGINE,Unreal Engine,,,{}
MICROSOFT_DESIGNER,Microsoft Designer,,,{}
CONTENTSTACK,Contentstack,,,{}
JASK,JASK,,,{}
KLARNA,Klarna,,,{}
MULESOFTS_ANYPOINT_PLATFORM,MuleSofts Anypoint Platform,,,{}
KUBEFLOW,Kubeflow,,,{}
ORACLE_RESPONSYS,Oracle Responsys,,,{}
AXONIFY,Axonify,,,{}
SOLARWINDS_NETWORK_PERFORMANCE_MONITOR,SolarWinds Network Performance Monitor,,,{}
GENERAL_ASSEMBLY,General Assembly,,,{}
CONFIANT,Confiant,,,{}
OPENUI5,OpenUI5,,,{}
PARTNERIZE,Partnerize,,,{}
COMPREHENSIVE_PAYROLL_COMPANY,Comprehensive Payroll Company,,,{}
ADP_PAYFORCE,ADP Payforce,,,{}
WORKDAY_TALENT_MANAGEMENT,Workday Talent Management,,,{}
CROSS_SELL,Cross Sell,,,{}
TRADESHIFT,Tradeshift,,,{}
1WORLDSYNC,1WorldSync,,,{}
ACCOMPA,Accompa,,,{}
STACKLINE,Stackline,,,{}
ASSORTIFY,Assortify,,,{}
YOOBIC,YOOBIC,,,{}
RETAIL_PRO,Retail Pro,,,{}
BBPOS,BBPOS,,,{}
HPA,HPA,,,{}
SWOT,SWOT,,,{}
CURALATE,Curalate,,,{}
VENDORA,Vendora,,,{}
INVENTRY,InVentry,,,{}
WP_ENGINE,WP Engine,,,{}
CINEMA_4D,Cinema 4D,,,{}
GRAVITY_SKETCH,gravity sketch,,,{}
SUBSTANCE_PAINTER,Substance Painter,,,{}
VIRTUAL_TRADER,Virtual Trader,,,{}
BRANDWATCH,Brandwatch,,,{}
ORACLE_HYPERION_PLANNING,Oracle Hyperion Planning,,,{}
ORACLE_DEMANTRA,Oracle Demantra,,,{}
DELL_DESKTOP,Dell Desktop,,,{}
ORACLE_BI_PUBLISHER,Oracle BI Publisher,,,{}
BYNDER,Bynder,,,{}
ORACLE_E_BUSINESS_SUITE,Oracle E-Business Suite,,,{}
SKETCHBOOK_PRO,SketchBook Pro,,,{}
NUORDER,NuORDER,,,{}
FLUENCE_TECHNOLOGIES,Fluence Technologies,,,{}
ROMANS_CAD,Romans CAD,,,{}
FRESHDESK,Freshdesk,,,{}
HUBTYPE,Hubtype,,,{}
TOPTAL,Toptal,,,{}
ORACLE_APPLICATION_DEVELOPMENT_FRAMEWORK,Oracle Application Development Framework,,,{}
LOFTWARE_ENTERPRISE_LABELING_SOLUTIONS,Loftware Enterprise Labeling Solutions,,,{}
LABWARE_LIMS,LabWare LIMS,,,{}
MYAGI,Myagi,,,{}
AMBER_ROAD,Amber Road,,,{}
DYNAMIC_YIELD,Dynamic Yield,,,{}
TRUSTPILOT,Trustpilot,,,{}
IPROCUREMENT,iProcurement,,,{}
VISUAL_RETAIL_PLUS,Visual Retail Plus,,,{}
CALL_LOOP,Call Loop,,,{}
WEBDRIVERIO,WebdriverIO,,,{}
TREASURA,Treasura,,,{}
PIXLEE,Pixlee,,,{}
ORACLE_WAREHOUSE_MANAGEMENT,Oracle Warehouse Management,,,{}
EXOTEC,Exotec,,,{}
COREMEDIA,CoreMedia,,,{}
CONSUMER_DATA_PLATFORM_CDP,Consumer Data Platform (CDP),,,{}
PEOPLESOFT_FSCM,PeopleSoft FSCM,,,{}
IBM_FILENET,IBM FileNet,,,{}
KOFAX_TRANSFORMATION_MODULES,Kofax Transformation Modules,,,{}
ORACLE_CLOUD_FINANCIALS,Oracle Cloud Financials,,,{}
PEOPLESOFT,PeopleSoft,,,{}
APTOS,APTOS,,,{}
XSTORE,XStore,,,{}
O9_SOLUTIONS,O9 solutions,,,{}
SKECHWAY,SkechWay,,,{}
SKECHAI_PLATFORM,SkechAI Platform,,,{}
AWS_BEDROCK,AWS Bedrock,,,{}
CLAUDE3,Claude3,,,{}
STABILITY_AI_LLM_MODELS,Stability.ai LLM models,,,{}
SALESFORCE_SERVICE_CLOUD_SFSC,Salesforce Service Cloud (SFSC),,,{}
B2B_CLOUD_CRAZE,B2B Cloud Craze,,,{}
INFORMIX,Informix,,,{}
AWS_MYSQL,AWS MySQL,,,{}
MARIADB,MariaDB,,,{}
MONGODB_ATLAS,MongoDB Atlas,,,{}
AWS_LAMBDA_PYTHON,AWS Lambda (Python),,,{}
SERVICE_NOW,Service Now,,,{}
WEBDRIVER,WebDriver,,,{}
JAVA_ECLIPSE,Java Eclipse,,,{}
QNXT,QNXT,,,{}
RFID,RFID,,,{}
OFFICE_365,Office 365,,,{}
SCCM,SCCM,,,{}
REMOTE_DESKTOP,Remote Desktop,,,{}
BOMGAR,Bomgar,,,{}
CI_CD_PIPELINE,CI/CD pipeline,,,{}
APPLE_STORE,Apple Store,,,{}
GOOGLE_STORE,Google Store,,,{}
GCP,GCP,,,{}
GOOGLE_VERTEX_GEN_AI,Google Vertex Gen AI,,,{}
GOOGLE_GEN_AI_CHAT,Google Gen AI Chat,,,{}
TOGAF,TOGAF,,,{}
ZTNA,ZTNA,,,{}
PASTA,PASTA,,,{}
AWS_S3,AWS (S3),,,{}
REDSHIFT,Redshift,,,{}
LAMBDAS,Lambdas,,,{}
STEP_FUNCTIONS,Step Functions,,,{}
JAVA_SPRING_BOOT,Java Spring Boot,,,{}
APOLLO_FEDERATED_GRAPH,Apollo Federated Graph,,,{}
NODE,Node,,,{}
CLICKHOUSE,ClickHouse,,,{}
BRANCH,Branch,,,{}
POWERBI,PowerBI,,,{}
AMPLITUDE_ANALYTICS,Amplitude Analytics,,,{}
CLO3D,CLO3D,,,{}
GATSBY,Gatsby,,,{}
S_4_HANA,S/4 HANA,,,{}
SAP_SUITE_PRODUCTS,SAP suite products,,,{}
CRM,CRM,,,{}
SAFE,SAFe,,,{}
EDI,EDI,,,{}
AZURE_CLOUD,Azure Cloud,,,{}
IBM_STERLING_COMMERCE,IBM Sterling Commerce,,,{}
FLUENT_COMMERCE,Fluent Commerce,,,{}
MANHATTAN,Manhattan,,,{}
SAP_ECC_6_0,SAP ECC 6.0,,,{}
SAP_S_4_HANA,SAP S/4 HANA,,,{}
SAP_CAR,SAP CAR,,,{}
AWS_EMR,AWS EMR,,,{}
AWS_REDSHIFT,AWS Redshift,,,{}
AWS_ATHENA,AWS Athena,,,{}
PANDAS,Pandas,,,{}
SAP_S4,SAP S4,,,{}
SAP_BI,SAP BI,,,{}
SAP_AFS,SAP AFS,,,{}
FEATURE_STORE,Feature Store,,,{}
GOOGLE_DATA_STUDIO,Google Data Studio,,,{}
MATPLOTLIB,Matplotlib,,,{}
GO1,Go1,,,{}
PEOPLE_HR,People HR,,,{}
HUBSPOT_MARKETING_HUB,HubSpot Marketing Hub,,,{}
AZURE_ARTIFACTS,Azure Artifacts,,,{}
MICROSOFT_FABRIC,Microsoft Fabric,,,{}
MICROSOFT_SYNAPSE,Microsoft Synapse,,,{}
JEST,Jest,,,{}
REACT_TESTIN_LIBRARIES,React Testing libraries,,,{}
WEBDRIVER_IO,Webdriver.io,,,{}
NET,.NET,,,{}
REACT_TESTING_LIBRARY,React Testing Library,,,{}
AI,AI,,,{}
AZURE_APP_SERVICES,Azure App Services,,,{}
POWERSHELL,PowerShell,,,{}
POSTMAN,Postman,,,{}
AZURE_DDOS,Azure DDoS,,,{}
AZURE_AAD,Azure AAD,,,{}
MICROSOFT_AZURE_DEFENDER,Microsoft Azure Defender,,,{}
SELENIUM_JAVA,Selenium-Java,,,{}
MAVEN,Maven,,,{}
JASMINE_FRAMEWORK,Jasmine Framework,,,{}
FASTLANE,Fastlane,,,{}
MSSQL,MSSQL,,,{}
TDD,TDD,,,{}
AZURE_DATA_CENTERS,Azure data centers,,,{}
AZURE_MACC,Azure MACC,,,{}
REST_APIS,REST APIs,,,{}
FERMAT_CAD,Fermat CAD,,,{}
APACHE,Apache,,,{}
SPOTFIRE,Spotfire,,,{}
ATACCAMA,Ataccama,,,{}
UMBRACO,Umbraco,,,{}
GENAI,GenAI,,,{}
LAMBDA,Lambda,,,{}
ATHENA,Athena,,,{}
ML,ML,,,{}
SQS,SQS,,,{}
LAMBDA_FUNCTIONS,Lambda Functions,,,{}
CLOUDFORMATION,CloudFormation,,,{}
BIZTALK,BizTalk,,,{}
NEXT_JS,Next.js,,,{}
ABLETON_LIVE,Ableton Live,,,{}
FL_STUDIO,FL Studio,,,{}
GARAGEBAND,GarageBand,,,{}
LOGIC_PRO_X,Logic Pro X,,,{}
AVID_PRO_TOOLS,Avid Pro Tools,,,{}
MUSIC_MAKER,Music Maker,,,{}
DOCEBO,Docebo,,,{}
Q4,Q4,,,{}
BORDERFREE,Borderfree,,,{}
APACHE_FLUME,Apache Flume,,,{}
EXTENSIS_PORTFOLIO,Extensis Portfolio,,,{}
ALIDA,Alida,,,{}
SHAREFILE,ShareFile,,,{}
PING_IDENTITY,Ping Identity,,,{}
STUDIO_DESIGN,STUDIO design,,,{}
ADOBE_EXPERIENCE_CLOUD,Adobe Experience Cloud,,,{}
STAGENOW,StageNow,,,{}
CORE_APPS,Core-apps,,,{}
SEOPTIMER,SEOptimer,,,{}
NTUITIVE,nTuitive,,,{}
EVALS,EVALS,,,{}
LINKEDIN_ADS,Linkedin Ads,,,{}
CRITEO,Criteo,,,{}
INFOSHIP,InfoSHIP,,,{}
PASSWORD_SAFE,Password Safe,,,{}
EMERCHANT,eMerchant,,,{}
PROTOOLS,ProTools,,,{}
TRAKSTAR,Trakstar,,,{}
Q360,Q360,,,{}
BASWARE,Basware,,,{}
COMMVAULT_CLOUD,Commvault Cloud,,,{}
CONFER_WITH,Confer With,,,{}
AHREFS,Ahrefs,,,{}
DEEPCRAWL,DeepCrawl,,,{}
SERVICE_PRO,Service Pro,,,{}
RF_SMART,RF-SMART,,,{}
AUDIO_NETWORK,Audio Network,,,{}
VOCOLLECT,Vocollect,,,{}
AUDIOEYE,AudioEye,,,{}
SLIMSTOCK,Slimstock,,,{}
CHECKOV,Checkov,,,{}
OWASP_ZAP,OWASP ZAP,,,{}
POPEYE,Popeye,,,{}
HEROKU,Heroku,,,{}
ARCHICAD,Archicad,,,{}
DIALOGFLOW_BY_GOOGLE_CLOUD_PLATFORM,Dialogflow by Google Cloud Platform,,,{}
POWER_APPS,Power Apps,,,{}
POWER_AUTOMATE,Power Automate,,,{}
POWER_VA,Power VA,,,{}
SAP_TM,SAP TM,,,{}
KIBANA,Kibana,,,{}
SHAREPOINT,SharePoint,,,{}
FIREBASE_BY_GOOGLE,Firebase by Google,,,{}
PROMETHEUS,Prometheus,,,{}
GIT_SONAR,Git Sonar,,,{}
TERRAGRUNT,Terragrunt,,,{}
S_4HANA,S/4Hana,,,{}
PACKER,Packer,,,{}
ARGOCD,ArgoCD,,,{}
SPRINT_BOOT,Sprint Boot,,,{}
KAKFA,Kakfa,,,{}
CI_CD_JENKINS,CI/CD Jenkins,,,{}
GOOGLE_TAG_MANAGER_SERVER-SIDE,Google Tag Manager Server-Side,,,{}
REMEDY,Remedy,,,{}
NUTANIX,Nutanix,,,{}
EDITED_MARKET,EDITED Market,,,{}
EDITED_MESSAGING,EDITED Messaging™,,,{}
WEBGL,WebGL,,,{}
AMAZON_QUICKSIGHT,Amazon QuickSight,,,{}
INCORTA,Incorta,,,{}
E_STAFF,E-Staff,,,{}
STATSPACK,Statspack,,,{}
BENEFITFOCUS,Benefitfocus,,,{}
PENTAHO,Pentaho,,,{}
IBM_SERVER,IBM Server,,,{}
CAMUNDA_PLATFORM,Camunda Platform,,,{}
MICROSOFT_EXCHANGE_ONLINE,Microsoft Exchange Online,,,{}
AZURE_FUNCTIONS,Azure Functions,,,{}
SERVICENOW_NOW_PLATFORM,ServiceNow Now Platform,,,{}
XMPIE,XMPie,,,{}
ARTICULATE_STORYLINE,Articulate Storyline,,,{}
AMAZON_COGNITO,Amazon Cognito,,,{}
DATAGRIP,DataGrip,,,{}
DATA_PUMP,Data Pump,,,{}
KOGNI,Kogni,,,{}
GOOGLE_AUDIENCE_CENTER,Google Audience Center,,,{}
CCPA_COMPLIANCE,CCPA Compliance,,,{}
SAP_INTEGRATED_BUSINESS_PLANNING,SAP Integrated Business Planning,,,{}
BLOOMREACH_EXPERIENCE,Bloomreach Experience,,,{}
CHANNELADVISOR,ChannelAdvisor,,,{}
COMMERCETOOLS,commercetools,,,{}
EMAIL_ON_ACID,Email on Acid,,,{}
C_CURE_9000,C-CURE 9000,,,{}
SIMPLELEGAL,SimpleLegal,,,{}
SIGNALFX_INFRASTRUCTURE_MONITORING,SignalFx Infrastructure Monitoring,,,{}
INTELEX,Intelex,,,{}
AWS_GLUE,AWS Glue,,,{}
AWS_DATABASE_MIGRATION_SERVICE,AWS Database Migration Service,,,{}
INMOMENT,InMoment,,,{}
SAMPRO_ENTERPRISE,SAMPro Enterprise,,,{}
SAP_S_4HANA_CLOUD_FOR_ADVANCED_FINANCIAL_CLOSING,SAP S/4HANA Cloud for advanced financial closing,,,{}
CISCO_FIREWALL,Cisco Firewall,,,{}
VISIER,Visier,,,{}
CRUNCHR,crunchr,,,{}
SAP_SUCCESSFACTORS,SAP SuccessFactors,,,{}
INDEFEND,inDefend,,,{}
SNAPLOGIC,SnapLogic,,,{}
AXONIUS,Axonius,,,{}
EXT_JS,Ext JS,,,{}
GRAILS,Grails,,,{}
EMARSYS,Emarsys,,,{}
MARCOMCENTRAL,MarcomCentral,,,{}
STATSD,StatsD,,,{}
GOOGLE_WORKSPACE,Google Workspace,,,{}
CALENDLY,Calendly,,,{}
LINKEDIN_LEARNING,LinkedIn Learning,,,{}
CYGWIN,Cygwin,,,{}
ALMALINUX,AlmaLinux,,,{}
ADOBE_FLASH,Adobe flash,,,{}
STICKY_NOTES,Sticky Notes,,,{}
BCC_MAIL_MANAGER,BCC Mail Manager,,,{}
VISA_CHECKOUT,Visa Checkout,,,{}
LIVECLICKER,Liveclicker,,,{}
ADOBE_PHOTOSHOP_LIGHTROOM_CLASSIC,Adobe Photoshop Lightroom Classic,,,{}
CAFEPRESS,CafePress,,,{}
MIXPANEL,Mixpanel,,,{}
OBJECTIVE_C,Objective-C,,,{}
WEB_SERVICES_DESCRIPTION_LANGUAGE_WSDL,Web Services Description Language (WSDL),,,{}
SPARK_SQL,Spark SQL,,,{}
MEMSQL,MemSQL,,,{}
AMAZON_AURORA,Amazon Aurora,,,{}
AZURE_SQL_DATABASE,Azure SQL Database,,,{}
HYPERSQL,HyperSQL,,,{}
RED_HAT_VIRTUALIZATION,Red Hat Virtualization,,,{}
OVIRT,oVirt,,,{}
VMOTION,vMotion,,,{}
QUADIENT,Quadient,,,{}
BRT,BRT,,,{}
FEDEX,FedEx,,,{}
TESTRAIL,TestRail,,,{}
SHUTTERSTOCK,Shutterstock,,,{}
ADOBE_STOCK,Adobe Stock,,,{}
SUBVERSION,Subversion,,,{}
ANIMOTO,Animoto,,,{}
KAPWING,Kapwing,,,{}
CHERWELL,Cherwell,,,{}
HDFS,HDFS,,,{}
PARQUET,Parquet,,,{}
AVRO,Avro,,,{}
COSMOS_DB,Cosmos DB,,,{}
GIS,GIS,,,{}
ARCGIS_ENTERPRISE,ArcGIS Enterprise,,,{}
ARCGIS_ONLINE,ArcGIS Online,,,{}
REACT_JS,React.js,,,{}
MATERIAL,Material,,,{}
AZURE_POSTGRESQL,Azure PostgreSQL,,,{}
MSSQL_ON_PREMISES,MSSQL on-premises,,,{}
AWS_CLOUDFRONT,AWS CloudFront,,,{}
HD_INSIGHT,HD Insight,,,{}
GITHUB_ACTIONS,GitHub Actions,,,{}
BAZEL,BAZEL,,,{}
CCAAS_GENESYS_CLOUD,CCaaS / Genesys Cloud,,,{}
AVAYA,Avaya,,,{}
GENESYS_ENGAGE,Genesys Engage,,,{}
NICE,NICE,,,{}
CAESAR_II_PIPE_STRESS,Caesar II Pipe Stress,,,{}
NOZZLEPRO,NozzlePro,,,{}
ACTIVEDIRECTORY_INTEGRATIONS_FOR_SSO,ActiveDirectory integrations for SSO,,,{}
IFS_PSO,IFS PSO,,,{}
DRUPAL_CMS,Drupal CMS,,,{}
ACQUIA_CLOUD_PLATFORM,Acquia Cloud Platform,,,{}
OPENTEXT_CONTENT_SERVER,OpenText Content Server,,,{}
OPENTEXT_APPWORKS,OpenText AppWorks,,,{}
OPENTEXT_BRAVA_VIEWER,OpenText Brava Viewer,,,{}
SAP_BUSINESS_OBJECTS,SAP Business Objects,,,{}
POWER_QUERY,Power Query,,,{}
POWER_PIVOT,Power Pivot,,,{}
GLUE,Glue,,,{}
BUSINESS_OBJECTS_DATA_SERVICES_BODS,Business Objects Data Services (BODS),,,{}
NX_WORKSPACE,Nx WorkSpace,,,{}
WEBPACK,Webpack,,,{}
AZURE_OPENAI_API,Azure OpenAI API,,,{}
AZURE_AI_SEARCH_SERVICES,Azure AI Search Services,,,{}
MACHINE_LEARNING,Machine Learning,,,{}
DATA_ANALYTICS,Data Analytics,,,{}
RISK_MODELING,Risk Modeling,,,{}
GIS_SOLUTIONS,GIS Solutions,,,{}
AZURE_DOCUMENT_INTELLIGENCE,Azure Document Intelligence,,,{}
AZURE_STORAGE_ACCOUNTS,Azure Storage Accounts,,,{}
AZURE_OPENAI,Azure OpenAI,,,{}
LIQUIBASE,Liquibase,,,{}
MWM,MWM,,,{}
RANDOM_FOREST_REGRESSOR,Random Forest Regressor,,,{}
MACHINE_LEARNING_MODELS,Machine Learning models,,,{}
SAFE_SCALED_AGILE_FRAMEWORK,SAFe (Scaled Agile Framework),,,{}
DATA_BRICKS,Data Bricks,,,{}
RLS,RLS,,,{}
RBAC,RBAC,,,{}
AZURE_DATA_LAKE,Azure Data Lake,,,{}
DATA_FACTORY,Data Factory,,,{}
COGNITIVE_SEARCH,Cognitive Search,,,{}
SEARCH_SERVICES,Search Services,,,{}
SQL_2017,SQL 2017,,,{}
VISUAL_STUDIO,Visual Studio,,,{}
ORACLE_UNIFIER,Oracle Unifier,,,{}
ENABLON,Enablon,,,{}
SAP_DATABASE,SAP Database,,,{}
AGILE_SCRUM_METHODOLOGIES,Agile/Scrum Methodologies,,,{}
COPPERLEAF_ASSET,Copperleaf Asset™,,,{}
COPPERLEAF_PORTFOLIO,Copperleaf Portfolio™,,,{}
VWO,VWO,,,{}
GOOGLE_OPTIMIZE,Google Optimize,,,{}
TARGETX,TargetX,,,{}
BING_CHAT,Bing Chat,,,{}
NEPTUNE_ML,Neptune.ml,,,{}
SAS_ENTERPRISE_GUIDE,SAS Enterprise Guide,,,{}
NVIVO,NVivo,,,{}
TABLEAU_DESKTOP,Tableau Desktop,,,{}
SWAGGERHUB,SwaggerHub,,,{}
PERFECTO,perfecto,,,{}
ASO_TOOLS,ASO Tools,,,{}
HEXA_3D_2D_TO_3D_AR_CONTENT_CONVERSION_API,Hexa 3D - 2D to 3D/AR content conversion API,,,{}
PEARSON_VERSANT,Pearson Versant,,,{}
AIMSWEBPLUS,aimswebPlus,,,{}
MARKLOGIC,Marklogic,,,{}
APACHE_BEAM,Apache Beam,,,{}
APACHE_STORM,Apache Storm,,,{}
GOOGLE_CLOUD_DATAFLOW,Google Cloud Dataflow,,,{}
ETHEREUM,Ethereum,,,{}
GOOGLE_CLOUD_DIALOGFLOW,Google Cloud Dialogflow,,,{}
MICROSOFT_EDGE,Microsoft Edge,,,{}
SPOTLIGHT_REPORTING,Spotlight Reporting,,,{}
BUILDKITE,Buildkite,,,{}
INTERCALL,InterCall,,,{}
SIGNAL_VINE,Signal Vine,,,{}
APPTIO_CLOUDABILITY,Apptio Cloudability,,,{}
CLOUDCHECKR,CloudCheckr,,,{}
NETAPP,NetApp,,,{}
AZURE_CLOUD_SERVICES,Azure Cloud Services,,,{}
AZURE_WEB_APPS,Azure Web Apps,,,{}
SALESFORCE_HEROKU,Salesforce Heroku,,,{}
ORACLE_FUSION_MIDDLEWARE,Oracle Fusion Middleware,,,{}
GOOGLE_CLOUD_KNATIVE,Google Cloud Knative,,,{}
FIREEYE_CLOUDVISORY,FireEye Cloudvisory,,,{}
SERVER_PROTECTION,Server Protection,,,{}
MIRO,Miro,,,{}
NUMPY,numpy,,,{}
NICE_INCONTACT,NICE inContact,,,{}
AVAYA_AURA,Avaya Aura,,,{}
AVAYA_CALL_CENTER_ELITE,Avaya Call Center Elite,,,{}
AMAZON_ECS,Amazon ECS,,,{}
AWS_FARGATE,AWS Fargate,,,{}
IBM_URBANCODE_DEPLOY,IBM UrbanCode Deploy,,,{}
CODEMAGIC,Codemagic,,,{}
CMX,CMx,,,{}
SKILLJAR,Skilljar,,,{}
ADOBE_PRESENTER,Adobe Presenter,,,{}
MAUTHOR,mAuthor,,,{}
CLOUDSENSE,CloudSense,,,{}
PIVOTAL_CRM,Pivotal CRM,,,{}
DITO,Dito,,,{}
AMAZON_ATHENA,Amazon Athena,,,{}
AMAZON_DOCUMENTDB,Amazon DocumentDB,,,{}
PHPMYADMIN,phpMyAdmin,,,{}
BIGID,BigID,,,{}
PRIVACY_TOOLS,Privacy Tools,,,{}
MYSQL_WORKBENCH,Mysql workbench,,,{}
GECKOBOARD,Geckoboard,,,{}
ORACLE_BUSINESS_INTELLIGENCE,Oracle Business Intelligence,,,{}
AZURE_SQL_DATA_WAREHOUSE,Azure SQL Data Warehouse,,,{}
SQL_SERVER_2019,SQL Server 2019,,,{}
APPCUES,Appcues,,,{}
COMSCORE,ComScore,,,{}
CREDLY,Credly,,,{}
KENTICO_XPERIENCE,Kentico Xperience,,,{}
NEARPOD,Nearpod,,,{}
DISCOVERY_EDUCATION_INC,Discovery Education Inc,,,{}
EDPUZZLE,Edpuzzle,,,{}
LEARNING_CATALYTICS,Learning Catalytics,,,{}
GOOGLE_DOCS,Google Docs,,,{}
ADOBE_ACROBAT_DC,Adobe Acrobat DC,,,{}
AVANGATE,Avangate,,,{}
KMK,KMK,,,{}
O_REILLY_ONLINE_LEARNING,O'Reilly Online Learning,,,{}
SENDGRID,SendGrid,,,{}
SALESFORCE_HIGH_VELOCITY_SALES,Salesforce High Velocity Sales,,,{}
THALES_CIPHERTRUST_MANAGER,Thales CipherTrust Manager,,,{}
MICROSOFT_BITLOCKER,Microsoft BitLocker,,,{}
DOCUMENTUM,Documentum,,,{}
NERDIO,Nerdio,,,{}
COMSCI,ComSci,,,{}
WEBSPHERE_MESSAGE_BROKER,WebSphere Message Broker,,,{}
KEYEDIN,KeyedIn,,,{}
HIGHERED,Highered,,,{}
PRISMA_CLOUD,Prisma Cloud,,,{}
IBM_STERLING_FILE_GATEWAY,IBM Sterling File Gateway,,,{}
FORTINET_FIREWALLS,Fortinet Firewalls,,,{}
CISCO_PIX,Cisco PIX,,,{}
GOOGLE_VOICE,Google Voice,,,{}
INBOUND_IVR_FOR_G_SUITE,Inbound IVR for G Suite,,,{}
IQVIA,IQVIA,,,{}
ORACLE_SERVICE_CLOUD_FORMERLY_RIGHTNOW,Oracle Service Cloud (formerly RightNow),,,{}
LIVEAGENT,LiveAgent,,,{}
ORACLE_HUMAN_CAPITAL_MANAGEMENT_HCM_CLOUD,Oracle Human Capital Management (HCM) Cloud,,,{}
VXRAIL,VxRail,,,{}
AWS_IDENTITY_AND_ACCESS_MANAGEMENT_IAM,AWS Identity and Access Management (IAM),,,{}
FORGEROCK,ForgeRock,,,{}
CONVERSICA,Conversica,,,{}
WORKATO,Workato,,,{}
JACOCO,JaCoCo,,,{}
EXPRESS_JS,Express.js,,,{}
BACKBONE_JS,Backbone.js,,,{}
EMBER_JS,ember.js,,,{}
REACT_JS1,React JS,,,{}
AXIOS,Axios,,,{}
MICROSOFT_AUTHENTICATION,Microsoft Authentication,,,{}
BEM,BEM,,,{}
PROTOTYPE_FRAMEWORKS,Prototype Frameworks,,,{}
RESTEASY,RestEasy,,,{}
PRIMEFACES,PrimeFaces,,,{}
JAVA_SWING,Java Swing,,,{}
WEBFLUX,Webflux,,,{}
MICRONAUT,Micronaut,,,{}
SALESFORCE_FOR_EDUCATION,Salesforce for Education,,,{}
POWERSCHOOL,PowerSchool,,,{}
UNBOUNCE,Unbounce,,,{}
LEVEL_5,Level 5,,,{}
APPLE_PC,Apple PC,,,{}
WIZA,Wiza,,,{}
MOODLE,Moodle,,,{}
ITSLEARNING,itslearning,,,{}
HIPCHAT,HipChat,,,{}
CHARLES_PROXY,Charles Proxy,,,{}
EDCAST,EdCast,,,{}
OPENEDX,OpenEdX,,,{}
DATORAMA,Datorama,,,{}
ORACLE_ELOQUA,Oracle Eloqua,,,{}
ORACLE_MDM,Oracle MDM,,,{}
PWA,PWA,,,{}
HTTP_2,HTTP/2,,,{}
SENSOR_TOWER,Sensor Tower,,,{}
DATA_AI_INTELLIGENCE_FORMERLY_APP_ANNIE,data.ai Intelligence (formerly App Annie),,,{}
MICRO_FOCUS_LOADRUNNER_ENTERPRISE,Micro Focus LoadRunner Enterprise,,,{}
AWS_AMPLIFY,AWS Amplify,,,{}
AZURE_SDK,Azure SDK,,,{}
GOOGLE_ADMIN,Google Admin,,,{}
HUGGING_FACE,Hugging Face,,,{}
NMAP,Nmap,,,{}
MICROSOFT_POWER_APPS,Microsoft Power Apps,,,{}
ENGAGE_EHS,Engage EHS,,,{}
G_SUITE,G Suite,,,{}
FORMASSEMBLY,FormAssembly,,,{}
SCHOOLOGY,Schoology,,,{}
HARVARDX,HarvardX,,,{}
MYLAB,MyLab,,,{}
AGILIX,Agilix,,,{}
ONVUE,OnVUE,,,{}
ORACLE_LINUX,Oracle Linux,,,{}
FAETHM,Faethm,,,{}
MICROSOFT_GRAPH,Microsoft Graph,,,{}
AWS_APP_MESH,AWS App Mesh,,,{}
AWS_CLOUD_DEVELOPMENT_KIT_AWS_CDK,AWS Cloud Development Kit (AWS CDK),,,{}
SITESPEED,Sitespeed,,,{}
MATHTYPE,MathType,,,{}
MOOTOOLS,MooTools,,,{}
JQUERY_UI,jQuery UI,,,{}
WEBRTC,WebRTC,,,{}
DATATABLES,Datatables,,,{}
APACHE_MYFACES,Apache MyFaces,,,{}
XTRA,Xtra,,,{}
PLAYFRAMEWORK,playframework,,,{}
FOXIT_PDF_EDITOR,Foxit PDF Editor,,,{}
COBALT_STRIKE,Cobalt Strike,,,{}
LAMINAS_PROJECT_FORMERLY_ZEND_FRAMEWORK,Laminas Project (formerly Zend Framework),,,{}
TURNITIN,Turnitin,,,{}
GOOGLE_SLIDE,Google Slide,,,{}
PROACTIVE_OUTREACH_MANAGER,Proactive Outreach Manager,,,{}
SKAN,Skan,,,{}
COFFEESCRIPT,CoffeeScript,,,{}
GO_PROGRAMMING_LANGUAGE,Go (Programming Language),,,{}
XPATH,XPath,,,{}
R_PROGRAMMING_LANGUAGE,R (Programming Language),,,{}
XQUERY,XQuery,,,{}
SECURITY_ASSERTION_MARKUP_LANGUAGE_SAML,Security Assertion Markup Language (SAML),,,{}
MICROSOFT_PLANNER,Microsoft Planner,,,{}
CALIBER,Caliber,,,{}
JUPYTER,Jupyter,,,{}
TEAMVIEWER,TeamViewer,,,{}
ISL_LIGHT,ISL Light,,,{}
GOTOASSIST,GoToAssist,,,{}
DAMEWARE,Dameware,,,{}
OPSCENTER,OpsCenter,,,{}
AVAILITY,Availity,,,{}
SERVICENOW_ORCHESTRATION,ServiceNow Orchestration,,,{}
BRAINSHARK,Brainshark,,,{}
APPLE_SEARCH_ADS,Apple Search Ads,,,{}
IRONPORT,IronPort,,,{}
AWS_CLOUD_SECURITY_SECURITY,AWS Cloud Security: Security,,,{}
IDENTITY_AND_COMPLIANCE,Identity and Compliance,,,{}
GOOGLE_TRENDS,Google Trends,,,{}
AWS_BACKUP,AWS Backup,,,{}
SQL_SERVER_ON_VIRTUAL_MACHINES,SQL Server on Virtual Machines,,,{}
VIVANTIO,Vivantio,,,{}
NEWRELIC_INTEGRATION,NewRelic Integration,,,{}
CTT,CTT,,,{}
STATEFLOW,Stateflow,,,{}
AUTOMATION_STUDIO,Automation Studio,,,{}
TWITTER_ADS,Twitter Ads,,,{}
LICENSE_DASHBOARD,License Dashboard,,,{}
SERVICENOW_SOFTWARE_ASSET_MANAGEMENT,ServiceNow Software Asset Management,,,{}
SNYK,Snyk,,,{}
BLAZEMETER,BlazeMeter,,,{}
OBSERVE_AI,Observe.AI,,,{}
GOOGLE_SHEETS,Google Sheets,,,{}
RESHARPER,ReSharper,,,{}
ESLINT,ESLint,,,{}
IDA_PRO,IDA Pro,,,{}
STATCRUNCH,StatCrunch,,,{}
QUIZLET,Quizlet,,,{}
ZUORA_BILLING,Zuora Billing,,,{}
ZUORA,Zuora,,,{}
ORACLE_SUPPLY_CHAIN_MANAGEMENT,Oracle Supply Chain Management,,,{}
LOGILITY_SOLUTIONS,Logility Solutions,,,{}
GOOGLE_FORMS,Google Forms,,,{}
A_CLOUD_GURU,A Cloud Guru,,,{}
KATALON_STUDIO,Katalon Studio,,,{}
TURNKEY_SOLUTIONS,TurnKey Solutions,,,{}
XCUITEST,XCUITest,,,{}
EGENCIA,Egencia,,,{}
PRODUCTBOARD,productboard,,,{}
USERTESTING,UserTesting,,,{}
LOOKBACK,Lookback,,,{}
CORELDRAW,coreldraw,,,{}
ICONIK,iconik,,,{}
AMAZON_CHIME,Amazon Chime,,,{}
GOOGLE_HANGOUTS,Google Hangouts,,,{}
JOIN_ME,Join.Me,,,{}
VIDEOSCRIBE,VideoScribe,,,{}
IMOVIE,iMovie,,,{}
MICROSOFT_STREAM,Microsoft Stream,,,{}
EASYRECRUE,EasyRecRue,,,{}
AWS_TRAINING_AND_CERTIFICATION,AWS Training and Certification,,,{}
SKETCHFAB,Sketchfab,,,{}
LUMENVOX_AUTOMATED_SPEECH_RECOGNITION_ASR,LumenVox Automated Speech Recognition (ASR),,,{}
KALDI,Kaldi,,,{}
BURP_SUITE,Burp Suite,,,{}
JOOMLA,Joomla,,,{}
CONTENSIS,Contensis,,,{}
TAILWIND_CSS,Tailwind CSS,,,{}
YOLA,Yola,,,{}
WORDPRESS_COM,WordPress.com,,,{}
FORCEPOINT,Forcepoint,,,{}
WORKFORCE_MANAGEMENT,Workforce Management,,,{}
LLMS,LLMs,,,{}
OWASP,OWASP,,,{}
NIST,NIST,,,{}
REACTJS,ReactJS,,,{}
NEWRELIC,NewRelic,,,{}
GO,Go,,,{}
IAAS,IAAS,,,{}
NAAS,NAAS,,,{}
OPENLLM,OpenLLM,,,{}
RAG,RAG,,,{}
GOOGLE_CLOUD_PLATFORM_GCP,Google Cloud Platform (GCP),,,{}
DBT,DBT,,,{}
BIGQUERY,BigQuery,,,{}
NLP,NLP,,,{}
SPRINGBOOT,SpringBoot,,,{}
APPSPIDER,AppSpider,,,{}
QUALYS,Qualys,,,{}
MONGO_ATLAS,Mongo Atlas,,,{}
FIREBASE_ANALYTICS,Firebase Analytics,,,{}
GA,GA,,,{}
GTM,GTM,,,{}
REALM,RealM,,,{}
FIRESTORE,Firestore,,,{}
RETROFIT,Retrofit,,,{}
DAGGER,Dagger,,,{}
GLIDE,Glide,,,{}
OTTO,Otto,,,{}
LOTTIE,Lottie,,,{}
YOUTUBE,YouTube,,,{}
ANDROID_PROFILER,Android Profiler,,,{}
MOCKITTO,Mockitto,,,{}
FIREBASE,Firebase,,,{}
STATERAMP,StateRAMP,,,{}
AGILE,Agile,,,{}
SCRUM,Scrum,,,{}
CI_AND_CD,CI&CD,,,{}
GENERATIVE_AI,Generative AI,,,{}
IAM,IAM,,,{}
MACH,MACH,,,{}
JAVARX,JavaRx,,,{}
OCR,OCR,,,{}
MATHPIX,MathPix,,,{}
GEN_AI,Gen-AI,,,{}
SALESFORCE_CHAT,Salesforce chat,,,{}
SERVICE_CLOUD_VOICE,Service Cloud Voice,,,{}
EXPERIENCE_CLOUD,Experience Cloud,,,{}
EINSTEIN_BOT,Einstein bot,,,{}
API,API,,,{}
ZINGTREE,Zingtree,,,{}
AWS_FSX,AWS FSx,,,{}
NETAPP_ONTAP,Netapp Ontap,,,{}
NOSQL,NoSQL,,,{}
AIX,AIX,,,{}
SALESFORCE_SERVICE_CLOUD_VOICE,Salesforce Service Cloud Voice,,,{}
AMAZON_CONNECT_AWS_CONNECT,Amazon Connect (AWS Connect),,,{}
BEYONDTRUST,BeyondTrust,,,{}
WHITEHAT_SYNOPSYS,WhiteHat (Synopsys),,,{}
AZURE_SENTINEL_SIEM,Azure Sentinel SIEM,,,{}
DEEPWATCH,DeepWatch,,,{}
STEALTHBITS_NETWRIX,Stealthbits (Netwrix),,,{}
AKAMAI,Akamai,,,{}
NET_CORE,.NET Core,,,{}
JSP_AND_SERVLETS,JSP & Servlets,,,{}
REST_SOAP_APIS,REST / SOAP APIs,,,{}
IMPEX,ImpEx,,,{}
HOT_FOLDERS,hot folders,,,{}
CSS_AND_HTML_5,CSS & HTML 5,,,{}
AZURE_APP_SERVICE,Azure App Service,,,{}
SAP_MASTER_DATA_GOVERNANCE_DATA_AND_GOVERNANCE_MODELS,SAP Master Data Governance Data and Governance Models,,,{}
SAP_BODS,SAP BODS,,,{}
REST_SOAP_API,REST/SOAP API,,,{}
SQL_SERVER_MANAGEMENT_STUDIO_SSMS,SQL Server Management Studio (SSMS),,,{}
SQL_SERVER_REPORTING_SERVICES_SSRS,SQL Server Reporting Services (SSRS),,,{}
NLTK,NLTK,,,{}
SCIKIT,Scikit,,,{}
SKLEARN,Sklearn,,,{}
FACEBOOK_PROPHET,Facebook Prophet,,,{}
AZURE_STORAGE,Azure Storage,,,{}
SCI_KIT_LEARN,sci-kit-learn,,,{}
AZURE_COGNITIVE_SEARCH,Azure Cognitive Search,,,{}
AZURE_OPEN_AI,Azure Open AI,,,{}
WPF,WPF,,,{}
SQLITE_SQL_SERVER,SQLite/SQL Server,,,{}
ENTITY_FRAMEWORK,Entity Framework,,,{}
PRISM,Prism,,,{}
XAML,XAML,,,{}
SITECORE10,Sitecore10,,,{}
KANBAN,Kanban,,,{}
POWER_PLATFORM,Power Platform,,,{}
TOAD,Toad,,,{}
SOLUTION_MANAGER_7_2,Solution Manager 7.2,,,{}
MICROSOFT,Microsoft,,,{}
SEGMENT_CUSTOMER_DATA_PLATFORM,Segment – Customer Data Platform,,,{}
SAP_BASIS,SAP Basis,,,{}
MICROSOFT_DYNAMICS_CRM_2011,Microsoft Dynamics CRM 2011,,,{}
MICROSOFT_DYNAMICS_365_CRM,Microsoft Dynamics 365 CRM,,,{}
NETFABB,Netfabb,,,{}
3PL_WAREHOUSE_MANAGER,3PL Warehouse Manager,,,{}
12TWENTY,12Twenty,,,{}
FINANCE_D_TENUE_DE_LIVRES_SIMPLIFIA,Finance D - Tenue de livres simplifiA,,,{}
HIGHRADIUS,HighRadius,,,{}
RAKUTEN,Rakuten,,,{}
IBM_ENGINEERING_WORKFLOW_MANAGEMENT,IBM Engineering Workflow Management,,,{}
IBM_COGNOS_ANALYTICS,IBM Cognos Analytics,,,{}
SAP_ANALYTICS_CLOUD,SAP Analytics Cloud,,,{}
TIBCO_SPOTFIRE,TIBCO Spotfire,,,{}
THOUGHTSPOT,ThoughtSpot,,,{}
SAP_BUSINESSOBJECTS_BUSINESS_INTELLIGENCE_BI,SAP BusinessObjects Business Intelligence (BI),,,{}
AZURE_API_MANAGEMENT,Azure API Management,,,{}
JSON_API_APP,JSON API APP,,,{}
ABSOLUTE_PERFORMANCE,Absolute Performance,,,{}
COMPLIANCE_INTELLIGENCE,Compliance Intelligence,,,{}
NATURAL_REVIEW_MONITOR,Natural Review Monitor,,,{}
MICROSOFT_APPLICATION_INSIGHTS,Microsoft Application Insights,,,{}
WILDFLY,Wildfly,,,{}
GUNICORN,gunicorn,,,{}
SCOPE_AR,Scope AR,,,{}
CLASSMARKER,ClassMarker,,,{}
INFOLEASE,InfoLease,,,{}
JOIN_IT,join-it,,,{}
POLL_EVERYWHERE,Poll Everywhere,,,{}
EU_ETS,EU-ETS,,,{}
EDGE_INFORMATION,Edge Information,,,{}
APACHE_NIFI,Apache Nifi,,,{}
APACHE_SQOOP,Apache Sqoop,,,{}
QUBOLE,Qubole,,,{}
APACHE_IMPALA,Apache Impala,,,{}
AWS_LAKE_FORMATION,AWS Lake Formation,,,{}
DATABRICKS_LAKEHOUSE_PLATFORM,Databricks Lakehouse Platform,,,{}
UCS_B200_M5_BLADE_SERVER,UCS B200 M5 Blade Server,,,{}
HP_PROLIANT_BL460C_GEN_8,HP ProLiant BL460c Gen 8,,,{}
QBOX,QBox,,,{}
YOUTRACK,YouTrack,,,{}
AUTOCAD_PLANT_3D,AutoCAD Plant 3D,,,{}
NAVISWORKS,Navisworks,,,{}
UNILY,Unily,,,{}
CAPA_MANAGER,CAPA Manager,,,{}
NAVVIA,Navvia,,,{}
IGRAFX,iGrafx,,,{}
SIGNAVIO,Signavio,,,{}
CIGNON_BPM,Cignon BPM,,,{}
AUTOCAL,AutoCal,,,{}
ORACLE_PRIMAVERA,Oracle Primavera,,,{}
FUEL50,Fuel50,,,{}
CASHFLOWS,CashFlows,,,{}
ECATALOG,eCatalog,,,{}
CATERTRAX,CaterTrax,,,{}
TEXAS_INSTRUMENTS_INCORPORATED,Texas Instruments Incorporated,,,{}
KORE_AI,kore.ai,,,{}
MEDIDATA_CTMS,Medidata CTMS,,,{}
CLINDEX_CDMS_CTMS_EDC,Clindex CDMS/CTMS/EDC,,,{}
AWS_CLOUD_STORAGE,AWS Cloud Storage,,,{}
AWS_DIRECTORY_SERVICE,AWS Directory Service,,,{}
LOGICMONITOR,LogicMonitor,,,{}
GOOGLE_CLOUD_OPERATIONS_FORMERLY_STACKDRIVER,Google Cloud Operations (formerly Stackdriver),,,{}
AWS_DATASYNC,AWS DataSync,,,{}
SKYSYNC,SkySync,,,{}
SHAREGATE,ShareGate,,,{}
MICROSOFT_ONEDRIVE_FOR_BUSINESS,Microsoft OneDrive for Business,,,{}
PLATFORM_SH,Platform.sh,,,{}
VMWARE_CLOUD_FOUNDATION,VMware Cloud Foundation,,,{}
DETA,Deta,,,{}
SECURONIX_CLOUD_SECURITY_MONITORING,Securonix Cloud Security Monitoring,,,{}
ECOSTRUXURE,EcoStruxure,,,{}
CERDAAC,CERDAAC,,,{}
ODRIVE,Odrive,,,{}
RIGHTFIND,RightFind,,,{}
XMETAL,XMetaL,,,{}
PAYFACTORS,Payfactors,,,{}
EXTANGULAR,ExtAngular,,,{}
AWS_CONFIG,AWS Config,,,{}
THE_POWER_TOOLS,The Power Tools,,,{}
C_SITE,C-SITE,,,{}
GENESYS_PURECONNECT,Genesys PureConnect,,,{}
GENESYS_CLOUD,Genesys Cloud,,,{}
SENSU,Sensu,,,{}
AMAZON_EKS,Amazon EKS,,,{}
LINKSQUARES,LinkSquares,,,{}
EMPORTANT,Emportant,,,{}
META4,Meta4,,,{}
BEAMERY,Beamery,,,{}
SERVICENOW_HR_SERVICE_DELIVERY,ServiceNow HR Service Delivery,,,{}
SINGLEPOINT,SinglePoint,,,{}
SABA_CLOUD,Saba Cloud,,,{}
ABSORB_LMS,Absorb LMS,,,{}
CORNERSTONE_LEARNING,Cornerstone Learning,,,{}
PROJECT_HELPING,Project Helping,,,{}
FRM_SOLUTIONS,FRM Solutions,,,{}
ORACLE_SIEBEL,Oracle Siebel,,,{}
CROSS_BORDER_E_COMMERCE_SOFTWARE,Cross Border E-Commerce Software,,,{}
FIDELITY_DIGITAL_ASSETS,Fidelity Digital Assets,,,{}
CSG_XPONENT,CSG Xponent,,,{}
ZEOTAP,Zeotap,,,{}
TREASURE_DATA_CUSTOMER_DATA_PLATFORM,Treasure Data Customer Data Platform,,,{}
OKTA_CUSTOMER_IDENTITY,Okta Customer Identity,,,{}
DBEAVER,DBeaver,,,{}
SENTRYONE,SentryOne,,,{}
DATASPARK,DataSpark,,,{}
CORSEARCH,Corsearch,,,{}
INFORMATICA_INTELLIGENT_CLOUD_SERVICES_IICS,Informatica Intelligent Cloud Services (IICS),,,{}
OVALEDGE,OvalEdge,,,{}
TAMR,Tamr,,,{}
ALTAIR_MONARCH,Altair Monarch,,,{}
DEMANDTOOLS,DemandTools,,,{}
INFORMATICA_DATA_QUALITY,Informatica Data Quality,,,{}
SAP_LANDSCAPE_TRANSFORMATION,SAP Landscape Transformation,,,{}
H2O,H2O,,,{}
DOMINO,Domino,,,{}
KNIME_ANALYTICS_PLATFORM,KNIME Analytics Platform,,,{}
D3_JS,D3.js,,,{}
ERWIN_DATA_MODELER,erwin Data Modeler,,,{}
BIRT,BIRT,,,{}
NUGIT,Nugit,,,{}
KNOWN_FACTORS,Known Factors,,,{}
SAP_LUMIRA,SAP Lumira,,,{}
EMITE,eMite,,,{}
GGPLOT,Ggplot,,,{}
SEABORN,Seaborn,,,{}
AZURE_SYNAPSE_ANALYTICS,Azure Synapse Analytics,,,{}
SAP_BW_4HANA,SAP BW/4HANA,,,{}
SEQUEL_DATA_WAREHOUSE,Sequel Data Warehouse,,,{}
KINAXIS,Kinaxis,,,{}
MARKETING_360,Marketing 360,,,{}
CITRIX_XENAPP,Citrix XenApp,,,{}
AMAZON_WORKSPACES,Amazon WorkSpaces,,,{}
ADOBE_FRAMEMAKER,Adobe FrameMaker,,,{}
DRAW_IO,draw.io,,,{}
WHATFIX,Whatfix,,,{}
FRONTIFY,Frontify,,,{}
PIMCORE,Pimcore,,,{}
VEEVA_VAULT_PROMOMATS,Veeva Vault PromoMats,,,{}
NEXTHINK,Nexthink,,,{}
FORENSIC_TOOLKIT_FTK,Forensic Toolkit (FTK),,,{}
KORBYT,Korbyt,,,{}
ACRONIS_DISASTER_RECOVERY,Acronis Disaster Recovery,,,{}
STANDARD_ERP,Standard ERP,,,{}
PIKTOCHART,Piktochart,,,{}
DOCUSAURUS,Docusaurus,,,{}
COUCHBASE,CouchBase,,,{}
AZURE_HDINSIGHT,Azure HDInsight,,,{}
EVERSANA,Eversana,,,{}
SIREN_BY_OASYS,Siren by Oasys,,,{}
ORDER_METRICS,Order Metrics,,,{}
ACCESSO,Accesso,,,{}
UNAS,Unas,,,{}
SOFTCLINIC_EHR,SoftClinic EHR,,,{}
VING,Ving,,,{}
SAP_TRAINING,SAP Training,,,{}
ORACLE_CLINICAL,Oracle Clinical,,,{}
MEDIDATA_RAVE,Medidata Rave,,,{}
FLOW_SOFTWARE,Flow Software,,,{}
BIOVIA_WORKBOOK,biovia workbook,,,{}
LABVANTAGE_ELN_AND_LES,LabVantage ELN and LES,,,{}
TIBCO_JASPERSOFT,Tibco Jaspersoft,,,{}
PLUXEE,Pluxee,,,{}
WORKTIME,WorkTime,,,{}
CISCO_AMP_FOR_ENDPOINTS,Cisco AMP for Endpoints,,,{}
METASYS,Metasys,,,{}
VEEVA_VAULT,Veeva Vault,,,{}
AWS_MANAGEMENT_CONSOLE,AWS Management Console,,,{}
APPTIO,Apptio,,,{}
VCLOUD_SUITE,vCloud Suite,,,{}
APACHE_LUCENE,Apache Lucene,,,{}
WEBMETHODS_INTEGRATION_SERVER,webMethods Integration Server,,,{}
SAP_FICO,SAP FICO,,,{}
SAP_MATERIAL_MANAGEMENT,SAP Material Management,,,{}
SAP_BUSINESS_OBJECT_DATA_SERVICES_SAP_BODS,SAP Business Object Data Services (SAP BODS),,,{}
HCENTIVE,hCentive,,,{}
SMG_SERVICE_MANAGEMENT_GROUP,SMG - Service Management Group,,,{}
TREND_MICRO_VISION_ONE,Trend Micro Vision One,,,{}
ANOMALI,Anomali,,,{}
IVISITOR,iVisitor,,,{}
SAP_XI,SAP XI,,,{}
ALPHASENSE,AlphaSense,,,{}
SALESFORCE_FINANCIAL_SERVICES_CLOUD,Salesforce Financial Services Cloud,,,{}
PALO_ALTO_PA_7050,Palo Alto PA-7050,,,{}
CISCO_MERAKI_MX_FIREWALLS,Cisco Meraki MX firewalls,,,{}
TRIMBLE_FLEET_MOBILITY_FORMERLY_PEOPLENET,Trimble Fleet Mobility (Formerly PeopleNet),,,{}
STRUXUREWARE,StruxureWare,,,{}
SAP_APPLICATION_INTERFACE_FRAMEWORK,SAP Application Interface Framework,,,{}
RANDSTAD_SOURCERIGHT_FREELANCER_MANAGEMENT_SYSTEM,Randstad Sourceright Freelancer Management System,,,{}
UNITY,Unity,,,{}
TEXTIO,Textio,,,{}
DATAPEOPLE,Datapeople,,,{}
MANIFOLD_SYSTEM,Manifold System,,,{}
SAP_GLOBAL_TRADE_SERVICES,SAP Global Trade Services,,,{}
CYBERGRANTS,CyberGrants,,,{}
SERVICENOW_GOVERNANCE,ServiceNow Governance,,,{}
RISK_AND_COMPLIANCE,Risk and Compliance,,,{}
DRATA,Drata,,,{}
COMMUNICATION_COMPLIANCE,Communication Compliance,,,{}
VIZIENT,Vizient,,,{}
HCS_CONNECT,HCS Connect,,,{}
EMSI,Emsi,,,{}
AZURE_STACK,Azure Stack,,,{}
PLANVIEW_SPIGIT,Planview Spigit,,,{}
SALESFORCE_IDENTITY,Salesforce Identity,,,{}
NILEARN,Nilearn,,,{}
MOOGSOFT,Moogsoft,,,{}
MITIGA,Mitiga,,,{}
DEVICEWISE,deviceWISE,,,{}
ADDI,Addi,,,{}
POLICY_WORKS,Policy Works,,,{}
MEMOTECH,Memotech,,,{}
NEXT_IT,Next IT,,,{}
NVOICE,NVOICE,,,{}
THINGWORX_INDUSTRIAL_IOT_PLATFORM,ThingWorx Industrial IOT Platform,,,{}
SERVICENOW_IT_ASSET_MANAGEMENT,ServiceNow IT Asset Management,,,{}
SAP_SOLUTION_MANAGER,SAP Solution Manager,,,{}
MICROSOFT_MANAGEMENT_CONSOLE_MMC,Microsoft Management Console (MMC),,,{}
EJS,EJS,,,{}
REACTREDUX,ReactRedux,,,{}
REACT_ROUTER,React Router,,,{}
VISX,Visx,,,{}
HIBERNATE,Hibernate,,,{}
APACHE_FLINK,Apache Flink,,,{}
APACHE_STRUTS,Apache Struts,,,{}
IBATIS,iBatis,,,{}
JAVA_ARCHITECTURE_FOR_XML_BINDING_JAXB,Java Architecture for XML Binding (JAXB),,,{}
RAKEN,Raken,,,{}
PIVOTAL_GEMFIRE,Pivotal Gemfire,,,{}
NICELABEL,NiceLabel,,,{}
MOSAIC,Mosaic,,,{}
BIOLOGICAL_SPECIMEN_INVENTORY_SYSTEM,Biological Specimen Inventory System,,,{}
PREDICTIVE_RESPONSE,Predictive Response,,,{}
KRITIK,Kritik,,,{}
LABVANTAGE,LabVantage,,,{}
BENCHLING,Benchling,,,{}
THERMO_SCIENTIFIC_WATSON_LIMS,Thermo Scientific Watson LIMS,,,{}
BOLD360,Bold360,,,{}
GENESYS_DX,Genesys DX,,,{}
ONCUSTOMER,OnCustomer,,,{}
ADA,Ada,,,{}
AWS_ELASTIC_LOAD_BALANCING,AWS Elastic Load Balancing,,,{}
ELASTIC_CLOUD,Elastic Cloud,,,{}
APPIAN,Appian,,,{}
COMO,Como,,,{}
AMAZON_FORECAST,Amazon Forecast,,,{}
MLLIB,MLlib,,,{}
CAMSTAR,Camstar,,,{}
TRAKSYS,Traksys,,,{}
PAS_X,PAS-X,,,{}
SYNCADE_MES,Syncade MES,,,{}
MARKETING_TOOLS_GROWTH_MARKETING_PLATFORM,Marketing Tools Growth Marketing Platform,,,{}
MINDBOX,Mindbox,,,{}
NIELSEN_GLOBAL_CONNECT,Nielsen Global Connect,,,{}
RELTIO_CLOUD,Reltio Cloud,,,{}
FACTIVA,Factiva,,,{}
DOXIMITY,Doximity,,,{}
KNEAT,Kneat,,,{}
API_HEALTHCARE_STAFFING_AND_SCHEDULING_FROM_SYMPLR,API Healthcare Staffing and Scheduling from symplr,,,{}
PROSPACE,ProSpace,,,{}
JQUERY_MOBILE,jQuery Mobile,,,{}
PHONEGAP,PhoneGap,,,{}
SALESFORCE_MOBILE,Salesforce Mobile,,,{}
SD_WORX,SD Worx,,,{}
GOOGLE_AUTHENTICATOR,Google Authenticator,,,{}
ITENTIAL,Itential,,,{}
THOUSANDEYES,ThousandEyes,,,{}
CANOPEN,CANopen,,,{}
CISCO_DNA_CENTER,Cisco DNA Center,,,{}
AMAZON_TEXTRACT,Amazon Textract,,,{}
INTERTEK,Intertek,,,{}
GPROMS,gPROMS,,,{}
CLOUDSUITE_HCM,CloudSuite HCM,,,{}
APPOINTY,Appointy,,,{}
IVERSITY,iversity,,,{}
UPGRAD,upGrad,,,{}
NOVOED,NovoEd,,,{}
VIRAL_LAUNCH,Viral Launch,,,{}
GLOBALVISION,GlobalVision,,,{}
AWS_SERVER_MIGRATION_SERVICE,AWS Server Migration Service,,,{}
TALEND_DATA_INTEGRATION,Talend Data Integration,,,{}
UBUNTU_DESKTOP,Ubuntu Desktop,,,{}
THE_RISK_MANAGEMENT_CENTER,The Risk Management Center,,,{}
THINAPP,ThinApp,,,{}
METRIC_INSIGHTS,Metric Insights,,,{}
DATAHUB,Datahub,,,{}
ANSYS_FLUENT,Ansys Fluent,,,{}
ANSYS_MESHING,Ansys Meshing,,,{}
STAR_CCM,STAR-CCM+,,,{}
OPENGL,OpenGL,,,{}
CMAKE,Cmake,,,{}
INTEL_QUARTUS_PRIME,Intel Quartus Prime,,,{}
SMARTASSIST,SmartAssist,,,{}
AID4MAIL,Aid4Mail,,,{}
EFFICIENT_FINANCE,Efficient Finance,,,{}
DCRM,dCRM,,,{}
WIND_RIVER_EDGE_SYNC,Wind River Edge Sync,,,{}
AWS_TRANSIT_GATEWAY,AWS Transit Gateway,,,{}
NTLM,NTLM,,,{}
KERBEROS,Kerberos,,,{}
PHENOLOGIC,PhenoLOGIC,,,{}
WHIZAI,WhizAI,,,{}
COMPLIANCEWIRE,ComplianceWire,,,{}
OFFICE_TIMELINE,Office Timeline,,,{}
INTEGRICHAIN_ICYTE_PLATFORM,IntegriChain iCyte Platform,,,{}
ETHERIOS_EASYDESCRIBE,Etherios EasyDescribe,,,{}
XACTLY_QUOTA_AND_TERRITORIES,Xactly Quota & Territories,,,{}
COMBIN,Combin,,,{}
INFINITE_SCROLL,Infinite Scroll,,,{}
WINFORMS,WinForms,,,{}
VIDEO4LINUX_V4L2,Video4Linux (V4L2),,,{}
ZK,ZK,,,{}
PARTNER_INSIGHT,Partner Insight,,,{}
ONEMD,OneMD,,,{}
SALESFORCE_HEALTH_CLOUD,Salesforce Health Cloud,,,{}
AMERICAN_EXPRESS,American Express,,,{}
ALTIUM_DESIGNER,Altium Designer,,,{}
HYPERLYNX,Hyperlynx,,,{}
OPNET_NETWORK_SIMULATOR,OPNET Network simulator,,,{}
BUSINESS_TALENT_GROUP,Business Talent Group,,,{}
VEEVA_CRM,Veeva CRM,,,{}
WINNONLIN,WinNonlin,,,{}
COGNITION_COCKPIT,Cognition Cockpit,,,{}
PROWATCH,ProWatch,,,{}
ENOVIA,Enovia,,,{}
PTC_WINDCHILL,PTC Windchill,,,{}
TEAMCENTER,Teamcenter,,,{}
HOGAN_ASSESSMENT_SYSTEMS,Hogan Assessment Systems,,,{}
REVELIAN,Revelian,,,{}
PREZI,Prezi,,,{}
VISME,Visme,,,{}
POWER_USER,Power-user,,,{}
PAPERCUT,PaperCut,,,{}
CELONIS,Celonis,,,{}
SAP_FIELDGLASS,SAP Fieldglass,,,{}
INVENTOR,Inventor,,,{}
AUTODESK_VAULT,Autodesk Vault,,,{}
SOLIDWORKS_PDM,SolidWorks PDM,,,{}
EPICOR_UK_PRODUCT_DATA_MANAGEMENT_PDM,Epicor UK Product Data Management (PDM),,,{}
SIGMA_CATALOG,Sigma Catalog,,,{}
ROADMUNK,Roadmunk,,,{}
REPLICON,Replicon,,,{}
SAS_BASE,SAS Base,,,{}
VISUAL_BASIC,Visual Basic,,,{}
VISUAL_BASIC_FOR_APPLICATIONS_VBA,Visual Basic for Applications (VBA),,,{}
C_PROGRAMMING_LANGUAGE,C (Programming Language),,,{}
PHP,PHP,,,{}
TANDEM_ADVANCED_COMMAND_LANGUAGE_TACL,Tandem Advanced Command Language (TACL),,,{}
PROJECT_OBJECTS,Project Objects,,,{}
SWIT,Swit,,,{}
PROJECT_DRIVE,Project Drive,,,{}
SAP_PS,SAP PS,,,{}
ONEPAGER,OnePager,,,{}
NEXT_PROJECT,next project,,,{}
PROMOVATE,Promovate,,,{}
UXPIN,UXPin,,,{}
BUILD,BUILD,,,{}
LEADERSHIP_CONNECT,Leadership Connect,,,{}
VORTAL,Vortal,,,{}
SPYDER,Spyder,,,{}
ETQ_RELIANCE,EtQ Reliance,,,{}
TRACKWISE,TrackWise,,,{}
JD_EDWARDS_ENTERPRISEONE_MANUFACTURING_AND_ENGINEERING,JD Edwards EnterpriseOne Manufacturing and Engineering,,,{}
GRAPHDB,GraphDB,,,{}
MARKET_LEADER,Market Leader,,,{}
PIPELINE_ROI,Pipeline ROI,,,{}
EQUEST,eQuest,,,{}
VEEVA_VAULT_QMS,Veeva Vault QMS,,,{}
ENVIANCE_MANAGEMENT_OF_CHANGE,Enviance Management of Change,,,{}
TRUEVAULT_ATLAS,TrueVault Atlas,,,{}
IBM_DB2,IBM DB2,,,{}
METABASE,Metabase,,,{}
LOGMEIN_RESCUE,LogMeIn Rescue,,,{}
BEYONDTRUST_REMOTE_SUPPORT,BeyondTrust Remote Support,,,{}
CASECOMPLETE,CaseComplete,,,{}
INMARKET,inMarket,,,{}
PRICING_HUB,Pricing HUB,,,{}
MODELN,ModelN,,,{}
MANUGISTICS,Manugistics,,,{}
CISCO_ROUTER_ASR_1000,Cisco Router ASR 1000,,,{}
CISCO_ROUTER_ISR_4000,Cisco Router ISR 4000,,,{}
FORECASTABLE,Forecastable,,,{}
SOPRO,Sopro,,,{}
COPYSTORM,CopyStorm,,,{}
GLOBAL_DATABASE,Global Database,,,{}
SAP_SALES,SAP Sales,,,{}
VERTEX_O_SERIES,Vertex O Series,,,{}
MINDTICKLE,MindTickle,,,{}
MASTER_O,Master-O,,,{}
HIREDSCORE,HiredScore,,,{}
NUGENESIS_SDMS,NuGenesis SDMS,,,{}
SPLUNK_ENTERPRISE_SECURITY,Splunk Enterprise Security,,,{}
THREATCONNECT,ThreatConnect,,,{}
SWIMLANE,Swimlane,,,{}
DEMISTO,Demisto,,,{}
RUBRIK,Rubrik,,,{}
TOPDESK,TOPdesk,,,{}
MAILCLARK,MailClark,,,{}
DPD,DPD,,,{}
DHL,DHL,,,{}
GLS,GLS,,,{}
COMSOL_MULTIPHYSICS,Comsol Multiphysics,,,{}
FLEXSIM,FlexSim,,,{}
MASTERCAM,Mastercam,,,{}
MATHCAD,Mathcad,,,{}
MATHEMATICA,Mathematica,,,{}
OPENFOAM,OpenFoam,,,{}
ANSYS_SIWAVE,Ansys Siwave,,,{}
ANSYS_HFSS,Ansys HFSS,,,{}
SOLIDWORKS_SIMULATION,SolidWorks Simulation,,,{}
ANSYS_MECHANICAL_PRO,ANSYS Mechanical Pro,,,{}
SIMPY,SimPy,,,{}
MOLDFLOW,Moldflow,,,{}
LTSPICE,LTSpice,,,{}
PSPICE,PSpice,,,{}
AMAZON_LIVE,Amazon live,,,{}
QUINTLY,quintly,,,{}
SOCIALRANK,SocialRank,,,{}
TALKWALKER,Talkwalker,,,{}
SAP_LICENSE_MANAGEMENT,SAP License Management,,,{}
JFROG_XRAY,JFrog Xray,,,{}
PRACTITEST,PractiTest,,,{}
ESKO_WEBCENTER,ESKO WebCenter,,,{}
TECHNOLOGY_INSIGHT,Technology Insight,,,{}
WOLFSSL,wolfSSL,,,{}
PYLINT,Pylint,,,{}
BETTER_CODE_HUB,Better Code Hub,,,{}
RSTUDIO,RStudio,,,{}
GRAPHPAD_PRISM,GraphPad Prism,,,{}
EVIEWS,eviews,,,{}
MINITAB_STATISTICAL_SOFTWARE,Minitab Statistical Software,,,{}
WONDERWARE_INTOUCH,Wonderware InTouch,,,{}
SIMATIC_STEP_7,SIMATIC STEP 7,,,{}
SAP_SUPPLY_CHAIN_NETWORK_COLLABORATION,SAP Supply Chain Network Collaboration,,,{}
LINKEDIN_TALENT_INSIGHTS,LinkedIn Talent Insights,,,{}
SEEKOUT,SeekOut,,,{}
UDACITY,Udacity,,,{}
KONSOLE,Konsole,,,{}
ARAVO,Aravo,,,{}
HICX,HICX,,,{}
MALTEGO,Maltego,,,{}
VICTORIAMETRICS,VictoriaMetrics,,,{}
ETMS,eTMS,,,{}
CIRCANA,Circana,,,{}
ISOTRAIN,ISOtrain,,,{}
ENVASE,Envase,,,{}
TRIPIT,TripIt,,,{}
TRIPCASE,TripCase,,,{}
BITLY,Bitly,,,{}
SOCIAL_BOARD,Social Board,,,{}
USERZOOM,UserZoom,,,{}
ACCELA,Accela,,,{}
MICROSOFT_APPLICATION_VIRTUALIZATION,Microsoft Application Virtualization,,,{}
FLEXAPP,FLEXAPP,,,{}
WINDOWS_SHELL,Windows Shell,,,{}
FRAME_IO,Frame.io,,,{}
VIDMOB,VidMob,,,{}
MOTION,Motion,,,{}
BRIGHTCOVE,Brightcove,,,{}
OPENVPN,OpenVPN,,,{}
AWS_DIRECT_CONNECT,AWS Direct Connect,,,{}
LEAD_FORENSICS,Lead Forensics,,,{}
BRIGHTSPOT,Brightspot,,,{}
DJANGO_CMS,django CMS,,,{}
WRITE_AS,Write.as,,,{}
AGILE_CONTENT,Agile Content,,,{}
METHODE,Methode,,,{}
MAAK,MAAK,,,{}
USER_ACCESSIBILITY,User Accessibility,,,{}
BOX_RELAY,Box Relay,,,{}
LIGHTHOUSE,Lighthouse,,,{}
TALENT_INSIGHTS,Talent Insights,,,{}
ALTAIR_GRID_ENGINE,Altair Grid Engine,,,{}
QUARK,Quark,,,{}
PUBLISHER,Publisher,,,{}
IN_DESIGN,In-Design,,,{}
VB_NET,VB.net,,,{}
TRIA,TRiA,,,{}
CONDUCTOR,Conductor,,,{}
BRIGHT_LOCAL,Bright Local,,,{}
TRIA_MANAGEMENT_PLATFORM,TRiA management platform,,,{}
EKTRON_CMS,Ektron CMS,,,{}
UMBRACO_CLOUD,Umbraco Cloud,,,{}
GOOGLE_ANALYTICS_4,Google Analytics 4,,,{}
LOOKER_STUDIO,Looker Studio,,,{}
OSCOMMERCE,osCommerce,,,{}
NEXXSYS,Nexxsys,,,{}
WCAG_2_0,WCAG 2.0,,,{}
EXCEL,Excel,,,{}
PYTHON_SARIMA_MODEL,Python (SARIMA model),,,{}
SEO_PRACTICES,SEO practices,,,{}
AJAX,ajax,,,{}
SOLR,Solr,,,{}
TINKERCAD,TinkerCAD,,,{}
ACCOUNTING_SEED,Accounting Seed,,,{}
REVISO,Reviso,,,{}
GCAS,GCAS,,,{}
EXPRESS_ACCOUNTS,Express Accounts,,,{}
EDGE_IMPULSE,Edge Impulse,,,{}
REGIE_AI,regie.ai,,,{}
MICRO_FOCUS_ALM_OCTANE,Micro Focus ALM Octane,,,{}
REMAIN_SOFTWARE,Remain Software,,,{}
APACHE_SUPERSET,Apache Superset,,,{}
FULLSTORY,FullStory,,,{}
CA_API_MANAGEMENT,CA API Management,,,{}
IFTTT,IFTTT,,,{}
DOCKER_ENGINE_ENTERPRISE_FOR_WINDOWS_SERVER_2019,Docker Engine - Enterprise for Windows Server 2019,,,{}
MPULSE,MPulse,,,{}
NETDATA,Netdata,,,{}
AKAMAI_ION,Akamai Ion,,,{}
OPENRESTY,OpenResty,,,{}
APACHE_SERVER,Apache Server,,,{}
VCENTER_SERVER,vCenter Server,,,{}
IBM_HTTP_SERVER,IBM HTTP Server,,,{}
ORACLE_APPLICATION_SERVER,Oracle Application Server,,,{}
HP_ILO,HP iLO,,,{}
APACHE_HTTP_SERVER,Apache HTTP Server,,,{}
REDSHIELD,Redshield,,,{}
GWI,GWI,,,{}
ARKIT,Arkit,,,{}
NETGATE_TNSR_VROUTER,Netgate TNSR vRouter,,,{}
CHECKR,Checkr,,,{}
HEALTHEQUITY,HealthEquity,,,{}
EXPRESS_SCRIPTS_MEDCO,Express Scripts (MEDCO),,,{}
SAP_DATA_SERVICES,SAP Data Services,,,{}
HORTONWORKS_DATA_PLATFORM,Hortonworks Data Platform,,,{}
HP_PROLIANT,HP ProLiant,,,{}
MICROSOFT_BOT_FRAMEWORK,Microsoft Bot Framework,,,{}
BUGSNAG,Bugsnag,,,{}
APACHE_ANT,Apache Ant,,,{}
ASSURANCE,Assurance,,,{}
CISCO_JABBER,Cisco Jabber,,,{}
SOCKET_IO,Socket.Io,,,{}
OPENFIRE,Openfire,,,{}
IBM_BUSINESS_PROCESS_MANAGER,IBM Business Process Manager,,,{}
ACTIVEVOS,ActiveVOS,,,{}
EJBCA_ENTERPRISE,EJBCA Enterprise,,,{}
VIDEO_SDK,video sdk,,,{}
CLOUDPASSAGE,CloudPassage,,,{}
ALIBABA_CLOUD,Alibaba Cloud,,,{}
APPLE_ICLOUD,Apple iCloud,,,{}
TIVOLI_STORAGE_MANAGER_TSM,Tivoli Storage Manager (TSM),,,{}
APACHE_DIRECTORY,Apache Directory,,,{}
VMWARE_SASE,VMware SASE,,,{}
AZURE_FILES,Azure Files,,,{}
CLOUDIFY,Cloudify,,,{}
AZURE_MONITOR,Azure Monitor,,,{}
APACHE_CLOUDSTACK,Apache CloudStack,,,{}
MORPHEUS,Morpheus,,,{}
CLOUDHEALTH,CloudHealth,,,{}
F5_BIG_IP,F5 BIG-IP,,,{}
SNOWFLAKE_CLOUD_MANAGEMENT,Snowflake Cloud Management,,,{}
AZURE_MIGRATE,Azure Migrate,,,{}
IBM_CLOUD,IBM Cloud,,,{}
GOOGLE_APP_ENGINE,Google App Engine,,,{}
MICROSOFT_AZURE_CONTAINERS,Microsoft Azure Containers,,,{}
VARMOUR,vArmour,,,{}
CLOUD_WORKLOAD_SECURITY,Cloud Workload Security,,,{}
AMAZON_S3_GLACIER,Amazon S3 Glacier,,,{}
PRIMENG,PrimeNg,,,{}
OPENPYXL,openpyxl,,,{}
PYTHON_SQL,python sql,,,{}
SALTSTACK,SaltStack,,,{}
CFENGINE,CFEngine,,,{}
TIVOLI_APPLICATION_DEPENDENCY_DISCOVERY_MANAGER_TADDM,Tivoli Application Dependency Discovery Manager (TADDM),,,{}
EXPESITE,Expesite,,,{}
AMDOCS_CUSTOMER_MANAGEMENT,Amdocs Customer Management,,,{}
TWILIO_FLEX,Twilio Flex,,,{}
CISCO_UNIFIED_CONTACT_CENTER_EXPRESS,Cisco Unified Contact Center Express,,,{}
AVAYA_IP_OFFICE,Avaya IP Office,,,{}
GOOGLE_CONTACT_CENTER_AI,Google Contact Center AI,,,{}
AVAYA_CALL_MANAGEMENT_SYSTEM_CMS,Avaya Call Management System (CMS),,,{}
QFINITI,Qfiniti,,,{}
OCI,OCI,,,{}
LXC,LXC,,,{}
OPEN_VSWITCH,Open VSwitch,,,{}
AZURE_CONTAINER_INSTANCES,Azure Container Instances,,,{}
AZURE_SERVICE_FABRIC,Azure Service Fabric,,,{}
HADOOP_YARN,Hadoop YARN,,,{}
AZURE_CONTAINER_REGISTRY,Azure Container Registry,,,{}
GOOGLE_CLOUD_CDN,Google Cloud CDN,,,{}
ATANDT_CONTENT_DELIVERY_NETWORK_CDN,AT&T Content Delivery Network (CDN),,,{}
BUDDYBUILD,buddybuild,,,{}
BUILDMASTER,BuildMaster,,,{}
TRAVIS_CI,Travis CI,,,{}
FLOSUM,Flosum,,,{}
WHISBI,Whisbi,,,{}
POWERPLAN,PowerPlan,,,{}
DOMINKNOW,dominKnow,,,{}
ORACLE_CPQ_CLOUD,Oracle CPQ Cloud,,,{}
SALESFORCE_COMMUNICATIONS_CLOUD,Salesforce Communications Cloud,,,{}
MICROSOFT_DYNAMICS_CRM,Microsoft Dynamics CRM,,,{}
REFLECT_CRM,Reflect CRM,,,{}
MONITOR_360,Monitor 360,,,{}
ACTIONIQ,ActionIQ,,,{}
TECHSEE,TechSee,,,{}
AZURE_DATABASE_FOR_POSTGRESQL,Azure Database for PostgreSQL,,,{}
DBVISUALIZER,DbVisualizer,,,{}
SQLCMD,Sqlcmd,,,{}
TPUMP,TPump,,,{}
TRUSTWAVE_APPDETECTIVEPRO,Trustwave AppDetectivePRO,,,{}
CISCO_DATA_CENTER_NETWORK_MANAGER,Cisco Data Center Network Manager,,,{}
ARISTA_NETWORKS,Arista Networks,,,{}
JUNIPER_QFABRIC_SYSTEM,Juniper QFabric System,,,{}
H3C,H3C,,,{}
BLUE_PLANET,Blue Planet,,,{}
CISCO_ACI,Cisco ACI,,,{}
DATAMINER,Dataminer,,,{}
K2VIEW,K2View,,,{}
CISCO_ACE,Cisco ACE,,,{}
PALANTIR_FOUNDRY,Palantir Foundry,,,{}
RAPIDMINER,RapidMiner,,,{}
H2O_DRIVERLESS_AI,H2O Driverless AI,,,{}
GEPHI,Gephi,,,{}
TELMAR,Telmar,,,{}
VERTICA,Vertica,,,{}
ALTOVA_XMLSPY,Altova XMLSpy,,,{}
COPADO,Copado,,,{}
WOOPRA,Woopra,,,{}
MATOMO,Matomo,,,{}
ASSET_BANK,Asset Bank,,,{}
CATCHPOINT,Catchpoint,,,{}
LIFERAY_DIGITAL_EXPERIENCE_PLATFORM,Liferay Digital Experience Platform,,,{}
CAMUNDA_BPM,Camunda BPM,,,{}
ENVIVO,envivo,,,{}
BRIGHTSIGN,BrightSign,,,{}
DIRECT_MAIL_MANAGER,Direct Mail Manager,,,{}
DOXYGEN,Doxygen,,,{}
RETHINKDB,RethinkDB,,,{}
TUCOWS,Tucows,,,{}
GOOGLE_DOMAINS,Google Domains,,,{}
HCL_APPSCAN,HCL AppScan,,,{}
CART_FUNCTIONALITY,Cart Functionality,,,{}
SMARTCLASS,SmartClass,,,{}
AMAZON_WORKMAIL,Amazon WorkMail,,,{}
HCL_NOTES,HCL Notes,,,{}
POSTUP,PostUp,,,{}
MICROSOFT_EXCHANGE_SERVER_2019,Microsoft Exchange Server 2019,,,{}
CROSSBOX_COMMUNICATION_SUITE,CrossBox Communication Suite,,,{}
HCL_CONNECTIONS,HCL Connections,,,{}
MYOFFICE,MyOffice,,,{}
CISCO_ADVANCED_MALWARE_PROTECTION,Cisco Advanced Malware Protection,,,{}
SPARX,Sparx,,,{}
STORAGETEK,StorageTek,,,{}
TRUESIGHT_CAPACITY_OPTIMIZATION,TrueSight Capacity Optimization,,,{}
AWS_SYSTEMS_MANAGER,AWS Systems Manager,,,{}
NETREO,Netreo,,,{}
AZURE_SEARCH,Azure Search,,,{}
LUCIDWORKS_FUSION,Lucidworks Fusion,,,{}
AZURE_SERVICE_BUS,Azure Service Bus,,,{}
UNISYS_CLEARPATH_AIS,Unisys ClearPath AIS,,,{}
TWIKI,TWiki,,,{}
SAP_BUSINESS_ONE,SAP Business One,,,{}
ONRAMP,OnRamp,,,{}
FIVETRAN,Fivetran,,,{}
INFORMATICA_DATA_ENGINEERING,Informatica Data Engineering,,,{}
OPEN_STUDIO_FOR_DATA_INTEGRATION,Open Studio for Data Integration,,,{}
AMAZON_MSK,Amazon MSK,,,{}
ACCESS_EXPENSE,Access Expense,,,{}
FORSTA,Forsta,,,{}
CORTEX_XDR,Cortex XDR,,,{}
ORACLE_FIELD_SERVICE_CLOUD,Oracle Field Service Cloud,,,{}
SALESFORCE_FIELD_SERVICE,Salesforce Field Service,,,{}
SECUREDATA,SecureData,,,{}
CONNECT_DIRECT,Connect Direct,,,{}
BLOOMBERG_TERMINAL,Bloomberg Terminal,,,{}
PALO_ALTO_PA_5260,Palo Alto PA-5260,,,{}
SONICWALL,SonicWall,,,{}
CISCO_ADAPTIVE_SECURITY_VIRTUAL_APPLIANCE_ASAV,Cisco Adaptive Security Virtual Appliance (ASAv),,,{}
PALO_ALTO_VM_SERIES,Palo Alto VM-Series,,,{}
JUNIPER_FIREWALL,Juniper Firewall,,,{}
JUNIPER_VSRX,Juniper vSRX,,,{}
MYGEOTAB,MyGeotab,,,{}
FLEET_COMPLETE,Fleet Complete,,,{}
PROLABORATE,Prolaborate,,,{}
MICROSTATION,MicroStation,,,{}
MIDJOURNEY,Midjourney,,,{}
QGIS,QGIS,,,{}
ESRI_ARCGIS,Esri ArcGIS,,,{}
ARCGIS_PRO,ArcGIS Pro,,,{}
MAPBOX,Mapbox,,,{}
ORACLE_SPATIAL,Oracle Spatial,,,{}
ARCGIS_FOR_DESKTOP_BASIC,ArcGIS for Desktop Basic,,,{}
ARCVIEW,ArcView,,,{}
ARCMAP,ArcMap,,,{}
ARCSDE,ArcSDE,,,{}
MARKET_INSIDE,Market Inside,,,{}
RISKONNECT,Riskonnect,,,{}
VALIDATE_ADDRESS_FOR_G_SUITE,Validate Address for G Suite,,,{}
SANITY,Sanity,,,{}
TRUSTWAVE_HEALTH_CARE,Trustwave Health Care,,,{}
ADOBE_ROBOHELP,Adobe RoboHelp,,,{}
FLEXPOD,Flexpod,,,{}
IBM_SECURITY_VERIFY,IBM Security Verify,,,{}
IBM_SECURITY_VERIFY_ACCESS,IBM Security Verify Access,,,{}
INFLUENTIALS,Influentials,,,{}
AZURE_VIRTUAL_MACHINES,Azure Virtual Machines,,,{}
CISCO_JASPER,Cisco Jasper,,,{}
AZURE_IOT_CENTRAL,Azure IoT Central,,,{}
ATANDT_IOT_PLATFORM,AT&T IoT Platform,,,{}
AZURE_LOGIC_APPS,Azure Logic Apps,,,{}
ORACLE_INTEGRATION_CLOUD,Oracle Integration Cloud,,,{}
PLANVIEW_ENTERPRISE_ONE,Planview Enterprise One,,,{}
AZURE_POLICY,Azure Policy,,,{}
JSHINT,JSHint,,,{}
CHART_JS,Chart.Js,,,{}
MOMENT_JS,Moment.js,,,{}
AG_GRID,Ag Grid,,,{}
NESTJS,Nestjs,,,{}
WIJMO,Wijmo,,,{}
REDUX_JS,Redux.js,,,{}
LODASH,Lodash,,,{}
VUEX,Vuex,,,{}
PLOTLY,Plotly,,,{}
APACHE_FELIX,Apache Felix,,,{}
POWERMOCK,PowerMock,,,{}
JFREECHART,JFreeChart,,,{}
DROPWIZARD,Dropwizard,,,{}
JBEHAVE,Jbehave,,,{}
DELL_LAPTOP,Dell Laptop,,,{}
PANASONIC_TOUGHBOOK,Panasonic ToughBook,,,{}
ILIAS,ILIAS,,,{}
APPLE_BUSINESS_CHAT,Apple Business Chat,,,{}
AZURE_TRAFFIC_MANAGER,Azure Traffic Manager,,,{}
CLOUDTEST,CloudTest,,,{}
NGINX_PLUS,Nginx Plus,,,{}
GLOBAL_TRAFFIC_MANAGEMENT,Global Traffic Management,,,{}
PURELOAD,PureLoad,,,{}
AKAMAI_CLOUDTEST,Akamai CloudTest,,,{}
MOMENTFEED,MomentFeed,,,{}
GRAYLOG,Graylog,,,{}
ORACLE_APPLICATION_EXPRESS,Oracle Application Express,,,{}
THYMELEAF,Thymeleaf,,,{}
HCL_DOMINO,HCL Domino,,,{}
LEVELUP,LevelUp,,,{}
ADARA,Adara,,,{}
THEANO,Theano,,,{}
MACHINE_LEARNING_IN_PYTHON,machine-learning in Python,,,{}
ASAPP,ASAPP,,,{}
ATANDT_MANAGED_THREAT_DETECTION_AND_RESPONSE_SERVICE,AT&T Managed Threat Detection and Response Service,,,{}
SYMMETRIX,Symmetrix,,,{}
VIRTUAL_PROCESS,Virtual Process,,,{}
BROADRIDGE_INTELLIGENT_ANALYTICS,Broadridge Intelligent Analytics,,,{}
OPAL,Opal,,,{}
ALLOCADIA,Allocadia,,,{}
ORACLE_ENTERPRISE_DATA_MANAGEMENT_CLOUD,Oracle Enterprise Data Management Cloud,,,{}
ZEROMQ,ZeroMQ,,,{}
SWC,SWC,,,{}
WEBSOCKET,Websocket,,,{}
SPLUNK_MINT,Splunk MINT,,,{}
WANDERA,Wandera,,,{}
SYMANTEC_ENDPOINT_PROTECTION_MOBILE,Symantec Endpoint Protection Mobile,,,{}
IONIC,Ionic,,,{}
ADOBE_COLDFUSION,Adobe ColdFusion,,,{}
ANDROID_NDK,Android NDK,,,{}
MONACA,Monaca,,,{}
APPLE_PHONE,Apple Phone,,,{}
SAFEGUARD_GLOBAL_GMP,Safeguard Global GMP,,,{}
JUNIPER_NFV,Juniper NFV,,,{}
MICRO_FOCUS_NETWORK_AUTOMATION,Micro Focus Network Automation,,,{}
ARUBA_AIRWAVE,Aruba AirWave,,,{}
ENTUITY,Entuity,,,{}
HP_OPENVIEW,HP Openview,,,{}
OPENNMS_PLATFORM,OpenNMS Platform,,,{}
NGENIUSONE,nGeniusONE,,,{}
DRIVENETS,DriveNets,,,{}
NETWORK_NODE_MANAGER_I_NNMI,Network Node Manager i (NNMi),,,{}
MCAFEE_EPOLICY_ORCHESTRATOR,McAfee ePolicy Orchestrator,,,{}
MULTI_DOMAIN_SECURITY_MANAGEMENT,Multi-Domain Security Management,,,{}
VORMETRIC,Vormetric,,,{}
MILANOTE,Milanote,,,{}
JAVA_DATABASE_CONNECTIVITY_JDBC,Java Database Connectivity (JDBC),,,{}
AZURE_BLOB_STORAGE,Azure Blob Storage,,,{}
AMAZON_S3_ADAPTER_FOR_SAP_CPI,Amazon S3 Adapter for SAP CPI,,,{}
ISTARS,iSTARS,,,{}
WPS_OFFICE,WPS Office,,,{}
MYTIME,MyTime,,,{}
FME,FME,,,{}
IBM_AIX,IBM AIX,,,{}
ARISTA_EOS,Arista EOS,,,{}
WINDOWS_7,Windows 7,,,{}
MACOS_SIERRA,macOS Sierra,,,{}
SCIENTIFIC_LINUX,Scientific Linux,,,{}
TIZEN,Tizen,,,{}
UBUNTU_SERVER,Ubuntu Server,,,{}
KALI_LINUX,Kali Linux,,,{}
Z_VM,z/VM,,,{}
IOS_XR,IOS-XR,,,{}
NX_OS,NX-OS,,,{}
OPENWRT,OpenWRT,,,{}
SUSE,SUSE,,,{}
IBWAVE_DESIGN,iBwave Design,,,{}
ZIPKIN,Zipkin,,,{}
WEBTRENDS_ANALYTICS_FOR_SHAREPOINT,Webtrends Analytics for SharePoint,,,{}
TERADATA_INTELLICLOUD_AS_A_SERVICE,Teradata IntelliCloud As-a-service,,,{}
NODE_RED,Node-RED,,,{}
AZURE_PRIVATE_LINK,Azure Private Link,,,{}
ANETWORK,Anetwork,,,{}
EQUIFAX,Equifax,,,{}
ADOBE_FLASH_BUILDER,Adobe Flash Builder,,,{}
AMAZON_EC2_AUTO_SCALING,Amazon EC2 Auto Scaling,,,{}
C_LEVEL_CONNECTIONS,C-level connections,,,{}
GRAVITYVIEW,GravityView,,,{}
IBM_CAMPAIGN,IBM Campaign,,,{}
TELESTREAM,Telestream,,,{}
GOOGLE_WEB_TOOLKIT,Google Web Toolkit,,,{}
HANDLEBARS,Handlebars,,,{}
FONT_AWESOME,Font Awesome,,,{}
JAVASERVER_PAGES,JavaServer Pages,,,{}
KALTURA,Kaltura,,,{}
BITPAY,BitPay,,,{}
MASTERCARD,Mastercard,,,{}
PAYCOM,Paycom,,,{}
ORACLE_TALEO,Oracle Taleo,,,{}
QUANTUM_WORKPLACE,Quantum Workplace,,,{}
ADOBE_FIREFLY,Adobe Firefly,,,{}
PHPSTORM,PhpStorm,,,{}
BCAST,bCast,,,{}
RICOH_INFOPRINT,Ricoh InfoPrint,,,{}
AWS_SECRETS_MANAGER,AWS Secrets Manager,,,{}
MIR3,Mir3,,,{}
RALLY_SOFTWARE,Rally Software,,,{}
BAZAARVOICE,Bazaarvoice,,,{}
VISUAL_FOXPRO,Visual FoxPro,,,{}
BUSINESS_PROCESS_EXECUTION_LANGUAGE_BPEL,Business Process Execution Language (BPEL),,,{}
CLOJURE,Clojure,,,{}
COLLABORATIVE_APPLICATION_MARKUP_LANGUAGE_CAML,Collaborative Application Markup Language (CAML),,,{}
JYTHON,Jython,,,{}
PROGRAMMING_LANGUAGE_ONE,Programming Language One,,,{}
VOICEXML_VXML,VoiceXML (VXML),,,{}
AUTOIT,AutoIT,,,{}
XACML,XACML,,,{}
SCALA_PROGRAMMING_LANGUAGE,Scala (Programming Language),,,{}
PROJECT_PORTFOLIO_MANAGEMENT,Project Portfolio Management,,,{}
DHTMLX_UI,DHTMLX UI,,,{}
PLANNING_POKER,Planning Poker,,,{}
FLINTO,Flinto,,,{}
IRISE,iRise,,,{}
PROTO_IO,proto.io,,,{}
ATANDT_ENHANCED_PUSH_TO_TALK,AT&T Enhanced Push-to-Talk,,,{}
SELENIUM_IDE,Selenium IDE,,,{}
CHERRYPY,CherryPy,,,{}
CISCO_UCS_C_SERIES,Cisco UCS C-Series,,,{}
IPACS,IPACS,,,{}
REGULATORY_INTELLIGENCE,Regulatory Intelligence,,,{}
IBM_INFORMIX,IBM Informix,,,{}
TERADATA_ASTER,Teradata Aster,,,{}
PRESTODB,PrestoDB,,,{}
KSQLDB,ksqlDB,,,{}
QEMU,Qemu,,,{}
AZURE_BASTION,Azure Bastion,,,{}
REMOTE_DESKTOP_SERVICES,Remote Desktop Services,,,{}
VYSOR,Vysor,,,{}
SHOPPERTRAK,ShopperTrak,,,{}
BALBIX,Balbix,,,{}
SERVICENOW_SECURITY_OPERATIONS,ServiceNow Security Operations,,,{}
IBM_TIVOLI_WORKLOAD_SCHEDULER,IBM Tivoli Workload Scheduler,,,{}
AUTOMATE_ROBOTIC_PROCESS_AUTOMATION,Automate Robotic Process Automation,,,{}
TENJIN_GROUP,Tenjin Group,,,{}
FORTIGATE_FIREWALL,FortiGate Firewall,,,{}
JUNIPER_ROUTER,Juniper Router,,,{}
CISCO_ROUTER_ASR_9000,Cisco Router ASR 9000,,,{}
CISCO_ROUTER_ISR_1100,Cisco Router ISR 1100,,,{}
APACHE_KARAF,Apache Karaf,,,{}
APACHE_SERVICEMIX,Apache Servicemix,,,{}
RED_HAT_CEPH_STORAGE,Red Hat Ceph Storage,,,{}
SERVICENOW_APPLICATION_PORTFOLIO_MANAGEMENT,ServiceNow Application Portfolio Management,,,{}
QBS,QBS,,,{}
WEBPHONE,Webphone,,,{}
MYTRAILHEAD,myTrailhead,,,{}
SNAGIT,snagit,,,{}
CONVIVA,Conviva,,,{}
CONTRAIL,Contrail,,,{}
VELOCLOUD,VeloCloud,,,{}
EXPEREO,Expereo,,,{}
OPENDAYLIGHT,OpenDaylight,,,{}
ALIENVAULT_USM_FROM_ATANDT_CYBERSECURITY,AlienVault USM (from AT&T Cybersecurity),,,{}
REDSEAL,RedSeal,,,{}
SPIRION,Spirion,,,{}
MICROSOFT_INFORMATION_PROTECTION,Microsoft Information Protection,,,{}
BIQ,BiQ,,,{}
ONCRAWL,OnCrawl,,,{}
AZURE_BACKUP,Azure Backup,,,{}
7SIGNAL,7SIGNAL,,,{}
VMWARE_ESXI,VMware ESXi,,,{}
ORACLE_VM,Oracle VM,,,{}
VMWARE_WORKSTATION,VMware Workstation,,,{}
ISUPPORT_SOFTWARE,iSupport Software,,,{}
LINKERD,linkerd,,,{}
AZURE_COMPUTE,Azure Compute,,,{}
SAFEGUARD_LM,SafeGuard LM,,,{}
PRISMA_ACCESS_FORMERLY_GLOBALPROTECT_CLOUD_SERVICE,Prisma Access (formerly GlobalProtect cloud service),,,{}
GREMLIN,Gremlin,,,{}
BETA_TESTING,Beta Testing,,,{}
ROHDE_AND_SCHWARZ_MOBILE_NETWORK_TESTING,Rohde & Schwarz Mobile Network Testing,,,{}
SCILAB,SciLab,,,{}
SAS_ENTERPRISE_MINER,SAS Enterprise Miner,,,{}
ISUPPLIER,iSupplier,,,{}
CISCO_CATALYST_2960_SERIES,Cisco Catalyst 2960 Series,,,{}
ARISTA_SWITCH,Arista Switch,,,{}
ENSIGHTEN,Ensighten,,,{}
PLURALSIGHT,Pluralsight,,,{}
OFFENSIVE_SECURITY_PEN_200_OSCP,Offensive Security PEN-200 (OSCP),,,{}
CISCO_WEBEX_CONTACT_CENTER,Cisco Webex Contact Center,,,{}
KOBITON,Kobiton,,,{}
IDS,ids,,,{}
SUBLIME_TEXT,Sublime Text,,,{}
NOTEPAD,Notepad++,,,{}
CISCO_TALOS,Cisco Talos,,,{}
ORACLE_TRANSPORTATION_MANAGEMENT_CLOUD,Oracle Transportation Management Cloud,,,{}
RADAR_LIVE,Radar Live,,,{}
IBM_MAAS360,IBM MaaS360,,,{}
APP_VOLUMES,App Volumes,,,{}
HORIZON_7,Horizon 7,,,{}
TORTOISE_SVN,Tortoise SVN,,,{}
QUMU,Qumu,,,{}
ATANDT_CONNECT,AT&T Connect,,,{}
WEBEX_APP,Webex App,,,{}
LOOP_TEAM,Loop Team,,,{}
WEBEX_CALLING,Webex Calling,,,{}
ATANDT_COLLABORATE,AT&T Collaborate,,,{}
CISCO_HOSTED_COLLABORATION_SOLUTION,Cisco Hosted Collaboration Solution,,,{}
CISCO_VOICE_GATEWAYS,Cisco Voice Gateways,,,{}
OPENVAS,OpenVAS,,,{}
ELEMENTOR,Elementor,,,{}
SAILPOINT_IDENTITYIQ,Sailpoint IdentityIQ,,,{}
ASTRA_SECURITY_SUITE,Astra Security Suite,,,{}
WSSECURITY,WSSecurity,,,{}
ARCGIS_WORKFLOW_MANAGER,ArcGIS Workflow Manager,,,{}
INTRADIEM,Intradiem,,,{}
INFOR_CLOUDSUITE_WORKFORCE_MANAGEMENT_WFM,Infor CloudSuite Workforce Management (WFM),,,{}
ATANDT_WORKFORCE_MANAGER,AT&T Workforce Manager,,,{}
LEAVELINK,LeaveLink,,,{}
CUSTOMER_INFORMATION_SYSTEM,Customer Information System,,,{}
VCLOUD_DIRECTOR,vCloud Director,,,{}
IBM_CLOUD_ORCHESTRATOR,IBM Cloud Orchestrator,,,{}
SCREENDRAGON,Screendragon,,,{}
CLARIS_FILEMAKER,Claris FileMaker,,,{}
AWS_CLOUD_PLATFORM,AWS Cloud Platform,,,{}
JAVASCRIPT_JQUERY,JavaScript/jQuery,,,{}
NET_FRAMEWORK_CORE,.NET Framework/Core,,,{}
T-SQL,T-SQL,,,{}
AWS_DATA_AND_ANALYTICS_CLOUD_COMPONENTS_INCLUDING_S3,AWS Data & Analytics cloud components (including S3),,,{}
GOOGLE_ANALYTICS_GA4,Google Analytics/GA4,,,{}
BICEP,Bicep,,,{}
SIEM,SIEM,,,{}
OKTA_IDENTITY_AND_ACCESS_MANAGEMENT_PLATFORM,Okta Identity and Access Management Platform,,,{}
MICROSOFT_COPILOT,Microsoft CoPilot,,,{}
CI_CD_PIPELINES,CI/CD Pipelines,,,{}
PIPEDRIVE,Pipedrive,,,{}
GUIDEWIRE,GuideWire,,,{}
PEGA,Pega,,,{}
MICROSOFT_DYNAMICS,Microsoft Dynamics,,,{}
AWS_SERVICE_MANAGEMENT_CONNECTOR,AWS Service Management Connector,,,{}
INRULE_TECHNOLOGY,InRule Technology,,,{}
RICOH_ESHOP,Ricoh eShop,,,{}
SUMO_LOGIC_CONTINUOUS_INTELLIGENCE_PLATFORM,Sumo Logic Continuous Intelligence Platform,,,{}
RS_X_RATING,RS X Rating,,,{}
DRC_RATER,DRC Rater,,,{}
MS_CO_PILOT,MS Co-Pilot,,,{}
LOW_CODE_NO_CODE_LCNC,Low-Code/No-Code (LCNC),,,{}
KQL,KQL,,,{}
MITRE_ATT_AND_CK_FRAMEWORK,MITRE ATT&CK framework,,,{}
BOTTOMLINE,BottomLine,,,{}
EBIX_PLACINGHUB,Ebix PlacingHub,,,{}
MS_AZURE_WEBAPPS,MS Azure WebApps,,,{}
SBS_ECLIPSE,SBS Eclipse,,,{}
AZURE_AI,Azure AI,,,{}
OPENAI,OpenAI,,,{}
MICROSOFT_COPILOT_STUDIO,Microsoft CoPilot Studio,,,{}
AZURE_AI_SEARCH,Azure AI Search,,,{}
GENIUS,Genius,,,{}
PAS,PAS,,,{}
GUIDEWIRE_POLICYCENTER,Guidewire PolicyCenter,,,{}
BILLINGCENTRE,BillingCentre,,,{}
DIGITAL,Digital,,,{}
CLAIMSCENTRE,ClaimsCentre,,,{}
NET8,.NET8,,,{}
AWS_APIGATEWAY,AWS APIGateway,,,{}
ZEPHYR,Zephyr,,,{}
MICROSOFT_PLAYWRIGHT,Microsoft Playwright,,,{}
AZURE_INFRASTRUCTURE,Azure infrastructure,,,{}
SITECORE_EXPERIENCE_PLATFORM,Sitecore Experience Platform,,,{}
AZURE_API_MANAGEMENT_APIM,Azure API Management (APIM),,,{}
CIRRUS_CONNECTS,Cirrus Connects,,,{}
MATTERS_OLUS,matters+,,,{}
WEBRECRUIT,Webrecruit,,,{}
MVC,MVC,,,{}
SQL_SERVER,SQL Server,,,{}
MICROSOFT_ETL_TOOLS,Microsoft ETL tools,,,{}
NEWGEN,Newgen,,,{}
DYNAMICS_365,Dynamics 365,,,{}
MANTISBT,MantisBT,,,{}
OPENTEXT_EXSTREAM,OpenText Exstream,,,{}
SURFACE,Surface,,,{}
SPINNAKER,Spinnaker,,,{}
3DS_MAX_DESIGN,3ds Max Design,,,{}
TILT_BRUSH,Tilt Brush,,,{}
AI_BUILD,Ai Build,,,{}
MITSUBA,Mitsuba,,,{}
KARTE,Karte,,,{}
MOONSHOT,Moonshot,,,{}
LEANDATA,LeanData,,,{}
DEMANDBASE,Demandbase,,,{}
TRIBLIO,Triblio,,,{}
BOOK_KEEPER,Book Keeper,,,{}
CLOUD_FINANCIALS,Cloud Financials,,,{}
MONITE,Monite,,,{}
SAGE_UBS,Sage UBS,,,{}
PAYMENT_RAILS,Payment Rails,,,{}
FINTEL_CONNECT,Fintel Connect,,,{}
MLPERF,MLPerf,,,{}
VERTEX_AI_WORKBENCH,Vertex AI Workbench,,,{}
WEIGHTS_AND_BIASES,Weights & Biases,,,{}
GOOGLE_BARD,Google Bard,,,{}
GOOGLE_CLOUD_ANTHOS,Google Cloud Anthos,,,{}
GOOGLE_ADS_CONVERSION_TRACKING,Google Ads Conversion Tracking,,,{}
CHARTIO,Chartio,,,{}
EDGE_INTELLIGENCE,Edge Intelligence,,,{}
DYNAMIC_AI_ENTERPRISE,Dynamic AI Enterprise,,,{}
CREATE_STUDIO,Create Studio,,,{}
GOOGLE_APIGEE_API_MANGAGEMENT_PLATFORM,Google Apigee API Mangagement Platform,,,{}
GOOGLE_CLOUD_ENDPOINTS,Google Cloud Endpoints,,,{}
GOOGLE_CLOUD_APIS,Google Cloud APIs,,,{}
GOOGLE_ADMOB,Google AdMob,,,{}
INMOBI,InMobi,,,{}
PEOPLEBANK,Peoplebank,,,{}
GOOGLE_CLOUD_CONSOLE,Google Cloud Console,,,{}
CHECKMK,Checkmk,,,{}
FIREBASE_PERFORMANCE_MONITORING,Firebase Performance Monitoring,,,{}
TRACE32,Trace32,,,{}
CLUSTER_AUTOSCALER_CONTAINER_SOLUTION,Cluster Autoscaler Container Solution,,,{}
GOOGLE_WEB_SERVER,Google Web Server,,,{}
SYNNEX,Synnex,,,{}
ANGIE,Angie,,,{}
DENO,Deno,,,{}
MOBILEAR,MobileAR,,,{}
OPEN_HYBRID,Open Hybrid,,,{}
AUTOSAR,AUTOSAR,,,{}
ASSESS_AI,Assess.ai,,,{}
OPENFINANCE,Openfinance,,,{}
GOOGLE_ATTRIBUTION,Google Attribution,,,{}
ADOBE_AUDITION,Adobe Audition,,,{}
AUTORAMP,AutoRamp,,,{}
QUANTUM_CONTROL,Quantum Control,,,{}
DATAPATH,DataPath,,,{}
GOOGLE_CLOUD_DATAPROC,Google Cloud Dataproc,,,{}
IBM_BIGINSIGHTS,IBM BigInsights,,,{}
GOOGLE_CLOUD_PERSISTENT_DISK,Google Cloud Persistent Disk,,,{}
SUBQUERY,SubQuery,,,{}
NETGALLEY,NetGalley,,,{}
GOOGLE_CLOUD_RECAPTCHA_ENTERPRISE,Google Cloud reCAPTCHA Enterprise,,,{}
CHATBASE,Chatbase,,,{}
GUPSHUP,Gupshup,,,{}
CHROME,Chrome,,,{}
GOOGLE_CLOUD_DEBUGGER,Google Cloud Debugger,,,{}
WINDBG,WinDbg,,,{}
GOOGLE_CLOUD_BUILD,Google Cloud Build,,,{}
GOOGLE_MESSENGER,Google Messenger,,,{}
TRILLIAN,Trillian,,,{}
GOOGLE_CALENDAR,Google Calendar,,,{}
GRAYSCALE,Grayscale,,,{}
SITETRACKER,Sitetracker,,,{}
CHILDCARE_MANAGER_PROFESSIONAL,Childcare Manager Professional,,,{}
CIVIL_3D,Civil 3D,,,{}
INFOBIP,Infobip,,,{}
SINCH,Sinch,,,{}
DISKSTATION,DiskStation,,,{}
GOOGLE_CLOUD_ARMOR,Google Cloud Armor,,,{}
GOOGLE_CLOUD_FILESTORE,Google Cloud Filestore,,,{}
GOOGLE_CLOUD_RESOURCE_MANAGER,Google Cloud Resource Manager,,,{}
ELASTIFILE,Elastifile,,,{}
GOOGLE_CLOUD_DEPLOYMENT_MANAGER,Google Cloud Deployment Manager,,,{}
KUBEVIRT,KubeVirt,,,{}
GOOGLE_CLOUD_MONITORING,Google Cloud Monitoring,,,{}
GOOGLE_CLOUD_STORAGE_TRANSFER_SERVICE,Google Cloud Storage Transfer Service,,,{}
APPSCALE,AppScale,,,{}
GOOGLE_CLOUD_FUNCTIONS,Google Cloud Functions,,,{}
SAP_CLOUD_PLATFORM,SAP Cloud Platform,,,{}
GOOGLE_CLOUD_PLATFORM_SECURITY_OVERVIEW,Google Cloud Platform Security Overview,,,{}
GOOGLE_CLOUD_SECURITY_COMMAND_CENTER,Google Cloud Security Command Center,,,{}
TUBEBUDDY,TubeBuddy,,,{}
FEEDSPOT,Feedspot,,,{}
JAMBOARD,Jamboard,,,{}
SIMILARWEB,Similarweb,,,{}
FLUTTER,flutter,,,{}
KEYSIGHT_ADS,Keysight ADS,,,{}
GOOGLE_CLOUD_ANTHOS_CONFIG_MANAGEMENT,Google Cloud Anthos Config Management,,,{}
FUNDING_CHOICES,Funding Choices,,,{}
DIDOMI,Didomi,,,{}
PLANGRID,PlanGrid,,,{}
AUTODESK_BIM_360,Autodesk BIM 360,,,{}
AUTODESK_CONSTRUCTION_CLOUD,Autodesk Construction Cloud,,,{}
PROCORE,Procore,,,{}
CONTENT_GURU,Content Guru,,,{}
UJET,UJET,,,{}
VERINT_WORKFORCE_MANAGEMENT,Verint Workforce Management,,,{}
RED_HAT_OPENSHIFT_CONTAINER_PLATFORM,Red Hat OpenShift Container Platform,,,{}
TRITON_SMARTOS,Triton SmartOS,,,{}
GOOGLE_CLOUD_RUN,Google Cloud Run,,,{}
GOOGLE_CONTAINER_REGISTRY,Google Container Registry,,,{}
ACROLINX,Acrolinx,,,{}
GOOGLE_CLOUD_INTERCONNECT,Google Cloud Interconnect,,,{}
GOOGLE_HOSTED_LIBRARIES,Google Hosted Libraries,,,{}
PATHFACTORY,PathFactory,,,{}
DOCUSIGN_CLM,DocuSign CLM,,,{}
INTELLUM_PLATFORM,Intellum Platform,,,{}
QUADERNO,Quaderno,,,{}
PACKAGE_TRACKER,Package Tracker,,,{}
HOTMART,Hotmart,,,{}
BEHANCE,Behance,,,{}
COLLECTION_PARTNER,Collection Partner,,,{}
WITH_REACH,With Reach,,,{}
COINBASE,Coinbase,,,{}
GOOGLE_CLOUD_IDENTITY_PLATFORM,Google Cloud Identity Platform,,,{}
GOOGLE_SIGN-IN,Google Sign-in,,,{}
ADBRAIN,Adbrain,,,{}
OPENDCIM,openDCIM,,,{}
CIENA_WAVESERVER,Ciena Waveserver,,,{}
CRUNCHBASE,Crunchbase,,,{}
FLATFILE,Flatfile,,,{}
FIGURE_EIGHT_PREVIOUSLY_KNOWN_AS_CROWDFLOWER,Figure Eight (previously known as CrowdFlower),,,{}
PLAINSIGHT,Plainsight,,,{}
BIGML,BigML,,,{}
DATAROBOT,DataRobot,,,{}
GOOGLE_CLOUD_AI_HUB,Google Cloud AI Hub,,,{}
GOOGLE_CLOUD_AI_PLATFORM,Google Cloud AI Platform,,,{}
VISUALIZATION_TOOLKIT_VTK,Visualization ToolKit (VTK),,,{}
YAGUARA,Yaguara,,,{}
DTEX,Dtex,,,{}
GOOGLE_CLOUD_DATASTORE,Google Cloud Datastore,,,{}
SAP_HANA_CLOUD,SAP HANA Cloud,,,{}
GOOGLE_CLOUD_BIGTABLE,Google Cloud Bigtable,,,{}
ORACLE_DATA_HUB_CLOUD_SERVICE,Oracle Data Hub Cloud Service,,,{}
MACBOOK_PRO,MacBook Pro,,,{}
XARA_PAGE_AND_LAYOUT_DESIGNER_11,Xara Page & Layout Designer 11,,,{}
YED,yEd,,,{}
HITWISE,Hitwise,,,{}
LATENTVIEW,LatentView,,,{}
SIMPLE_ANALYTICS,Simple Analytics,,,{}
COLLABORO,Collaboro,,,{}
MAPS_SYSTEM,MaPS System,,,{}
ACCREDIBLE,Accredible,,,{}
SALESFORCE_CONTENT_MANAGEMENT,Salesforce Content Management,,,{}
WIDEVINE,Widevine,,,{}
SPECIALIST_APPS,Specialist Apps,,,{}
GOOGLE_DISPLAY_AD_NETWORK,Google Display Ad Network,,,{}
PDFELEMENT,PDFelement,,,{}
GOOGLE_CLOUD_FIRESTORE,Google Cloud Firestore,,,{}
GOOGLE_CLOUD_DOCUMENT_AI,Google Cloud Document AI,,,{}
GODADDY,GoDaddy,,,{}
OVH,OVH,,,{}
KRITA,Krita,,,{}
PAINTTOOL_SAI,PaintTool SAI,,,{}
PRESTASHOP,PrestaShop,,,{}
SELLFY,Sellfy,,,{}
SHOPIFY_PLUS,Shopify Plus,,,{}
GOOGLE_PAY,Google Pay,,,{}
DOKAN,Dokan,,,{}
EKM,EKM,,,{}
HARAVAN,Haravan,,,{}
SHOPLINE,Shopline,,,{}
GEMMS,GEMMS,,,{}
WEB_EDI,Web EDI,,,{}
SENDY,Sendy,,,{}
SNOVIO,Snovio,,,{}
ZIMBRA,Zimbra,,,{}
STAFFBASE,Staffbase,,,{}
PEAKON,Peakon,,,{}
SUITABLE_AI,Suitable AI,,,{}
GOOGLE_CLOUD_KEY_MANAGEMENT_SERVICE,Google Cloud Key Management Service,,,{}
FILEVAULT,Filevault,,,{}
NUVOLO,Nuvolo,,,{}
AODOCS,AoDocs,,,{}
GOOGLE_VAULT,Google Vault,,,{}
SYMPHONY_SUMMITAI_IT_SERVICE_MANAGEMENT,Symphony SummitAI - IT Service Management,,,{}
GOOGLE_CLOUD_SEARCH,Google Cloud Search,,,{}
GOOGLE_CUSTOM_SEARCH,Google Custom Search,,,{}
ODBC_DRIVER_FOR_POSTGRESQL,ODBC driver for PostgreSQL,,,{}
SHAREWORKS,Shareworks,,,{}
SAP_RISE,Sap Rise,,,{}
ALOOMA,Alooma,,,{}
ODOO_FIELD_SERVICE,Odoo Field Service,,,{}
FACTSET,FactSet,,,{}
WAGESTREAM,Wagestream,,,{}
PFSENSE,pfSense,,,{}
OCROLUS,Ocrolus,,,{}
AVATAR_SYSTEMS,Avatar Systems,,,{}
2CAPTCHA,2Captcha,,,{}
ID_ME,ID.me,,,{}
SERVICESOURCE,ServiceSource,,,{}
UNIDAYS,unidays,,,{}
FIVERR,Fiverr,,,{}
FREELANCER_COM,Freelancer.com,,,{}
YUNOJUNO,YunoJuno,,,{}
ALLO,Allo,,,{}
GOOGLE_APPS_SCRIPT_FOR_G_SUITE,Google Apps Script for G Suite,,,{}
ROBLOX_ADS_MANAGER,Roblox ads manager,,,{}
BUILDBOX,Buildbox,,,{}
GOOGLE_CLOUD_GAME_SERVERS,Google Cloud Game Servers,,,{}
FREECAD,FreeCAD,,,{}
TIGERGRAPH,Tigergraph,,,{}
SERVICENOW_GOVERNANCE_RISK_AND_COMPLIANCE,"ServiceNow Governance, Risk and Compliance",,,{}
KEEPING,Keeping,,,{}
GOOGLE_CLOUD_IDENTITY_AND_ACCESS_MANAGEMENT_IAM,Google Cloud Identity & Access Management (IAM),,,{}
OPENLDAP,OpenLDAP,,,{}
GOOGLE_CLOUD_VISION_API,Google Cloud Vision API,,,{}
PILOT_AI,Pilot AI,,,{}
CALL_BOX,Call Box,,,{}
FAMEBIT,FameBit,,,{}
INFLUENSTER,Influenster,,,{}
NOXINFLUENCER,NoxInfluencer,,,{}
CLOUD_INSURANCE,Cloud Insurance,,,{}
GOOGLE_EDGE_TPU,Google Edge TPU,,,{}
ANDROID_THINGS,Android Things,,,{}
ZEPHYR_RTOS,Zephyr RTOS,,,{}
CAMEYO,Cameyo,,,{}
ZAPIER,Zapier,,,{}
SAP_CHARM,SAP ChaRM,,,{}
GUICE,Guice,,,{}
CLOSURE_LIBRARY,Closure Library,,,{}
FLICKITY,Flickity,,,{}
LIBPHONENUMBER,libphonenumber,,,{}
POLYFILL,Polyfill,,,{}
POLYMER_PROJECT,Polymer Project,,,{}
GOOGLE_CLOUD_MEMORYSTORE,Google Cloud Memorystore,,,{}
BOLTDB,BoltDB,,,{}
BUSUU,Busuu,,,{}
ABOUT_ME,About.Me,,,{}
GOOGLE_CLASSROOM,Google Classroom,,,{}
NVIDIA_SHADOWPLAY,NVIDIA ShadowPlay,,,{}
VMIX,vMix,,,{}
FACEBOOK_LIVE,Facebook Live,,,{}
STREAMYARD,StreamYard,,,{}
XSPLIT,XSplit,,,{}
STREAMLABS,Streamlabs,,,{}
GOOGLE_CLOUD_LOAD_BALANCING,Google Cloud Load Balancing,,,{}
LOAD_IMPACT,Load Impact,,,{}
APACHE_BENCH,Apache Bench,,,{}
GOOGLE_CLOUD_LOGGING,Google Cloud Logging,,,{}
GOOGLE_CLOUD_ACCESS_TRANSPARENCY,Google Cloud Access Transparency,,,{}
NIFT,Nift,,,{}
PERX_PLATFORM,Perx Platform,,,{}
DEALROOM,DealRoom,,,{}
GOOGLE_CLOUD_DATA_CATALOG,Google Cloud Data Catalog,,,{}
CLOUD_TALENT_SOLUTION,Cloud Talent Solution,,,{}
GOOGLE_CLOUD_TPU,Google Cloud TPU,,,{}
QUANTIPHI,Quantiphi,,,{}
TENSORRT,TensorRT,,,{}
GOOGLE_TRANSLATE,Google Translate,,,{}
GOOGLE_CLOUD_AUTOML_TRANSLATION,Google Cloud AutoML Translation,,,{}
FIREEYE_MANAGED_DEFENSE,FireEye Managed Defense,,,{}
GOOGLE_CLOUD_DNS,Google Cloud DNS,,,{}
ENDOLE,Endole,,,{}
INFOSYS_BPM_MASTER_DATA_MANAGEMENT,Infosys BPM - Master Data Management,,,{}
DATAFORM,Dataform,,,{}
FITBIT,Fitbit,,,{}
MICROMENTOR,MicroMentor,,,{}
AUTOPIPE,AutoPIPE,,,{}
REVIT_MEP,Revit MEP,,,{}
APACHE_PULSAR,Apache Pulsar,,,{}
BLINKIST,Blinkist,,,{}
HTTP_3,HTTP/3,,,{}
RECURATE,Recurate,,,{}
SPDY,SPDY,,,{}
APPSFLYER,Appsflyer,,,{}
APPTOPIA,Apptopia,,,{}
REPRO,Repro,,,{}
OLLYDBG,OllyDbg,,,{}
BITBAR_MOBILE_APP_TESTING,Bitbar Mobile App Testing,,,{}
ERICSSON_WALLET_PLATFORM,Ericsson Wallet Platform,,,{}
SQUARE_CASH,Square Cash,,,{}
ADMIN,Admin,,,{}
HEXNODE_MDM,Hexnode MDM,,,{}
ONEBOX,Onebox,,,{}
OPENSCREEN,Openscreen,,,{}
WEB_CEO,Web CEO,,,{}
GOOGLE_PIXEL,Google Pixel,,,{}
GOOGLE_PHONE,Google Phone,,,{}
SAMSUNG_PHONE,Samsung Phone,,,{}
SHOPPING_FEED,Shopping Feed,,,{}
ZILLOW,Zillow,,,{}
VERODIN,Verodin,,,{}
BACKSTORY,Backstory,,,{}
NINOX,Ninox,,,{}
APLOS,Aplos,,,{}
GOOGLE_KEEP,Google Keep,,,{}
GOOGLE_CLOUD_STORAGE,Google Cloud Storage,,,{}
SAFETY_CLOUD,Safety Cloud,,,{}
INSIGHT_VIA,Insight Via,,,{}
IWORK,iWork,,,{}
AFT_FATHOM,AFT FATHOM,,,{}
SAP_DATA_MANAGEMENT,SAP Data Management,,,{}
OPENWEB,OpenWeb,,,{}
KAGGLE,Kaggle,,,{}
CLOUD_ACADEMY,Cloud Academy,,,{}
KHAN_ACADEMY,Khan Academy,,,{}
UNACADEMY,Unacademy,,,{}
SKILLSHARE,Skillshare,,,{}
NEXT_TECH,Next Tech,,,{}
UDEMY,Udemy,,,{}
CHROME_OS,Chrome OS,,,{}
COREOS,CoreOS,,,{}
GENTOO,Gentoo,,,{}
KATAOS,KataOS,,,{}
LINUX_DEVICE_DRIVER,Linux Device Driver,,,{}
POSIX,POSIX,,,{}
SLACKWARE,Slackware,,,{}
UBUNTU_FOR_RASPBERRY_PI,Ubuntu for Raspberry Pi,,,{}
VYOS,Vyos,,,{}
BI_HUB,BI Hub,,,{}
GEOGEBRA,GeoGebra,,,{}
GOOGLE_CLOUD_VIDEO_INTELLIGENCE_API,Google Cloud Video Intelligence API,,,{}
ARTIOSCAD,ArtiosCAD,,,{}
GNU_MAKE,GNU Make,,,{}
GOOGLE_CLOUD_SDK,Google Cloud SDK,,,{}
COCOAPODS,Cocoapods,,,{}
GOOGLE_CAST_SDK,Google Cast SDK,,,{}
ADFORM,Adform,,,{}
GOOGLE_FOR_EDUCATION,Google for Education,,,{}
BRIGHTBYTES,BrightBytes,,,{}
AMAKA,Amaka,,,{}
UNIFIEDPOST,Unifiedpost,,,{}
DIRECTI,Directi,,,{}
NUBANK,Nubank,,,{}
WEALTHFRONT,Wealthfront,,,{}
GOOGLE_CLOUD_HEALTHCARE_API,Google Cloud Healthcare API,,,{}
CHANGE_HEALTHCARE,Change Healthcare,,,{}
GOOGLE_CLOUD_CODE,Google Cloud Code,,,{}
GOLAND,GoLand,,,{}
PYDEV,PyDev,,,{}
WEBSTORM,WebStorm,,,{}
INFRA_APP,Infra App,,,{}
LOGICAL_FORM,Logical Form,,,{}
UX_METRICS,UX Metrics,,,{}
GOOGLE_CLOUD_SHELL,Google Cloud Shell,,,{}
DD_WRT,DD-WRT,,,{}
COMPILERWORKS,CompilerWorks,,,{}
PERFORMANCE_LAB,Performance Lab,,,{}
HUMAN_PRESENCE,Human Presence,,,{}
HSTS,HSTS,,,{}
CLERKY,Clerky,,,{}
GROUPM,GroupM,,,{}
PUBLICIS_SAPIENT,Publicis.Sapient,,,{}
APPHUB,AppHub,,,{}
GOOGLE_FIREBASE_REALTIME_DATABASE,Google Firebase Realtime Database,,,{}
INCOUNTRY,InCountry,,,{}
VIDIQ,vidiq,,,{}
HH_GLOBAL,HH Global,,,{}
NUMETRIC,Numetric,,,{}
WONDERSHARE_UNICONVERTER,wondershare uniconverter,,,{}
CLIPCHAMP,Clipchamp,,,{}
PLAYREADY,PlayReady,,,{}
SHAKA_PLAYER,Shaka Player,,,{}
ANGULARDART,AngularDart,,,{}
BLAZOR,Blazor,,,{}
JAVA_SERVLET,Java Servlet,,,{}
TWENTY_TWENTY,Twenty Twenty,,,{}
REMEMBEAR,RememBear,,,{}
NIUM,NIUM,,,{}
PAYONEER,Payoneer,,,{}
GOOGLE_WALLET,Google Wallet,,,{}
HEARTLAND_PAYMENT,Heartland Payment,,,{}
ANSYS_REDHAWK-SC,Ansys RedHawk-SC,,,{}
PRE_COMMIT,pre-commit,,,{}
GERRIT_CODE_REVIEW,Gerrit Code Review,,,{}
COGNOSHR_PEO,CognosHR PEO,,,{}
AFFINITY_PHOTO,Affinity Photo,,,{}
PICMONKEY,PicMonkey,,,{}
PICSART,Picsart,,,{}
PIXLR,PIXLR,,,{}
GOOGLE_PHOTOS,Google Photos,,,{}
PHOTO_GALLERY,Photo Gallery,,,{}
GOOGLE_NEST,Google Nest,,,{}
BEAMNG,BeamNG,,,{}
ORACLE_PRODUCT_LIFECYCLE_MANAGEMENT_CLOUD,Oracle Product Lifecycle Management Cloud,,,{}
SAP_PLM,SAP PLM,,,{}
CASTOS,Castos,,,{}
LIBSYN,Libsyn,,,{}
HEALTHIFY,Healthify,,,{}
KRYTERION,Kryterion,,,{}
IBM_SPSS_MODELER,IBM SPSS Modeler,,,{}
SPRINGML,SpringML,,,{}
PRINTFUL,Printful,,,{}
IVALUA,Ivalua,,,{}
ONSHAPE,Onshape,,,{}
SOLIDWORKS_3D_CAD,SOLIDWORKS 3D CAD,,,{}
PLANVIEW_CHANGEPOINT,Planview Changepoint,,,{}
SIXRED,SixRed,,,{}
ANSYS_PARAMETRIC_DESIGN_LANGUAGE_APDL,ANSYS Parametric Design Language (APDL),,,{}
DART,Dart,,,{}
HASKELL,Haskell,,,{}
OPENGL_SHADING_LANGUAGE_GLSL,OpenGL Shading Language (GLSL),,,{}
PROGRAMMING_LANGUAGE_ONE_PL_I,Programming Language One (PL/I),,,{}
RUST_PROGRAMMING,Rust Programming,,,{}
SWIFT_PROGRAMMING_LANGUAGE,Swift (Programming Language),,,{}
SYSTEMC,SystemC,,,{}
ECOSYS,EcoSys,,,{}
DELTEK_COSTPOINT,Deltek Costpoint,,,{}
YARDI_VOYAGER,Yardi Voyager,,,{}
PROTOPIE,ProtoPie,,,{}
UX-APP,UX-App,,,{}
GOOGLE_ADSENSE,Google AdSense,,,{}
ADAPTIVE_MEDIA,Adaptive Media,,,{}
ADSPEED,AdSpeed,,,{}
EZOIC,Ezoic,,,{}
OPENX,OpenX,,,{}
SCOPUS,Scopus,,,{}
GOOGLE_CLOUD_SPANNER,Google Cloud Spanner,,,{}
GOOGLE_CLOUD_SQL,Google Cloud SQL,,,{}
REMITLY,Remitly,,,{}
CHROME_REMOTE_DESKTOP,Chrome Remote Desktop,,,{}
LOOPIO,Loopio,,,{}
RFPIO,RFPIO,,,{}
TENABLE_IO,Tenable.io,,,{}
SALES_I,sales-i,,,{}
APOLLO_IO,Apollo.io,,,{}
SEAMLESS_AI,Seamless.ai,,,{}
WINMO,Winmo,,,{}
SALESFORCE_CRM_DASHBOARDS,Salesforce CRM Dashboards,,,{}
VIDDER,Vidder,,,{}
SEAMLESS_SEARCH,Seamless Search,,,{}
YOUTUBE_ADVERTISING,YouTube Advertising,,,{}
SIEMPLIFY_GOOGLE_CLOUD,Siemplify - Google Cloud,,,{}
SENUTO,Senuto,,,{}
SITEBULB,Sitebulb,,,{}
KVM,KVM,,,{}
HALP,Halp,,,{}
ENVOY,envoy,,,{}
APACHE_AVRO,Apache Avro,,,{}
APACHE_ZOOKEEPER,Apache Zookeeper,,,{}
GLASSBOX,Glassbox,,,{}
ROYAL_MAIL,Royal Mail,,,{}
SQUARE_E_COMMERCE_FORMERLY_WEEBLY,Square E-Commerce (formerly Weebly),,,{}
GUMROAD,Gumroad,,,{}
ANSYS_HIGH_FREQUENCY_STRUCTURE_SIMULATOR_HFSS,Ansys High Frequency Structure Simulator (HFSS),,,{}
FLOTHERM,Flotherm,,,{}
OPENSIM,OpenSim,,,{}
ABAQUS_FEA,Abaqus FEA,,,{}
ALTAIR_HYPERCRASH,Altair HyperCrash,,,{}
ANSYS_ICEPAK,ANSYS Icepak,,,{}
ANYLOGIC,AnyLogic,,,{}
HSPICE,HSPICE,,,{}
LS_DYNA,LS Dyna,,,{}
UBERCLOUD,UberCloud,,,{}
CLOUD_CAMPAIGN,Cloud Campaign,,,{}
SOCIAL_ANIMAL,Social Animal,,,{}
SOCIAL_BLADE,Social Blade,,,{}
PLANOLY,Planoly,,,{}
ALIGNABLE,Alignable,,,{}
BUZZFEED,BuzzFeed,,,{}
GOOGLE_FIREBASE_TEST_LAB,Google Firebase Test Lab,,,{}
TEST_IO,test IO,,,{}
TEAM_APP,Team App,,,{}
CLOSURE_COMPILER,Closure Compiler,,,{}
VISUAL_EXPERT,Visual Expert,,,{}
SMART_ANALYTICS,Smart Analytics,,,{}
JMP,JMP,,,{}
EPIDEMIC_SOUND,Epidemic Sound,,,{}
PHOTOMATH,Photomath,,,{}
RECURLY,Recurly,,,{}
PATREON,Patreon,,,{}
ADEPTO_PLATFORM,Adepto platform,,,{}
IGNITION_SCADA,Ignition SCADA,,,{}
SWEATCOIN,Sweatcoin,,,{}
PROJECT44,project44,,,{}
BLOCK_SURVEY,Block Survey,,,{}
SURVEY_ANALYTICS,Survey Analytics,,,{}
BREEZOMETER,BreezoMeter,,,{}
SYNTHESIS_AI,Synthesis AI,,,{}
GOOGLE_CLOUD_TASKS,Google Cloud Tasks,,,{}
KLARA,Klara,,,{}
ACTIFIO,Actifio,,,{}
GOOGLE_CLOUD_AUTOML_NATURAL_LANGUAGE,Google Cloud AutoML Natural Language,,,{}
CODEMIRROR,Codemirror,,,{}
NEOVIM,Neovim,,,{}
WEBDEV,WEBDEV,,,{}
MANDIANT_ADVANTAGE,Mandiant Advantage,,,{}
THREAT_INTELLIGENCE_API_PLATFORM,Threat Intelligence API Platform,,,{}
TCP_TIMECLOCK_PLUS,TCP TimeClock Plus,,,{}
SERVICE_LIFECYCLE_MANAGEMENT,Service Lifecycle Management,,,{}
TRINT,Trint,,,{}
TINYURL,TinyURL,,,{}
CMNTY_PLATFORM,CMNTY Platform,,,{}
OPTIMAL_WORKSHOP,Optimal Workshop,,,{}
INKSCAPE,Inkscape,,,{}
GOOGLE_CLOUD_SOURCE_REPOSITORIES,Google Cloud Source Repositories,,,{}
ADOBE_PREMIERE_RUSH,Adobe Premiere Rush,,,{}
FILMORA,Filmora,,,{}
HITFILM_EXPRESS,HitFilm Express,,,{}
INVIDEO,InVideo,,,{}
KINEMASTER,KineMaster,,,{}
LIGHTWORKS,Lightworks,,,{}
MEDIA_COMPOSER,Media Composer,,,{}
MOVIE_STUDIO,Movie Studio,,,{}
PINNACLE_STUDIO,Pinnacle Studio,,,{}
POWERDIRECTOR,PowerDirector,,,{}
VEGAS_PRO,VEGAS Pro,,,{}
VIDEOPAD,VideoPad,,,{}
WEVIDEO,WeVideo,,,{}
WINDOWS_MOVIE_MAKER,Windows Movie Maker,,,{}
VIEWED,Viewed,,,{}
ADOBE_MEDIA_ENCODER,Adobe Media Encoder,,,{}
KODI,Kodi,,,{}
YOUNOW,YouNow,,,{}
JOBPIXEL,JobPixel,,,{}
GOOGLE_CLOUD_VIRTUAL_NETWORK,Google Cloud Virtual Network,,,{}
EXPRESSVPN,ExpressVPN,,,{}
SURFSHARK,Surfshark,,,{}
GOOGLE_CLOUD_VPN,Google Cloud VPN,,,{}
TUNNELBEAR,TunnelBear,,,{}
DAYDREAM,Daydream,,,{}
CARDBOARD,Cardboard,,,{}
GOOGLE_SCALE,Google Scale,,,{}
PLACER_AI,Placer.ai,,,{}
WEBSCALE,Webscale,,,{}
FATWIRE,Fatwire,,,{}
SUBSTACK,Substack,,,{}
BLOGGER,Blogger,,,{}
WEBFLOW,webflow,,,{}
SEMANTIC_UI,Semantic UI,,,{}
STOREFRONT_UI,Storefront UI,,,{}
GOOGLE_FONTS,Google Fonts,,,{}
GOOGLE_SITES,Google Sites,,,{}
BRANDCAST,Brandcast,,,{}
ADOBE_SPARK,Adobe Spark,,,{}
LIVEEDIT,LiveEdit,,,{}
WEBNODE,Webnode,,,{}
WEB_STORIES,Web Stories,,,{}
GOOGLE_APP_MAKER,Google App Maker,,,{}
DUO_SECURITY,Duo Security,,,{}
DELTA_LAKES,Delta lakes,,,{}
FAISS,FAISS,,,{}
WORD2VEC,Word2Vec,,,{}
OPENAI_GPT_API,OpenAI GPT API,,,{}
ARANGOGRAPH,ArangoGraph,,,{}
AUTOFAC,Autofac,,,{}
CCH_IFIRM,CCH iFirm,,,{}
FINANCIALFORCE_FINANCIAL_MANAGEMENT_ERP,FinancialForce Financial Management ERP,,,{}
FORTNOX,Fortnox,,,{}
TRIPLETEX,Tripletex,,,{}
GITHUB_COPILOT,Github Copilot,,,{}
FACEBOOK_PIXEL,Facebook Pixel,,,{}
HUBSPOT_ANALYTICS,HubSpot Analytics,,,{}
LINKEDIN_INSIGHT_TAG,Linkedin Insight Tag,,,{}
PROGNOZ,Prognoz,,,{}
ADOBE_ANIMATE,Adobe Animate,,,{}
BILL_COM,Bill.com,,,{}
ADP_RECRUITING_MANAGEMENT,ADP Recruiting Management,,,{}
PERSONIO,Personio,,,{}
KOA_FRAMEWORK,Koa framework,,,{}
RED_HAT_JBOSS_ENTERPRISE_APPLICATION_PLATFORM,Red Hat JBoss Enterprise Application Platform,,,{}
MICROSOFT_COGNITIVE_TOOLKIT,Microsoft Cognitive Toolkit,,,{}
TEAMMATE,TeamMate+,,,{}
SOFTRAX,SOFTRAX,,,{}
AKAMAI_BOT_MANAGER,Akamai Bot Manager,,,{}
CLOUDFLARE_BOT_MANAGEMENT,Cloudflare Bot Management,,,{}
ZOHO_BUGTRACKER,Zoho BugTracker,,,{}
SAP_PROCESS_ORCHESTRATION,SAP Process Orchestration,,,{}
COMMENTO,Commento,,,{}
COMPENSATION_TOOL,Compensation Tool,,,{}
DEDUPE,Dedupe,,,{}
NG_BOOTSTRAP,NG-Bootstrap,,,{}
PANDAS_PYTHON,pandas python,,,{}
REACT_BOOTSTRAP,React Bootstrap,,,{}
KNOWLEDGE_CLOUD,Knowledge Cloud,,,{}
AMAZON_ELASTIC_CONTAINER_REGISTRY,Amazon Elastic Container Registry,,,{}
UNPKG,Unpkg,,,{}
MICROSOFT_AZURE_CDN,Microsoft Azure CDN,,,{}
BAMBOO,Bamboo,,,{}
MICROSOFT_TEAM_FOUNDATION_SERVER,Microsoft Team Foundation Server,,,{}
CLM_MATRIX,CLM Matrix,,,{}
CHORUS_AI,Chorus.ai,,,{}
GONG,Gong,,,{}
DRIFT,Drift,,,{}
SAP_LITMOS,SAP Litmos,,,{}
CCH_TAGETIK,CCH Tagetik,,,{}
JEDOX,Jedox,,,{}
PROSYSTEM_FX_SUITE,ProSystem fx Suite,,,{}
CCH_PROSYSTEM_FX_TAX,CCH ProSystem fx Tax,,,{}
CCH_SURETAX,CCH SureTax,,,{}
ELUCIDAT,Elucidat,,,{}
VELOCPQ,VeloCPQ,,,{}
INFOR_CRM,Infor CRM,,,{}
PIPELINE_CRM,Pipeline CRM,,,{}
SEO_CRM,SEO CRM,,,{}
TEAMWORK_CRM,Teamwork CRM,,,{}
WEMINE_WECHAT_CRM_PLATFORM,WeMine (WeChat CRM Platform),,,{}
TALEND_DATA_FABRIC,Talend Data Fabric,,,{}
INFORMATICA_DATA_MANAGEMENT_CLOUD,Informatica Data Management Cloud,,,{}
MAPFORCE,MapForce,,,{}
SAP_AGILE_DATA_PREPARATION,SAP Agile Data Preparation,,,{}
DATEV,Datev,,,{}
ADABAS,Adabas,,,{}
APACHE_IGNITE,Apache Ignite,,,{}
USERGUIDING,UserGuiding,,,{}
SPEKIT,Spekit,,,{}
KENTICO,Kentico,,,{}
RAVENDB,RavenDB,,,{}
FORMS_WORKFLOW,Forms Workflow,,,{}
PDFTRON,PDFTron,,,{}
DOCUMAKER,Documaker,,,{}
EDUADMIN,EduAdmin,,,{}
FRONTLINE_ERP,Frontline ERP,,,{}
MAILUP,MailUp,,,{}
LEMLIST,lemlist,,,{}
SENDINBLUE,SendinBlue,,,{}
EVERBRIDGE_MASS_NOTIFICATION,Everbridge Mass Notification,,,{}
DOCUWARE,DocuWare,,,{}
OPSRAMP,OpsRamp,,,{}
MICROSOFT_OPERATIONS_MANAGEMENT_SUITE,Microsoft Operations Management Suite,,,{}
LEGAL_TRACK,Legal Track,,,{}
TYMETRIX_360,TyMetrix 360,,,{}
NSERVICEBUS,NServiceBus,,,{}
APEX_DATA_LOADER,Apex Data Loader,,,{}
DBAMP,DBAMP,,,{}
SAS_ETL,SAS ETL,,,{}
AVENTRI,Aventri,,,{}
MARITZCX,MaritzCX,,,{}
EFAX,eFax,,,{}
LAUNCHDARKLY,LaunchDarkly,,,{}
IBM_COGNOS_CONTROLLER,IBM Cognos Controller,,,{}
PITCHBOOK,Pitchbook,,,{}
DOSSIER_SYSTEMS,Dossier Systems,,,{}
GOVSPEND,GovSpend,,,{}
UPTODATE,UpToDate,,,{}
MEDIREGS,MediRegs,,,{}
ONE_IDENTITY,One Identity,,,{}
OPENATHENS,OpenAthens,,,{}
SAVIYNT,Saviynt,,,{}
TRUSTBUILDER,TrustBuilder,,,{}
SIRCON,Sircon,,,{}
BASECONE,Basecone,,,{}
MYECLIPSE,MyEclipse,,,{}
DOJO_TOOLKIT,Dojo Toolkit,,,{}
OPEN_LIBERTY,Open Liberty,,,{}
VAADIN,Vaadin,,,{}
CORE_JS,core-js,,,{}
GOOBER,Goober,,,{}
HAMMER_JS,Hammer.js,,,{}
MEDIAELEMENT_JS,Mediaelement.Js,,,{}
WEB_VITALS,web-vitals,,,{}
JSS,JSS,,,{}
AZURE_TABLE_STORAGE,Azure Table Storage,,,{}
EHCACHE,EhCache,,,{}
LEGISWAY_ESSENTIALS,Legisway Essentials,,,{}
LEGAL_INTELLIGENCE,Legal Intelligence,,,{}
HUBSPOT_CHAT,HubSpot Chat,,,{}
COMPLIANCEONE,ComplianceOne,,,{}
ONDECK,OnDeck,,,{}
CLOUDFLARE_DNS,Cloudflare DNS,,,{}
SALESFORCE_MARKETING_CLOUD_ACCOUNT_ENGAGEMENT,Salesforce Marketing Cloud Account Engagement,,,{}
INFOR_MARKETING_RESOURCE_MANAGEMENT,Infor Marketing Resource Management,,,{}
LAERDAL,Laerdal,,,{}
OPEN_GRAPH,Open Graph,,,{}
OPENGROK,OpenGrok,,,{}
NUMBER26,Number26,,,{}
BOOTSTRAP,Bootstrap,,,{}
FIELDA,Fielda,,,{}
CHANNABLE,Channable,,,{}
OPENNLP,openNLP,,,{}
ARCSERVE,Arcserve,,,{}
MARKETO_FORMS,Marketo Forms,,,{}
MASTERA,Mastera,,,{}
ACTIVEDATA,ActiveData,,,{}
AWS_ORGANIZATIONS,AWS Organizations,,,{}
VISUAL_STUDIO_TOOLS_FOR_OFFICE_VSTO,Visual Studio Tools for Office (VSTO),,,{}
CCH_INTELLICONNECT,CCH Intelliconnect,,,{}
CREDITSAFE,Creditsafe,,,{}
SILVERLIGHT,Silverlight,,,{}
UNIVERSAL_DATA_ACCESS_COMPONENTS,Universal Data Access Components,,,{}
CLOUDFLARE_ROCKET_LOADER,Cloudflare Rocket Loader,,,{}
LEGALDESK,Legaldesk,,,{}
REVENUE_IO,Revenue.io,,,{}
PROGET,ProGet,,,{}
WINDOWS_HELLO,Windows Hello,,,{}
CODESTREAM,CodeStream,,,{}
SLEEKNOTE,Sleeknote,,,{}
PRIORITY_BRIDGE,Priority Bridge,,,{}
CYBERARK_PRIVILEGED_ACCESS_MANAGEMENT,CyberArk Privileged Access Management,,,{}
PRODUCTPLAN,ProductPlan,,,{}
FINANCIALFORCE_PSA,FinancialForce PSA,,,{}
ECMASCRIPT_6_ES6,ECMAScript 6 (ES6),,,{}
GROOVY,Groovy,,,{}
TEAMGANTT,TeamGantt,,,{}
RESOURCE_MANAGEMENT_BY_SMARTSHEET,Resource Management by Smartsheet,,,{}
SMART_ADSERVER,Smart AdServer,,,{}
HYPERPROOF,Hyperproof,,,{}
NHIBERNATE,NHibernate,,,{}
AMAZON_APPSTREAM_2_0,Amazon AppStream 2.0,,,{}
VISURE,Visure,,,{}
QVIDIAN,Qvidian,,,{}
XACTLY_INCENT,Xactly Incent,,,{}
SALESINTEL,SalesIntel,,,{}
BOWTIEXP,BowtieXP,,,{}
JIJI_PASSWORD_RESET_SUITE,JiJi Password Reset Suite,,,{}
EMC_NETWORKER,EMC NetWorker,,,{}
VERITAS_BACKUP_EXEC,Veritas Backup Exec,,,{}
MOUSEFLOW,Mouseflow,,,{}
LUCKY_ORANGE,Lucky Orange,,,{}
PINGFEDERATE,PingFederate,,,{}
SNOW_SOFTWARE,Snow Software,,,{}
WEBLATE,Weblate,,,{}
DBUNIT,DBUnit,,,{}
READYAPI_PERFORMANCE,ReadyAPI Performance,,,{}
TRICENTIS_QTEST,Tricentis qTest,,,{}
CHECKSTYLE,Checkstyle,,,{}
SALESFORCE_BILLING,Salesforce Billing,,,{}
ADVANTAGE,Advantage,,,{}
MULTIPUB,Multipub,,,{}
TALENT_NEURON,Talent neuron,,,{}
CLICKTIME,ClickTime,,,{}
PRAGMATIC_INSTITUTE,Pragmatic Institute,,,{}
CROWDIN,Crowdin,,,{}
AREZZO,Arezzo,,,{}
GOTOMEETING,GoToMeeting,,,{}
PRITUNL,Pritunl,,,{}
ENABLON_A_WOLTERS_KLUWER_BUSINESS,ENABLON A WOLTERS KLUWER BUSINESS,,,{}
CLICKMEETING,ClickMeeting,,,{}
GOTOWEBINAR,GoToWebinar,,,{}
JAMS_ENTERPRISE_JOB_SCHEDULER,JAMS Enterprise Job Scheduler,,,{}
DATA_DOG,Data Dog,,,{}
ORCA,Orca,,,{}
OTEL,OTEL,,,{}
APPDY,Appdy,,,{}
ORACLE_DPS,Oracle DPS,,,{}
OCTOPUS,Octopus,,,{}
ADO,ADO,,,{}
BLADELOGIC,BladeLogic,,,{}
DORA_METRICS,DORA metrics,,,{}
PREDICTIVE_ANALYTICS,Predictive Analytics,,,{}
SOFTWARE_DEVELOPMENT_TOOLS,Software Development Tools,,,{}
SDLC,SDLC,,,{}
PRAGMATIC_AGILE_DEVELOPMENT_METHODOLOGIES,Pragmatic Agile Development Methodologies,,,{}
CI_CD,CI/CD,,,{}
TEST_AUTOMATION_FRAMEWORK_AURA,"Test Automation Framework ""AURA""",,,{}
SAAS,SaaS,,,{}
NET_5,.NET 5,,,{}
NET_6,.NET 6,,,{}
SQLSERVER,SQLServer,,,{}
XUNIT,xUnit,,,{}
MASSTRANSIT,MassTransit,,,{}
TEAMMATE_AUDIT_MANAGEMENT_SOFTWARE,TeamMate® Audit Management Software,,,{}
CLOUD_TECHNOLOGIES_TEAMCLOUD,Cloud Technologies (TeamCloud),,,{}
CLOUDX,Cloudx,,,{}
DEVOPS_TOOLS,DevOps tools,,,{}
AZURE_DATA_BRICKS,Azure Data Bricks,,,{}
AZURE_SQL,Azure SQL,,,{}
AZURE_ANALYTICS,Azure Analytics,,,{}
SAST,SAST,,,{}
DAST,DAST,,,{}
OSS,OSS,,,{}
NIST_CSF,NIST CSF,,,{}
SOC1,SOC1,,,{}
SOC2,SOC2,,,{}
UKG_PRO_FORMERLY_ULTIMATE_SOFTWARE_ULTIPRO,UKG Pro (formerly Ultimate Software UltiPro),,,{}
GAINSIGHT,Gainsight,,,{}
INFOR_LN,Infor LN,,,{}
REWARD_GATEWAY,Reward Gateway,,,{}
FLOQAST,FloQast,,,{}
AMS360,AMS360,,,{}
APPLIED_EPIC,Applied Epic,,,{}
EZLYNX,EZLynx,,,{}
SCHOOLZILLA,Schoolzilla,,,{}
DELL_POWEREDGE_RACK_SERVERS,Dell PowerEdge Rack Servers,,,{}
HIGHSPOT,Highspot,,,{}
CLASSLINK,ClassLink,,,{}
COHESITY_DATAPROTECT,Cohesity DataProtect,,,{}
RESPONDENT,Respondent,,,{}
CYTRIC_TRAVEL_AND_EXPENSE,cytric Travel & Expense,,,{}
LARGE_LANGUAGE_MODELS,Large Language Models,,,{}
ETL,ETL,,,{}
SFTP,SFTP,,,{}
JWT,JWT,,,{}
PHPUNIT,PHPUnit,,,{}
MOCKERY,Mockery,,,{}
CODESNIFFER,CodeSniffer,,,{}
ATLASSIAN_PLATFORM,Atlassian platform,,,{}
AAUTH,AAuth,,,{}
RGP,RGP,,,{}
CRONITOR,Cronitor,,,{}
IAC,IaC,,,{}
SNOWFLAKEDB,SnowflakeDB,,,{}
OPENAI_API,OpenAI API,,,{}
JMETER,JMeter,,,{}
AWS_CLOUDWATCH_METRICS,AWS CloudWatch Metrics,,,{}
APACHE_SPARK,Apache Spark,,,{}
JAVA_SCRIPT,Java Script,,,{}
ORACLE_NETSUITE,Oracle NetSuite,,,{}
HUBSTPOT,Hubstpot,,,{}
QUALTRIX,Qualtrix,,,{}
SKAI,Skai,,,{}
HEAP,Heap,,,{}
CLASSKICK,Classkick,,,{}
DREAMBOX,DreamBox,,,{}
LEANIX,LeanIX,,,{}
AND_POSTGRES,and Postgres,,,{}
EVALUATION_SYSTEM,Evaluation system,,,{}
CHAOSSEARCH,ChaosSearch,,,{}
MULESOFT_CLOUDHUB,MuleSoft CloudHub,,,{}
AMAZON_BEDROCK,Amazon Bedrock,,,{}
AMAZON_BEDROCK_GUARDRAILS,Amazon Bedrock Guardrails,,,{}
INSPIRIT_S_LEARNING_HUB,Inspirit's Learning Hub,,,{}
SNAP_AR,Snap AR,,,{}
LOGSTASH,Logstash,,,{}
REST,REST,,,{}
WIDEN_PLATFORM,Widen platform,,,{}
MULESOFT,Mulesoft,,,{}
AWS_EC2,AWS EC2,,,{}
ELK,ELK,,,{}
DOMINKNOW_ONE,dominKnow ONE,,,{}
DOMINKNOW_FLOW,dominKnow Flow,,,{}
AWS_CLI,AWS CLI,,,{}
JAVA_7,Java 7,,,{}
JAVA_8,Java 8,,,{}
MULE_ESB_3,Mule ESB 3,,,{}
MULE_ESB_4,Mule ESB 4,,,{}
SPRING,Spring,,,{}
MULE_CLOUDHUB,Mule Cloudhub,,,{}
CCS,CCS,,,{}
RUBY,Ruby,,,{}
GPT_4_LLM,GPT-4 LLM,,,{}
KALTURA_VIDEO_CLOUD,Kaltura Video Cloud,,,{}
LEARNING_LIFTOFF,Learning Liftoff,,,{}
MATOMO_ANALYTICS,Matomo Analytics,,,{}
ALERTSITE,AlertSite,,,{}
VMWARE_VSAN,VMware vSAN,,,{}
FASTLY,Fastly,,,{}
JSDELIVR,jsDelivr,,,{}
VARNISH_SOFTWARE,Varnish Software,,,{}
GOOGLE_TAG_MANAGER_FOR_WORDPRESS,Google Tag Manager for WordPress,,,{}
HUBSPOT_WORDPRESS_PLUGIN,HubSpot WordPress plugin,,,{}
ADP_VANTAGE_HCM,ADP Vantage HCM,,,{}
GOCO,GoCo,,,{}
ADP_WORKFORCE_NOW,ADP Workforce Now,,,{}
GL_WAND,GL Wand,,,{}
SEGMENT,Segment,,,{}
OBSERVEPOINT,ObservePoint,,,{}
YOAST_SEO,Yoast SEO,,,{}
COMMERCE_SERVER,Commerce Server,,,{}
DESTINY_LIBRARY_MANAGER,Destiny Library Manager,,,{}
GSAP,GSAP,,,{}
FANCYBOX,FancyBox,,,{}
JQUERY_MIGRATE,jQuery Migrate,,,{}
SWIPER_FRAMEWORKS,Swiper Frameworks,,,{}
LOTTIEFILES,LottieFiles,,,{}
SELECT2,Select2,,,{}
PANTHEON,Pantheon,,,{}
RSS,RSS,,,{}
WISTIA,Wistia,,,{}
REMEDYFORCE,Remedyforce,,,{}
SPECRIGHT,Specright,,,{}
TCS_COGNIXTM,TCS CognixTM,,,{}
CLOUD_EXPONENCE,Cloud Exponence,,,{}
OMNIPOS,OmniPOS,,,{}
ADVANCED_CENTRAL_SERVICES,Advanced Central Services,,,{}
TRANSACTION_PAYMENTS,Transaction+ payments,,,{}
MUI,MUI,,,{}
STYLED_COMPONENTS,styled-components,,,{}
STORYBOOK,Storybook,,,{}
SSIS,SSIS,,,{}
SSRS_AZURE_DEV_OPS,SSRS Azure Dev Ops,,,{}
VERIFONE_RTS,Verifone/RTS,,,{}
MAJESCOMASTEK,MajescoMastek,,,{}
CLOUDABILITY,Cloudability,,,{}
IPAAS,iPaaS,,,{}
COLLIBRA,Collibra,,,{}
COLLIBRA_APIS,Collibra APIs,,,{}
ACORD_GRLC_FRAMEWORK,ACORD GRLC framework,,,{}
INDICO,Indico,,,{}
IBM_WATSON,IBM Watson,,,{}
AZURE_KUBERNETES_SERVICES,Azure Kubernetes Services,,,{}
PALO_ALTO_VM_X005F_X0002_SERIES,Palo Alto VM_x005F_x0002_Series,,,{}
SQL_SERVER_DATA_TOOLS_SSDT,SQL Server Data Tools (SSDT),,,{}
PARSE_LY,Parse.ly,,,{}
SMUGMUG,SmugMug,,,{}
ASKNICELY,AskNicely,,,{}
ATANDT,AT&T,,,{}
TWILLO_SENDGRID,Twillo SendGrid,,,{}
EASE,Ease,,,{}
ONELOGIN,OneLogin,,,{}
APPRIVER,Appriver,,,{}
QUIP,Quip,,,{}
CISCO,Cisco,,,{}
PODIO,Podio,,,{}
LITMUS,Litmus,,,{}
TAYLOR_BENNETT,Taylor Bennett,,,{}
CDS,CDS,,,{}
OMEDA,Omeda,,,{}
POSTGRESQL_DATA_WAREHOUSE,PostgreSQL Data Warehouse,,,{}
MICROSOFT_OFFICE_SUITE,Microsoft Office Suite,,,{}
ADOBE_ACROBAT,Adobe Acrobat,,,{}
ACT,ACT,,,{}
LYRIS,Lyris,,,{}
SILVERPOP,Silverpop,,,{}
MAIL_CHIMP,Mail Chimp,,,{}
BOX,Box,,,{}
GOOGLE_KEYWORD_PLANNER,Google Keyword Planner,,,{}
GOOGLE_ADWORDS,Google Adwords,,,{}
KETCH_CONSENT_MANAGEMENT_PLATFORM,Ketch Consent Management Platform,,,{}
KETCH_CUSTOMER_SUCCESS_MANAGER,Ketch Customer Success Manager,,,{}
OMEDA_S_AUDIENCE_BUILDER,Omeda’s Audience Builder,,,{}
ODYSSEY,Odyssey,,,{}
OMEDA_CDP,Omeda CDP,,,{}
PATHFACTORY_S_CONTENT_INTELLIGENCE_PLATFORM,PathFactory’s Content Intelligence platform,,,{}
RAILS,Rails,,,{}
QUESTEX_PORTAL,Questex Portal,,,{}
QUESTEX_DASHBOARD,Questex Dashboard,,,{}
SKETCH,Sketch,,,{}
PSD,PSD,,,{}
GOOGLE_AD_MANAGER_FORMERLY_DFP,Google Ad Manager (Formerly DFP),,,{}
AMAZON_IVS,Amazon IVS,,,{}
FIREBASE_REALTIME,Firebase Realtime,,,{}
FIREBASE_AUTH,Firebase Auth,,,{}
BIRST_DATA_WAREHOUSE,Birst Data Warehouse,,,{}
MARKETO_ADOBE,Marketo (Adobe),,,{}
DRUPAL_8,Drupal 8,,,{}
ACQUIA_LIFT,Acquia Lift,,,{}
KAMELEOON,Kameleoon,,,{}
ODOO_ACCOUNTING,Odoo Accounting,,,{}
SALES_CLOUD_EINSTEIN,Sales Cloud Einstein,,,{}
UMAMI,Umami,,,{}
YOOZ,Yooz,,,{}
NUXT_JS,Nuxt.js,,,{}
HIRERIGHT,HireRight,,,{}
INFOMART,InfoMart,,,{}
EENFORCE,eEnforce,,,{}
STAYTUS,Staytus,,,{}
LIVEHIRE,LiveHire,,,{}
CAPEX_MANAGER,CapEx Manager,,,{}
GUESTCENTRIC,GuestCentric,,,{}
AIRCALL,Aircall,,,{}
3CX,3CX,,,{}
TENCENT_CLOUD,Tencent Cloud,,,{}
SHARETHIS,ShareThis,,,{}
LOCAL_MEASURE,Local Measure,,,{}
FABL,Fabl,,,{}
BITRISE,Bitrise,,,{}
AGILOFT,Agiloft,,,{}
360LEARNING,360Learning,,,{}
APACHE_VELOCITY,Apache Velocity,,,{}
VASION,Vasion,,,{}
RELEVA,Releva,,,{}
SERTIFI,Sertifi,,,{}
SOCIABBLE,Sociabble,,,{}
CULTURE_AMP,Culture Amp,,,{}
PERKBOX,Perkbox,,,{}
SKELLO,Skello,,,{}
STAFF_FILES,Staff Files,,,{}
GETFEEDBACK,GetFeedback,,,{}
IAUDITOR,iAuditor,,,{}
EVENTBRITE,Eventbrite,,,{}
FILEZILLA,filezilla,,,{}
KNOWCROSS,KNOWCROSS,,,{}
NUVOLA,Nuvola,,,{}
GUEST_ENGAGE,Guest Engage,,,{}
KIPSU,Kipsu,,,{}
HOTELOGIX,Hotelogix,,,{}
ORACLE_HOSPITALITY_OPERA_PROPERTY_MANAGEMENT_SYSTEM,Oracle Hospitality OPERA Property Management System,,,{}
ALICE_CONCIERGE,ALICE Concierge,,,{}
PROTEL_PMS,protel PMS,,,{}
RMS_PMS,RMS PMS,,,{}
BOOKASSIST,Bookassist,,,{}
IHOTELIER,iHotelier,,,{}
IDEAS_G3_RMS,IDeaS G3 RMS,,,{}
ALPINE_JS,Alpine.js,,,{}
PROSHOP,ProShop,,,{}
KEYDB,KeyDB,,,{}
KAMAR,KAMAR,,,{}
LOGDNA,LogDNA,,,{}
ENABLEY,enabley,,,{}
TINYCLUES,Tinyclues,,,{}
SAP_MARKETING_CLOUD,SAP Marketing Cloud,,,{}
MIRAKL_INC,Mirakl Inc,,,{}
PRO_FORMS,pro-Forms,,,{}
MICROSOFT_AUTHENTICATOR,Microsoft Authenticator,,,{}
PERQ,PERQ,,,{}
HANDS_MANAGER,H&S Manager,,,{}
AGENCY360,Agency360,,,{}
EMPLOYMENT_HERO,Employment Hero,,,{}
EFFICIENT_HIRE,Efficient Hire,,,{}
SEVENROOMS,SevenRooms,,,{}
SITEMINDER,SiteMinder,,,{}
BACKBLAZE,Backblaze,,,{}
CALDERA_OPENLINUX,Caldera OpenLinux,,,{}
ORIANA,Oriana,,,{}
HOTSOS,HotSOS,,,{}
DELPHI,Delphi,,,{}
LE_VPN,Le VPN,,,{}
MODERNIZR,Modernizr,,,{}
E_GOI,E-goi,,,{}
MAZEN,Mazen,,,{}
VIDEOJS,Videojs,,,{}
HONO,Hono,,,{}
LUANA,Luana,,,{}
MONERIS,Moneris,,,{}
SECUREPAY,SecurePay,,,{}
PAYSPACE,PaySpace,,,{}
ACCESS_PAYROLL_SOFTWARE,Access Payroll Software,,,{}
CLEAR_REVIEW,Clear Review,,,{}
ADOBE_PHOTOSHOP_LIGHTROOM,Adobe Photoshop Lightroom,,,{}
ASSESSFIRST,AssessFirst,,,{}
PRINTIX,Printix,,,{}
VIAVOO,Viavoo,,,{}
F,F#,,,{}
FLOW,Flow,,,{}
RENT_MANAGER,Rent Manager,,,{}
TKINTER,Tkinter,,,{}
BABTECQ,BabtecQ,,,{}
DINEPLAN,DinePlan,,,{}
KITCHEN_CUT,Kitchen CUT,,,{}
OPENTABLE,OpenTable,,,{}
RESDIARY,ResDiary,,,{}
FORMITABLE,Formitable,,,{}
ZENCHEF,Zenchef,,,{}
SILVERWARE,Silverware,,,{}
INFOGENESIS_POS,InfoGenesis POS,,,{}
PRODUCTIV,Productiv,,,{}
GLOBAL_LEARNING_SYSTEMS,Global Learning Systems,,,{}
BARRACUDA_BACKUP,Barracuda Backup,,,{}
LINKTREE,Linktree,,,{}
BABEL,Babel,,,{}
SCANMARKET,Scanmarket,,,{}
PLANSWIFT,PlanSwift,,,{}
TRIPACTIONS,TripActions,,,{}
ABLETO,AbleTo,,,{}
PLAYPLAY,PlayPlay,,,{}
ENGAGEZ,Engagez,,,{}
SAFEBOX,SafeBox,,,{}
CPANEL,CPanel,,,{}
THE_EVENTS_CALENDAR,The Events Calendar,,,{}
UNIFOCUS,UniFocus,,,{}
COREHR,CoreHR,,,{}
HUMANFORCE,Humanforce,,,{}
OCTIME_EXPRESSO,Octime Expresso,,,{}
OPERA_CLOUD_PMS,Opera cloud PMS,,,{}
ORACLE_MATERIAL_CONTROL,Oracle Material Control,,,{}
ORACLE_SIMPHONY_POS,Oracle Simphony POS,,,{}
ARUBA_WIFI,Aruba Wifi,,,{}
AIRANGEL,Airangel,,,{}
VICAS_PASSPORT_SCANNERS,Vicas Passport Scanners,,,{}
DIGITAL_SIGNAGE_TV,Digital Signage TV,,,{}
TELEPHONE_IVR,Telephone IVR,,,{}
ORACLE_OHIP,Oracle OHIP,,,{}
ARUBA_SWITCHES,Aruba Switches,,,{}
ATEN_KVM_SWITCH,Aten KVM Switch,,,{}
MIKROTIK_ROUTER,Mikrotik Router,,,{}
HP_SERVERS,HP Servers,,,{}
WINDOWS_HYPER_V,Windows Hyper-V,,,{}
ARUBA_WLAN_CONTROLLER,Aruba WLAN Controller,,,{}
ARUBA_WIFI_AP,Aruba Wifi AP,,,{}
DATAIKU,Dataiku,,,{}
TALEND,Talend,,,{}
SIMPHONY,Simphony,,,{}
ORACLE_OPERA_CLOUD,Oracle Opera Cloud,,,{}
ORACLE_MICROS,Oracle Micros,,,{}
ORACLE_MATERIALS_CONTROL,Oracle Materials Control,,,{}
CISCO_ROUTERS_SWITCHES,Cisco routers/switches,,,{}
BARRACUDA_CLOUD_BACKUP,Barracuda Cloud Backup,,,{}
VERITAS,Veritas,,,{}
FLACK_API,Flack API,,,{}
S3,S3,,,{}
FLASK,Flask,,,{}
SPLUNK_SPL,Splunk (SPL),,,{}
GIT_CICD,Git (CICD),,,{}
OPERA_CLOUD,Opera Cloud,,,{}
ORACLE_MICROS_POS_SYSTEM,Oracle Micros Pos system,,,{}
BARRACUDA,Barracuda,,,{}
CISCO_SWITCHES,Cisco switches,,,{}
ARUBA_WIFI_CONTROLLER,Aruba wifi Controller,,,{}
AWS_MICROSERVICES,AWS microservices,,,{}
BUCKET_S3,Bucket S3,,,{}
PRIVITAR,Privitar,,,{}
SYBASE_IQ,Sybase IQ,,,{}
SHELL,Shell,,,{}
DAYFORCE,Dayforce,,,{}
GRAPH_API,Graph API,,,{}
ORACLE_LINUX_7_8,Oracle Linux 7.8,,,{}
ORACLE_11GR2,Oracle 11gR2,,,{}
ORACLE_18C,Oracle 18c,,,{}
19C,19c,,,{}
NUTANIX_VMS,Nutanix VMs,,,{}
ORACLE_GOLD,Oracle Gold,,,{}
NUTANIX_CONVERGED_INFRASTRUCTURE,Nutanix Converged Infrastructure,,,{}
AHV,AHV,,,{}
LINUX_REDHAT_6,Linux Redhat 6,,,{}
7,7,,,{}
8SATELLITE_6_7,8Satellite 6.7,,,{}
FOREMAN,Foreman,,,{}
PULP,Pulp,,,{}
CI_CD_CHAIN_GITLAB,CI/CD chain (GITLAB),,,{}
SHELL_BASH,Shell BASH,,,{}
TALENT,Talent,,,{}
SERVICENOW_ITOM,ServiceNow ITOM,,,{}
ITSM,ITSM,,,{}
CHECK_MK,Check_mK,,,{}
SIS,SIS,,,{}
OBM,OBM,,,{}
SCOM,Scom,,,{}
PYTHON_SCRIPTS_BASH_POWERSHELL,Python Scripts Bash PowerShell,,,{}
VBA,VBA,,,{}
UNIX_SOLARIS,Unix (Solaris),,,{}
WINDOWS,Windows,,,{}
ASTORE,Astore,,,{}
CYBERSOURCE_CARDINAL,CyberSource/Cardinal,,,{}
INGENICO,Ingenico,,,{}
FISERV,Fiserv,,,{}
FLOA,Floa,,,{}
P24,P24,,,{}
WECHAT,WeChat,,,{}
GIFTCARDS,Giftcards,,,{}
PROTEL,Protel,,,{}
BIZZON,Bizzon,,,{}
TANIUM,Tanium,,,{}
ATLASSIAN_SUITE_JIRA_AND_CONFLUENCE,Atlassian Suite (JIRA and Confluence),,,{}
AWS_REKOGNITION,AWS Rekognition,,,{}
AWS_SAGEMAKER,AWS Sagemaker,,,{}
RDS,RDS,,,{}
CLOUDWATCH,CloudWatch,,,{}
CLOUDTRAIL,CloudTrail,,,{}
AWS_INSPECTOR,AWS Inspector,,,{}
AWS_EKS_ECS,AWS EKS/ECS,,,{}
TRIDION_DOCS,Tridion Docs,,,{}
RWS_TRANSLATION_MANAGEMENT_TECHNOLOGY,RWS translation management technology,,,{}
PASSOLO,Passolo,,,{}
WORKDAY_FINANCIAL_MANAGEMENT,Workday Financial Management,,,{}
BOOMI_API_MANAGEMENT,Boomi API Management,,,{}
PEPPERDATA,Pepperdata,,,{}
APACHE_ICEBERG,Apache Iceberg,,,{}
PLAYVOX,PlayVox,,,{}
SIMON_DATA,Simon Data,,,{}
AMAZON_SAGEMAKER_GROUND_TRUTH,Amazon SageMaker Ground Truth,,,{}
BRIGHTMETRICS,Brightmetrics,,,{}
CLEAR_SOFTWARE,Clear Software,,,{}
CLOUDHQ,cloudHQ,,,{}
YUI,YUI,,,{}
ITERABLE,Iterable,,,{}
HOTPADS,HotPads,,,{}
SHOWCASE_SALES_APP,Showcase Sales App,,,{}
STRIPE_PAYMENT,Stripe Payment,,,{}
PHOTOMATIX_PRO,Photomatix Pro,,,{}
SITEKIT,SiteKit,,,{}
DOTLOOP,dotloop,,,{}
RESWARE,ResWare,,,{}
ZILLOW_PREMIER_AGENT,Zillow Premier Agent,,,{}
FOLLOW_UP_BOSS,Follow Up Boss,,,{}
HIRED,Hired,,,{}
COMPENSAFE,CompenSafe,,,{}
LAMBDATEST,LambdaTest,,,{}
EIGHTFOLD_AI,Eightfold AI,,,{}
ZOOM_VIDEO_COMMUNICATIONS,Zoom Video Communications,,,{}
MATTERPORT,Matterport,,,{}
HEADLESS_UI,Headless UI,,,{}
BACKEND,Backend,,,{}
FRONTEND,Frontend,,,{}
COMPUTER_VISION,Computer Vision,,,{}
LARGE_LANGUAGE_MODELS_LLMS,Large Language Models (LLMs),,,{}
AI_PIPELINE_TOOLS,AI pipeline tools,,,{}
HUMAN_IN_THE_LOOP_SYSTEMS,Human-in-the-loop systems,,,{}
SEARCH_AI,Search AI,,,{}
CONVERSATIONAL_AI,Conversational AI,,,{}
TABULAR,Tabular,,,{}
HILT,Hilt,,,{}
ANVIL,Anvil,,,{}
JAVA_PLATFORM_MODULE,Java Platform module,,,{}
CLI,CLI,,,{}
IDE_PLUGIN,IDE plugin,,,{}
PNPM,pnpm,,,{}
VITE,Vite,,,{}
VITEST,Vitest,,,{}
GITLAB_CI,GitLab CI,,,{}
AI_ML_MODELS,AI/ML models,,,{}
WEB_TECHNOLOGIES,Web technologies,,,{}
IOS_ANDROID_DEVELOPMENT,iOS/Android development,,,{}
SECURITY_TOOLS,Security tools,,,{}
METAFLOW,Metaflow,,,{}
FASTAPI,FastAPI,,,{}
BERT,BERT,,,{}
SEO_TOOLS,SEO tools,,,{}
FLINK,Flink,,,{}
BACKEND_TECHNOLOGIES,Backend technologies,,,{}
VPC,VPC,,,{}
ECR,ECR,,,{}
ARTLANTIS,Artlantis,,,{}
CODE_EFFECTS,Code Effects,,,{}
CLOUDFLARE_BROWSER_INSIGHTS,Cloudflare Browser Insights,,,{}
SISENSE,Sisense,,,{}
PASSFORT,PassFort,,,{}
ENTERPRISE_LIBRARY,Enterprise Library,,,{}
SPLUNK_CLOUD,Splunk Cloud,,,{}
DREMIO,Dremio,,,{}
OBJECTIVE_PERFORM,Objective Perform,,,{}
PALIGO,Paligo,,,{}
ANGULAR_MATERIAL,Angular Material,,,{}
LUMEN5,Lumen5,,,{}
AUTORABIT,AutoRABIT,,,{}
BRANCHTRACK,BranchTrack,,,{}
CONGA_CPQ,Conga CPQ,,,{}
SALESFORCE_REVENUE_CLOUD,Salesforce Revenue Cloud,,,{}
AGILE_CRM,Agile CRM,,,{}
INFOSERV,InfoServ,,,{}
INITIATIVE_CRM,Initiative CRM,,,{}
MICROSOFT_DYNAMICS_365_SALES,Microsoft Dynamics 365 Sales,,,{}
ADOBE_EXPERIENCE_PLATFORM_IDENTITY_SERVICE,Adobe Experience Platform Identity Service,,,{}
HIGHCHARTS,Highcharts,,,{}
DATAWRAPPER,Datawrapper,,,{}
EAZYBI,eazyBI,,,{}
ELK_STACK,ELK Stack,,,{}
RAWGRAPHS,RAWGraphs,,,{}
DENTRIX,dentrix,,,{}
USERPILOT,Userpilot,,,{}
NETSPARKER_BY_INVICTI,Netsparker by Invicti,,,{}
REVEAL_DATA,Reveal Data,,,{}
LOGI_ANALYTICS,Logi Analytics,,,{}
WEBCENTER,WebCenter,,,{}
ORACLE_WEBCENTER_CONTENT,Oracle WebCenter Content,,,{}
GLOBAL_RELAY,Global Relay,,,{}
AWS_SERVICE_CATALOG,AWS Service Catalog,,,{}
GENSUITE,Gensuite,,,{}
CLOVERDX,CloverDX,,,{}
SPLIT_IO,Split.io,,,{}
IDACITI,idaciti,,,{}
ABS_SUITE,ABS Suite,,,{}
EIKON,Eikon,,,{}
MERGERMARKET,Mergermarket,,,{}
CREDITLENS,CreditLens,,,{}
PALO_ALTO_PA_5220,Palo Alto PA-5220,,,{}
CYBER_CONTROL,Cyber Control,,,{}
MAPBOX_GL_JS,Mapbox GL JS,,,{}
GEOSERVER,GeoServer,,,{}
OPENLAYERS,OpenLayers,,,{}
POSTGIS,postgis,,,{}
IBM_OPENPAGES_WITH_WATSON,IBM OpenPages with Watson,,,{}
OVERTIME_HVAC,Overtime HVAC,,,{}
EXALATE,Exalate,,,{}
OOMNITZA,Oomnitza,,,{}
POSTCSS,PostCSS,,,{}
HIGHSTOCK,Highstock,,,{}
LIT_ELEMENT,lit-element,,,{}
LIT_HTML,lit-html,,,{}
UNDERSCORE_JS,Underscore.js,,,{}
REQUIREJS,RequireJS,,,{}
THREE_JS,Three.js,,,{}
MICROSOFT_LAPTOP,Microsoft Laptop,,,{}
K6,k6,,,{}
RISKORIGINS,RiskOrigins,,,{}
GOANYWHERE_MFT,GoAnywhere MFT,,,{}
COMPUTACENTER,Computacenter,,,{}
AMAZON_MQ,Amazon MQ,,,{}
MODULE_FEDERATION,Module Federation,,,{}
CLOUDPAY,CloudPay,,,{}
CATYLIST,Catylist,,,{}
TEAMUP,TeamUp,,,{}
EMOTION_SH,Emotion.sh,,,{}
RISKFRONTIER,RiskFrontier,,,{}
COMMERCIAL_MORTGAGE_METRICS_CMM,Commercial Mortgage Metrics (CMM),,,{}
ER_ASSIST,ER Assist,,,{}
AWS_AUTO_SCALING,AWS Auto Scaling,,,{}
HCAPTCHA,hCaptcha,,,{}
APACHE_PARQUET,Apache Parquet,,,{}
QUALYS_PM,Qualys PM,,,{}
PENDO,Pendo,,,{}
JULIA_PROGRAMMING_LANGUAGE,Julia (Programming Language),,,{}
PEOPLECODE,PeopleCode,,,{}
REIS_REAL_ESTATE_SOLUTIONS,REIS Real Estate Solutions,,,{}
THE_CRE_SUITE,The CRE Suite,,,{}
THE_REIS_NETWORK,The REIS Network,,,{}
RDC,RDC,,,{}
SAP_HEC_HANA_ENTERPRISE_CLOUD,SAP HEC (HANA Enterprise Cloud),,,{}
ZOOM_ROOMS,Zoom Rooms,,,{}
ALLEGO,Allego,,,{}
BITSIGHT_SECURITY_RATINGS,BitSight Security Ratings,,,{}
IBM_SPECTRUM_PROTECT,IBM Spectrum Protect,,,{}
JIRA_SERVICE_MANAGEMENT,Jira Service Management,,,{}
SNOWMIRROR,SnowMirror,,,{}
GNS3_GRAPHICAL_NETWORK_SIMULATOR_3,GNS3 (Graphical Network Simulator-3),,,{}
FXCOP,FxCop,,,{}
SURVEY_FUNNEL,Survey Funnel,,,{}
CISCO_CATALYST_9300_SERIES,Cisco Catalyst 9300 Series,,,{}
SAP_GUI,SAP GUI,,,{}
SERVICENOW_DEVOPS,ServiceNow DevOps,,,{}
AWS_PRIVATELINK,AWS PrivateLink,,,{}
CORE_FRAMEWORK,Core Framework,,,{}
ACCESSIBE,accessiBe,,,{}
IBM_SPECTRUM_SCALE,IBM Spectrum Scale,,,{}
ANGULAR_15,Angular 15,,,{}
NET_FRAMEWORK,.NET framework,,,{}
STRUT,Strut,,,{}
PRISMA_ACCESS_VPN,Prisma Access VPN,,,{}
GLOBAL_PROTECT,Global Protect,,,{}
FORESCOUT,Forescout,,,{}
ENTRUST,Entrust,,,{}
APPVIEWX,AppViewX,,,{}
NEUSTAR,Neustar,,,{}
ULTRADNS,UltraDNS,,,{}
ANGULAR15,Angular15,,,{}
CSS3,CSS3,,,{}
SASS,SASS,,,{}
ASP_NET_MVC,ASP.NET MVC,,,{}
MQ,MQ,,,{}
REDIS_CACHE,Redis Cache,,,{}
LINQ,LINQ,,,{}
AZURE_REPOS,Azure Repos,,,{}
AZURE_AKS,Azure AKS,,,{}
ECS,ECS,,,{}
EC2,EC2,,,{}
SSM,SSM,,,{}
MWAA,MWAA,,,{}
OBO,OBO,,,{}
TERRAFORMCLOUD,TerraformCloud,,,{}
JUPYTER_NOTEBOOKS,Jupyter notebooks,,,{}
SCCM_AUTOPILOT,SCCM/Autopilot,,,{}
JAMF_PRO,Jamf Pro,,,{}
O365,O365,,,{}
VDI,VDI,,,{}
ZOOM,Zoom,,,{}
TEAMS,Teams,,,{}
AD,AD,,,{}
RESTASSURED,RestAssured,,,{}
NUXTJS_2_VUE2,NuxtJS 2 (Vue2),,,{}
ANGULAR_12_13_14,Angular 12/13/14,,,{}
CYPRESS,Cypress,,,{}
JASMINE,Jasmine,,,{}
LANGCHAIN,LangChain,,,{}
LANGSMITH,LangSmith,,,{}
AIRBREAK,AirBreak,,,{}
ON_PREMISE_SOLUTIONS,On-Premise solutions,,,{}
RWA_API,RWA API,,,{}
SA_CCR_API,SA-CCR API,,,{}
SKIMLINKS,Skimlinks,,,{}
CONSUMER_HUB,Consumer Hub,,,{}
GREENSOCK_ANIMATION_PLATFORM,GreenSock Animation Platform,,,{}
DOW_JONES_RISK_AND_COMPLIANCE,Dow Jones Risk & Compliance,,,{}
APPTWEAK,AppTweak,,,{}
QUALYS_FIM,Qualys FIM,,,{}
LIVE_BLOG,Live Blog,,,{}
RCS_ZETTA,RCS Zetta,,,{}
ADPOINT,Adpoint,,,{}
CORE_AUDIENCE,Core Audience,,,{}
PERMUTIVE,Permutive,,,{}
SAP_DATA_MIGRATION,SAP Data Migration,,,{}
PERCONA_XTRADB_CLUSTER_PXC,Percona XtraDB Cluster (PXC),,,{}
SAP_ENABLE_NOW,SAP Enable Now,,,{}
VMWARE_VCENTER_SERVER,VMware vCenter Server,,,{}
ALGOLIA,Algolia,,,{}
BIGPANDA,BigPanda,,,{}
SAILS_JS,Sails.js,,,{}
ATLAN,Atlan,,,{}
WORDPRESS_VIP,Wordpress VIP,,,{}
AWS_DEVICE_FARM,AWS Device Farm,,,{}
FILEWAVE_UNIFIED_ENDPOINT_MANAGEMENT_SOFTWARE,FileWave Unified Endpoint Management Software,,,{}
REALTOR_COM,Realtor.com,,,{}
GROWTH_TRIBE,Growth Tribe,,,{}
ADVANCED_CUSTOM_FIELDS,Advanced Custom Fields,,,{}
CROWDTANGLE,CrowdTangle,,,{}
WORDPRESS_THEMES,WordPress themes,,,{}
SOLVE_MEDIA,Solve Media,,,{}
PRIORITY_HINTS,Priority Hints,,,{}
SHAREASALE,ShareASale,,,{}
LASTPASS,LastPass,,,{}
ACAST,Acast,,,{}
NEWSWIRE,Newswire,,,{}
FIVESTREET,FiveStreet,,,{}
PERCONA,Percona,,,{}
MEDIARADAR,MediaRadar,,,{}
APPTEGA,Apptega,,,{}
SISTRIX,SISTRIX,,,{}
CRASHPLAN,CrashPlan,,,{}
NEWSWHIP,NewsWhip,,,{}
OPTA,Opta,,,{}
WEEK_PLAN,Week Plan,,,{}
GUTENBERG,Gutenberg,,,{}
DSCOUT,dscout,,,{}
CONNATIX,Connatix,,,{}
JW_PLAYER,JW Player,,,{}
WORDPRESS_MULTISITE,WordPress Multisite,,,{}
GCP_AND_AZURE,GCP and Azure,,,{}
RESTFUL_API,Restful API,,,{}
CORE_API,Core API,,,{}
JEASY_RULES,Jeasy Rules,,,{}
SPRING_BOOT_HAZELCAST_CACHE,Spring Boot Hazelcast Cache,,,{}
AWS_LAMBDA_WITH_TYPESCRIPT_NODE_JS,AWS Lambda with Typescript Node.js,,,{}
AWS_CDK_SERVERLESS_FOR_AUTO_DEPLOY,AWS CDK/Serverless for auto deploy,,,{}
AWS_COGNITO,AWS Cognito,,,{}
AWS_BEANSTALK,AWS Beanstalk,,,{}
ADOBE_REALTIME_EXPERIENCE_PLATFORM,Adobe Realtime Experience Platform,,,{}
INTRANET,Intranet,,,{}
MONDAY,Monday,,,{}
SENTRY,Sentry,,,{}
SALESFORCE_LIGHTNING,Salesforce Lightning,,,{}
SPRING_ELASTICSEARCH,Spring  ElasticSearch,,,{}
GOOGLE_DIALOGFLOW_ES_CHAT_VOICE_BOT,Google Dialogflow ES chat/voice bot,,,{}
BACKSTAGE_FROM_SPOTIFY,Backstage from Spotify,,,{}
RHEL_ON_VIRTUALBOX,RHEL on Virtualbox,,,{}
VAGRANT,Vagrant,,,{}
APACHE_KNOX,Apache Knox,,,{}
EQUILAR,Equilar,,,{}
VICIDIAL,VICIdial,,,{}
WORKDAY_STRATEGIC_SOURCING,Workday Strategic Sourcing,,,{}
QUALIFIED,Qualified,,,{}
COOKIEBOT,Cookiebot,,,{}
SPRING_ROO,Spring Roo,,,{}
STATUSPAGE,Statuspage,,,{}
SAP_ACTIVATE,SAP Activate,,,{}
CHEMDRAW,ChemDraw,,,{}
ORACLE_GLOBAL_TRADE_MANAGEMENT,Oracle Global Trade Management,,,{}
VINDI,Vindi,,,{}
ALTIUM_365,Altium 365,,,{}
TIBCO_STATISTICA,TIBCO Statistica,,,{}
TAXAMO,Taxamo,,,{}
VERTEX_CLOUD,Vertex Cloud,,,{}
DOTMATICS,Dotmatics,,,{}
SMARTLING,Smartling,,,{}
REDUX_FRAMEWORK,Redux Framework,,,{}
ARTIFICIAL_INTELLIGENCE_AI,Artificial Intelligence (AI),,,{}
MACHINE_LEARNING_ML,Machine Learning (ML),,,{}
TAX_REPORTING_ENGINE,Tax Reporting Engine.,,,{}
CORPORATE_SAAS_UI,Corporate SaaS UI,,,{}
CLOUD_SOLUTIONS,Cloud Solutions,,,{}
SAP_S4_HANA,SAP S4 HANA,,,{}
SAP_CENTRAL_FINANCE,SAP Central Finance,,,{}
REGISTER_MAP_SOLUTIONS,Register Map Solutions,,,{}
AI_ML,AI/ML,,,{}
MLOPS,MLOps,,,{}
WIREFRAMING,Wireframing,,,{}
PROTOTYPING,Prototyping,,,{}
DESIGN_SYSTEMS,Design systems,,,{}
CAD,CAD,,,{}
CMS_APPLICATION,CMS Application,,,{}
IAM_POLICIES,IAM policies,,,{}
CLOUD_SECURITY,Cloud security,,,{}
SECURITY_MONITORING_SYSTEMS,Security monitoring systems,,,{}
DLP,DLP,,,{}
CASB,CASB,,,{}
DEFENDER,Defender,,,{}
DIGITAL_GUARDIAN,Digital Guardian,,,{}
NETSCOPE,NetScope,,,{}
MS_DEFENDER,MS-Defender,,,{}
CIS_LEVEL1_LEVEL2,CIS-Level1/Level2,,,{}
C,C,,,{}
SUN_OS,Sun OS,,,{}
DWARF_OS,Dwarf OS,,,{}
COMMON_DATA_MODEL,Common Data Model,,,{}
QT,Qt,,,{}
REST_API,REST API,,,{}
SUN_WORKSTATIONS,Sun workstations,,,{}
PYO3,pyo3,,,{}
EMBEDDED_RUST_HAL,Embedded Rust HAL,,,{}
SVD2RUST,svd2rust,,,{}
T1L_ETHERNET_DRIVER,T1L Ethernet driver,,,{}
HTTPS,HTTPs,,,{}
MQTTS,MQTTs,,,{}
TLS,TLS,,,{}
MAXQ1065,MAXQ1065,,,{}
MBEDTLS,MbedTLS,,,{}
LWIP,lwIP,,,{}
CJSON,cJSON,,,{}
WEB_FORMS,Web forms,,,{}
COVERITY,Coverity,,,{}
PTX_NFC_SPI,PTX NFC SPI,,,{}
SC_ISO_SMART_CARD_AFE,SC-ISO Smart Card AFE,,,{}
MSR_CH1,MSR CH1,,,{}
UART,UART,,,{}
USB,USB,,,{}
RTC,RTC,,,{}
GPIO,GPIO,,,{}
QSPI_XIP_FLASH,QSPI XIP Flash,,,{}
PRECISION_IIO_FIRMWARE_APP,Precision IIO Firmware App,,,{}
PYADI_IIO,PyADI-IIO,,,{}
FPGA_HDL,FPGA HDL,,,{}
ACE_IIO_PLUGIN,ACE IIO Plugin,,,{}
MATLAB_PRECISION_TOOLBOX,MATLAB Precision Toolbox,,,{}
XILINX_ARM_MULTI_PROCESSOR_SOC_A53,Xilinx ARM Multi-processor SoC (A53,,,{}
DWARF_OS_LINUX,Dwarf OS Linux,,,{}
FPGA_PL,FPGA PL,,,{}
XCELIUM,Xcelium,,,{}
SIMVISION,SimVision,,,{}
INDAGO,Indago,,,{}
FONT_BUILDER_TOOL,Font Builder Tool,,,{}
SDK,SDK,,,{}
SDHC,SDHC,,,{}
ZEPHYR_OS,Zephyr OS,,,{}
CAN_BUS,CAN Bus,,,{}
ELB,ELB,,,{}
FARGATE,Fargate,,,{}
CLOUDFRONT,CloudFront,,,{}
AMPLIFY,Amplify,,,{}
ROUTE_53,Route 53,,,{}
COGNITO,Cognito,,,{}
ERP_SYSTEMS,ERP systems,,,{}
TELEMETRY_API,Telemetry API,,,{}
MULTIPLE_LINEAR,Multiple Linear,,,{}
POLYNOMIAL,Polynomial,,,{}
SUPPORT_VECTOR,Support Vector,,,{}
DECISION_TREE,Decision Tree,,,{}
RANDOM_FOREST,Random Forest,,,{}
CATBOOST,CatBoost,,,{}
ORACLE_FINANCIALS,Oracle Financials,,,{}
ORACLE_CLOUD_EPM,Oracle Cloud EPM,,,{}
SALESFORCE_QUERY_LANGUAGE_SAQL,Salesforce Query Language (SAQL),,,{}
PHOTOSHOP,Photoshop,,,{}
ILLUSTRATOR,Illustrator,,,{}
ML_ALGORITHMS,ML algorithms,,,{}
MS_SQL_SERVER,MS SQL Server,,,{}
IVANTI_NEURONS_FOR_ITSM,Ivanti Neurons for ITSM,,,{}
IVANTI_NEURONS_PLATFORM,Ivanti Neurons Platform,,,{}
IVANTI_NEURONS_FOR_DEX,Ivanti Neurons for DEX,,,{}
IVANTI_NEURONS_FOR_DISCOVERY,Ivanti Neurons for Discovery,,,{}
IVANTI_NEURONS_WORKSPACE,Ivanti Neurons Workspace,,,{}
IVANTI_NEURONS_FOR_HEALING,Ivanti Neurons for Healing,,,{}
IVANTI_REMOTE_CONTROL,Ivanti Remote Control,,,{}
IVANTI_NEURONS_FOR_EDGE_INTELLIGENCE,Ivanti Neurons for Edge Intelligence,,,{}
COUPA_PROCURE,Coupa Procure,,,{}
COUPA_INVOICE,Coupa Invoice,,,{}
COUPA_EXPENSE,Coupa Expense,,,{}
COUPA_SOURCE,Coupa Source,,,{}
CRM_ANALYTICS,CRM Analytics,,,{}
VERSATILE_ENTERPRISE,Versatile Enterprise,,,{}
VERSATILE_IMAGING,Versatile Imaging,,,{}
CORPTAX_PROVISION,Corptax Provision,,,{}
GOOGLE_DATAFLOW,Google Dataflow,,,{}
PUB_SUB,Pub/Sub,,,{}
GCS,GCS,,,{}
CUCUMBER,Cucumber,,,{}
BDD,BDD,,,{}
AUTOGENS,Autogens,,,{}
RAGS,RAGs,,,{}
GEN_AI_PLATFORM,Gen AI Platform,,,{}
AI_WORKBENCH,AI Workbench,,,{}
SPACY,SpaCy,,,{}
CNN,CNN,,,{}
HEADLESS_CMS,Headless CMS,,,{}
CANVAS,Canvas,,,{}
BLACKBOARD,Blackboard,,,{}
AWS_IAM,AWS IAM,,,{}
CONTENT_LAKE,Content Lake,,,{}
ES6,ES6,,,{}
REDUX,Redux,,,{}
ENZYME,Enzyme,,,{}
CELERY,Celery,,,{}
GRPC,gRPC,,,{}
ODATA,ODATA,,,{}
API_GATEWAY,API Gateway,,,{}
BIT_BUCKET,Bit Bucket,,,{}
LTI,LTI,,,{}
CEDS,CEDS,,,{}
ED_FI,Ed-Fi,,,{}
ECIDS,ECIDS,,,{}
EDX,edX,,,{}
MOOC,MOOC,,,{}
AGILE_SCRUM,Agile Scrum,,,{}
DAPR,Dapr,,,{}
UML,UML,,,{}
FAST_API,Fast API,,,{}
EKS_CLUSTER,EKS Cluster,,,{}
LXML,lxml,,,{}
GOOGLE_APIS,Google APIs,,,{}
CANVAS_LMS,Canvas LMS,,,{}
AWS_SERVICES,AWS services,,,{}
CLOUD_COMPOSER_AIRFLOW,Cloud Composer (Airflow),,,{}
AFFINIO,Affinio,,,{}
STATSOCIAL,StatSocial,,,{}
STACKADAPT,StackAdapt,,,{}
THERMO_SCIENTIFIC_NAUTILUS_LIMS,Thermo Scientific Nautilus LIMS,,,{}
WESTFAX,WestFax,,,{}
DEFINITIVE_HEALTHCARE,Definitive Healthcare,,,{}
TIPPY_JS,Tippy.js,,,{}
POPPER_JS,Popper.js,,,{}
APPLE_OS_X_MAVERICKS,Apple OS X Mavericks,,,{}
SAMTOOLS,Samtools,,,{}
THINKCELL,Thinkcell,,,{}
MASTERCONTROL_QMS,MasterControl QMS,,,{}
APPNEXUS_MARKETPLACE,AppNexus Marketplace,,,{}
MAILGUN,Mailgun,,,{}
FRESHSERVICE_CRM,FreshService CRM,,,{}
SIGMA,Sigma,,,{}
AMPLITUDE,Amplitude,,,{}
SCALR,Scalr,,,{}
DBT_CLOUD,dbt Cloud,,,{}
DATA_LOADER,Data Loader,,,{}
STORYBLOK,Storyblok,,,{}
PREFECT,Prefect,,,{}
LWC,LWC,,,{}
NIGHTWATCH_JS,Nightwatch.js,,,{}
AWS_ECS,AWS ECS,,,{}
SOAP,SOAP,,,{}
COMPLIANCE_CENTER,Compliance Center,,,{}
EXCHANGE,Exchange,,,{}
ORACLE_DATABASES,Oracle Databases,,,{}
SAP_HANA_2_0,SAP HANA 2.0,,,{}
STRIKINGLY,Strikingly,,,{}
SAP_SYBASE_RAP,SAP Sybase RAP,,,{}
GUPY,Gupy,,,{}
FACEBOOK_WEB_CUSTOM_AUDIENCES,Facebook Web Custom Audiences,,,{}
ORACLE_CLOUD_INFRASTRUCTURE_OCI,Oracle Cloud Infrastructure (OCI),,,{}
ORACLE_INTEGRATION_CLOUD_OIC,Oracle Integration Cloud (OIC),,,{}
ORACLE_COMMERCE_CLOUD_OCC,Oracle Commerce Cloud (OCC),,,{}
ORACLE_SUPPLY_CHAIN_MANAGEMENT_SCM,Oracle Supply Chain Management (SCM),,,{}
ORACLE_SUPPLY_CHAIN_PLANNING_SCP_AND_ORACLE_PRODUCT_DATA_HUB_PDH,Oracle Supply Chain Planning (SCP) and Oracle Product Data Hub (PDH),,,{}
MAYA,Maya,,,{}
DVC,DVC,,,{}
SNOWPLOW_ANALYTICS,Snowplow Analytics,,,{}
AWS_COST_EXPLORER,AWS Cost Explorer,,,{}
CONGA_CLM,Conga CLM,,,{}
CLINICALKEY,ClinicalKey,,,{}
HARVARD_BUSINESS_PUBLISHING_EDUCATION,Harvard Business Publishing Education,,,{}
AMAZON_OPENSEARCH_SERVICE,Amazon OpenSearch Service,,,{}
MULE_ESB,Mule ESB,,,{}
TALEND_DATA_MANAGEMENT_PLATFORM,Talend Data Management Platform,,,{}
CONFIRMIT,Confirmit,,,{}
HELP_SCOUT,Help Scout,,,{}
RIGHTSLINE,rightsline,,,{}
MOBX,MobX,,,{}
NEXIS,Nexis,,,{}
ORACLE_WAREHOUSE_BUILDER,Oracle Warehouse Builder,,,{}
FORMSTACK,Formstack,,,{}
PUBLISHER_DISCOVERY,Publisher Discovery,,,{}
15FIVE,15Five,,,{}
ITHENTICATE,iThenticate,,,{}
LEXISNEXIS_SOCIOECONOMIC_HEALTH_SCORE,LexisNexis Socioeconomic Health Score,,,{}
ARCHIBUS,Archibus,,,{}
MENDELEY,Mendeley,,,{}
WEB_OF_SCIENCE,Web of Science,,,{}
CLARI,Clari,,,{}
SCREENCASTIFY,Screencastify,,,{}
FRESHSERVICE,Freshservice,,,{}
MRW,MRW,,,{}
BROWSERSTACK,Browserstack,,,{}
VIDYARD,Vidyard,,,{}
BIGMARKER,BigMarker,,,{}
ONEBOOK,OneBook,,,{}
VALOHAI,Valohai,,,{}
PINTEREST_CONVERSION_TAG,Pinterest Conversion Tag,,,{}
ENFOCUS_SWITCH,Enfocus Switch,,,{}
WETRANSFER,WeTransfer,,,{}
FLYNN,Flynn,,,{}
TEALIUM_CONSENT_MANAGEMENT,Tealium Consent Management,,,{}
LYTICS,Lytics,,,{}
TEALIUM_AUDIENCESTREAM_CDP,Tealium AudienceStream CDP,,,{}
ILLUMIN_POWERED_BY_ACUITYADS,illumin powered by AcuityAds,,,{}
MICROSOFT_DESKTOP,Microsoft Desktop,,,{}
BIBLIO3,Biblio3,,,{}
LYTHO,Lytho,,,{}
SOLARWINDS_PINGDOM,SolarWinds Pingdom,,,{}
PERFECT_AUDIENCE,Perfect Audience,,,{}
ALFRED,Alfred,,,{}
CAVALLO,Cavallo,,,{}
LAZYSIZES,LazySizes,,,{}
SLICK_FRAMEWORKS,Slick Frameworks,,,{}
MUCK_RACK,Muck Rack,,,{}
CONNECTBOOKS_COM,connectbooks.com,,,{}
SAP_ORDER_TO_CASH_OTC,SAP Order to Cash (OTC),,,{}
COPY5,Copy5,,,{}
LIVEINTENT,LiveIntent,,,{}
SALES_AND_ORDERS,Sales & Orders,,,{}
AMAZON_AMS,Amazon AMS,,,{}
TWENTY_FOURTEEN,Twenty Fourteen,,,{}
IMPACT_COM,impact.com,,,{}
SAGE_MICROPAY,Sage MicrOpay,,,{}
PRACTICE_PRO,Practice Pro,,,{}
CATALIST,Catalist,,,{}
PERL_PROGRAMMING_LANGUAGE,Perl (Programming  Language),,,{}
CLICKUP,ClickUp,,,{}
METRICOOL,metricool,,,{}
HYPEAUDITOR,HypeAuditor,,,{}
AGORAPULSE,AgoraPulse,,,{}
EASYPROMOS,Easypromos,,,{}
UBIQUITI_UNIFI_SWITCH,Ubiquiti UniFi Switch,,,{}
PREVALENT,Prevalent,,,{}
EVE_VIRTUAL,eve virtual,,,{}
IPAGE,iPage,,,{}
WEBSITE_ACCESSIBILITY_SOFTWARE,Website Accessibility Software,,,{}
DAILY_DEALS,Daily Deals,,,{}
MICROSOFT_AZURE_SYNAPSE_ANALYTICS,Microsoft Azure Synapse Analytics,,,{}
TRACKONOMICS,Trackonomics,,,{}
FLIGHTBRIDGE,FlightBridge,,,{}
INFORMATION_BUILDERS_WEBFOCUS,Information Builders WebFOCUS,,,{}
ALLDATA,Alldata,,,{}
ORACLE_SERVER,Oracle Server,,,{}
PROCESSPLAN,ProcessPlan,,,{}
JUMPCLOUD,JumpCloud,,,{}
QUMULO,Qumulo,,,{}
NEWSROOM_AI,Newsroom AI,,,{}
CHARTBEAT,Chartbeat,,,{}
SQL_TOOLBELT,SQL Toolbelt,,,{}
ADAPT_LEARNING,Adapt Learning,,,{}
CELTRA,Celtra,,,{}
NETSERTIVE,Netsertive,,,{}
ZETA_PROGRAMMATIC,Zeta Programmatic,,,{}
FORMERLY_SIZMEK,Formerly Sizmek,,,{}
SUGARCRM,SugarCRM,,,{}
ADOBE_JOURNEY_OPTIMIZER,Adobe Journey Optimizer,,,{}
TUBULAR,Tubular,,,{}
CLOUDINGO,Cloudingo,,,{}
GOOGLE_CHART_TOOLS,Google Chart Tools,,,{}
ENPS,ENPS,,,{}
CENSHARE,Censhare,,,{}
QUANTCAST_PLATFORM,Quantcast Platform,,,{}
ENTELO,Entelo,,,{}
ADOBE_INCOPY,Adobe InCopy,,,{}
SIDETRADE_ACQUISITION,Sidetrade Acquisition,,,{}
4ME,4me,,,{}
SPRING_CLOUD_DATA_FLOW,Spring Cloud Data Flow,,,{}
HELPSPOT,Helpspot,,,{}
HOMECARE_HOMEBASE,Homecare Homebase,,,{}
CISCO_HYPERFLEX,Cisco HyperFlex,,,{}
AMAZON_REKOGNITION,Amazon Rekognition,,,{}
CELIGO,Celigo,,,{}
INTERSYSTEMS_ENSEMBLE,InterSystems Ensemble,,,{}
PDQ_INVENTORY,PDQ Inventory,,,{}
ALLOYUI,AlloyUI,,,{}
PACERMONITOR,PacerMonitor,,,{}
CB_INSIGHTS,CB Insights,,,{}
TAPCLICKS,TapClicks,,,{}
DROPCOUNTR,Dropcountr,,,{}
INC,Inc,,,{}
SIMPLI_FI,Simpli.fi,,,{}
KACE,KACE,,,{}
TRIPLELIFT,TripleLift,,,{}
CANALYZER,CANalyzer,,,{}
INFOGRAM,Infogram,,,{}
INDEX_EXCHANGE,Index Exchange,,,{}
ID5,ID5,,,{}
LINKBY,Linkby,,,{}
FFMPEG,Ffmpeg,,,{}
NINJECT,Ninject,,,{}
PHOTOIMPACT,PhotoImpact,,,{}
PHOTO_MECHANIC,Photo Mechanic,,,{}
CAKEPHP,CakePHP,,,{}
CODEIGNITER,CodeIgniter,,,{}
YII,Yii,,,{}
BEYONDTRUST_ENDPOINT_PRIVILEGE_MANAGEMENT,BeyondTrust Endpoint Privilege Management,,,{}
QUEST_ACTIVE_ROLES,Quest Active Roles,,,{}
AXOSOFT,Axosoft,,,{}
REDMINE,redmine,,,{}
BUILDING_ENGINES,Building Engines,,,{}
KLENTY,Klenty,,,{}
APC,APC,,,{}
SOCIALFLOW,SocialFlow,,,{}
LATER,Later,,,{}
MABL,mabl,,,{}
MICROSOFT_UI_AUTOMATION,Microsoft UI Automation,,,{}
DIGICERT,DigiCert,,,{}
POND5,Pond5,,,{}
STORYBLOCKS,Storyblocks,,,{}
DEVHUB,DevHub,,,{}
GRIDGAIN,GridGain,,,{}
WIDEORBIT,WideOrbit,,,{}
QUALIFIO,Qualifio,,,{}
TRAVELBANK,TravelBank,,,{}
GTREASURY,GTreasury,,,{}
LEADFEEDER,Leadfeeder,,,{}
TOWNNEWS,TownNews,,,{}
LIFERAY,Liferay,,,{}
LIVE_STORY,Live Story,,,{}
IMPORT_IO,Import.io,,,{}
SVELTEKIT,SvelteKit,,,{}
SITE24X7,Site24x7,,,{}
SAP_ANALYTICS,SAP Analytics,,,{}
EMR,EMR,,,{}
SAP_ERP_FINANCIALS,SAP ERP Financials,,,{}
SAP_SD_MM,SAP SD/MM,,,{}
ABAP,ABAP,,,{}
LIGHTNING,Lightning,,,{}
TURNTO,TurnTo,,,{}
MAP_BOX,Map Box,,,{}
MICROSOFT_PROJECT,Microsoft Project,,,{}
ARVATO,Arvato,,,{}
APTOS_MERCH,Aptos Merch,,,{}
SALES_AUDIT,Sales Audit,,,{}
PREDICTSPRING,PredictSpring,,,{}
SOAP_API,SOAP API,,,{}
IBM_STERLING,IBM Sterling,,,{}
AWXAY,Awxay,,,{}
FUSION,Fusion,,,{}
OKTA_SSO,Okta SSO,,,{}
SALESFORCE_COMMERCE_CLOUD_SFCC,Salesforce Commerce Cloud (SFCC),,,{}
MANUAL_FILES,Manual Files,,,{}
JS,JS,,,{}
DESMOS,Desmos,,,{}
SLATWALL,Slatwall,,,{}
IMGIX,imgix,,,{}
TREEHOUSE,Treehouse,,,{}
ICLICKER,iclicker,,,{}
SCRIBD,Scribd,,,{}
ACCORD_VOICE_WMS,Accord Voice WMS,,,{}
LUMAPPS,LumApps,,,{}
AUSTRALIA_POST,Australia Post,,,{}
HR_WORKS,HR Works,,,{}
ATLASSIAN_GLIFFY,Atlassian Gliffy,,,{}
E_COMMERCE_PLATFORMS,E-Commerce platforms,,,{}
YENDO,Yendo,,,{}
PEOPLE_AI,People.ai,,,{}
DIGITAL_AI_TEAMFORGE,Digital.ai TeamForge,,,{}
CALIBERMIND,CaliberMind,,,{}
BSWIFT,bswift,,,{}
ORACLE_ENTERPRISE_MANAGEMENT,Oracle Enterprise Management,,,{}
BILLTRUST,Billtrust,,,{}
STRATEGYZER,Strategyzer,,,{}
SITECORE_EXPERIENCE_EDGE,Sitecore Experience Edge,,,{}
IRON_MOUNTAIN_CONNECT,Iron Mountain Connect,,,{}
MICROSOFT_DEFENDER_FOR_CLOUD,Microsoft Defender for Cloud,,,{}
SKYWORD360,Skyword360,,,{}
VERCEL,Vercel,,,{}
CONTRACTOR_COMPLIANCE,Contractor Compliance,,,{}
KPI,KPI,,,{}
CRM_SERVICE,CRM Service,,,{}
TRAFFICMANAGER_AD,TrafficManager Ad,,,{}
GROWTH_CHANNEL,Growth Channel,,,{}
MICRO_FOCUS_CONTENT_MANAGER,Micro Focus Content Manager,,,{}
KNAPSACK,Knapsack,,,{}
FORPROJECT,forProject,,,{}
ALLSCRIPTS,Allscripts,,,{}
F_SECURE,F-Secure,,,{}
ONBASE,Onbase,,,{}
IBM_CONTENT_MANAGER,IBM Content Manager,,,{}
IBM_FILENET_CONTENT_MANAGER,IBM FileNet Content Manager,,,{}
NETDOCUMENTS,NetDocuments,,,{}
NUXEO_PLATFORM,Nuxeo Platform,,,{}
SPERA,Spera,,,{}
ECUSTOMS,eCustoms,,,{}
OTRS,OTRS,,,{}
PERVIEW_HCM,perview HCM,,,{}
IRISPOWERSCAN,IRISPowerscan,,,{}
ORACLE_RISK_MANAGEMENT_CLOUD,Oracle Risk Management Cloud,,,{}
REACT_FLOW,React Flow,,,{}
JOBSTREET,JobStreet,,,{}
ACCUMULO,Accumulo,,,{}
KNAB,KNAB,,,{}
WINMAGIC,WinMagic,,,{}
SHOPWARE,Shopware,,,{}
OBSERVIUM,Observium,,,{}
BUBBLE,Bubble,,,{}
KOFAX_CAPTURE,Kofax Capture,,,{}
ADAXES,Adaxes,,,{}
PRODUCT_SCHOOL,Product School,,,{}
IMPLAN,IMPLAN,,,{}
ENVISION_DIGITAL,Envision Digital,,,{}
KTOR,Ktor,,,{}
ZACHMAN,Zachman,,,{}
BILLPAY,BillPay,,,{}
UMARKETINGSUITE,uMarketingSuite,,,{}
PRICEFX,Pricefx,,,{}
FERA,Fera,,,{}
MEISTERPLAN,Meisterplan,,,{}
PROOFHUB,ProofHub,,,{}
CITAVI,Citavi,,,{}
CITRIX_GATEWAY,Citrix Gateway,,,{}
VISUALCRON,VisualCron,,,{}
ROUTE4ME,Route4Me,,,{}
MOK,Mok,,,{}
OPTYMYZE,Optymyze,,,{}
POSTNL,PostNL,,,{}
CONTROLPOINT,ControlPoint,,,{}
TELERIK_TEST_STUDIO,Telerik Test Studio,,,{}
TESTFIT,TestFit,,,{}
BITDEFENDER,Bitdefender,,,{}
WORKVIEW,Workview,,,{}
LEAN,Lean,,,{}
FINANCIALFORCE_CERTINIA,FinancialForce (Certinia),,,{}
VIRTUSTREAM,Virtustream,,,{}
ABBY,ABBY,,,{}
KOFAX,Kofax,,,{}
ENVISION,Envision,,,{}
ECM,ECM,,,{}
SCAN_SHARE,Scan Share,,,{}
OPENEDGE,OpenEdge,,,{}
SECURITYSCORECARD,SecurityScorecard,,,{}
ARCHER_GRC,Archer GRC,,,{}
SITECORE_CMS,Sitecore CMS,,,{}
ELOQUA,Eloqua,,,{}
SEISMIC,Seismic,,,{}
GOOGLE_LOOKER_STUDIO,Google Looker Studio,,,{}
FWMONITOR,fwmonitor,,,{}
MICROSOFT_VISUAL_STUDIO_CODE_IDE,Microsoft Visual Studio Code (IDE),,,{}
GIT_GITHUB,GIT/GitHub,,,{}
GOOGLE_CLOUD,Google Cloud,,,{}
BUSINESS_CENTRAL_365,Business Central 365,,,{}
AZURE_SYNAPSE,Azure Synapse,,,{}
RIDER,Rider,,,{}
GREENHOUSE,Greenhouse,,,{}
DREAMDATA_IO,Dreamdata.io,,,{}
OPENVOICE,OpenVoice,,,{}
VOXBONE,Voxbone,,,{}
GOTO_CONTACT,GoTo Contact,,,{}
TALKDESK,Talkdesk,,,{}
THE_TRADE_DESK_DMP,The Trade Desk DMP,,,{}
TRUSTARC,TrustArc,,,{}
MACBOOK_PRO_16,MacBook Pro 16,,,{}
AB_TASTY,AB Tasty,,,{}
LOGMEIN_CENTRAL,LogMeIn Central,,,{}
CLARABRIDGE_ANALYTICS,Clarabridge Analytics,,,{}
GENESYS_CLOUD_FOR_SALESFORCE,Genesys Cloud for Salesforce,,,{}
AOS_JS,AOS JS,,,{}
SVELTE_FRAMEWORKS,Svelte Frameworks,,,{}
QIANKUN,qiankun,,,{}
AZURE_EDGE_NETWORK,Azure Edge Network,,,{}
MIRADORE,Miradore,,,{}
REDDIT_ADS,Reddit Ads,,,{}
AMAZON_APPFLOW,Amazon AppFlow,,,{}
GOTOMYPC,GoToMyPC,,,{}
LOGMEIN_PRO,LogMeIn Pro,,,{}
GOTOASSIST_SEEIT,GoToAssist Seeit,,,{}
REMOTELYANYWHERE,RemotelyAnywhere,,,{}
GOTOROOM,GoToRoom,,,{}
CRI_O,cri-o,,,{}
SALESFORCE_SALES_ENGAGEMENT,Salesforce Sales Engagement,,,{}
SPOTIFY_APP,Spotify App,,,{}
TRUSTRADIUS,TrustRadius,,,{}
GOTOTRAINING,GoToTraining,,,{}
ZOIPER,Zoiper,,,{}
GITKRAKEN,Gitkraken,,,{}
DESK_AI,Desk AI,,,{}
GOTOCONNECT_FORMERLY_JIVE,GoToConnect (formerly Jive),,,{}
MICROSIP,MicroSIP,,,{}
GOTO_WEBINAR,GoTo Webinar,,,{}
NETLIFY,Netlify,,,{}
UX_DESIGN_TOOLS,UX design tools,,,{}
USER_TESTING_TOOLS,user-testing tools,,,{}
PLAYWRIGHT_FOR_AUTOMATION_TESTING,Playwright for automation testing,,,{}
APP_CONFIGURATION,App Configuration,,,{}
BLOB_STORAGE,Blob Storage,,,{}
VIRTUAL_MACHINES,Virtual Machines,,,{}
ELECTRON,Electron,,,{}
TYPESCRIPT_JAVASCRIPT,Typescript/Javascript,,,{}
UNIT_TESTING_WITH_JEST,Unit Testing with Jest,,,{}
GIT_BITBUCKET,Git (Bitbucket),,,{}
VISUAL_CODE,Visual Code,,,{}
AGILE_METHODOLOGY,Agile Methodology,,,{}
AI_MODELS,AI models,,,{}
PUBLIC_APIS,Public APIs,,,{}
GENESYS,Genesys,,,{}
WEB_COMPONENT,Web Component,,,{}
LIT,Lit,,,{}
MICROFRONTEND_MICROSERVICE_ARCHITECTURE,Microfrontend/microservice architecture,,,{}
OAUTH,OAuth,,,{}
SRE,SRE,,,{}
DEVOPS,DevOps,,,{}
INFRAASCODE,infraAsCode,,,{}
CLOUDIFICATION_CONTAINERIZATION_K8S,cloudification/containerization (k8s),,,{}
UNIT_TEST_JEST,Unit test (Jest),,,{}
LOCAL_STORAGE,Local storage,,,{}
WEB_WORKERS,web workers,,,{}
GIT_GITHUB_BITBUCKET,Git (Github/Bitbucket),,,{}
SCSS_SASS_CSS,Scss/sass/css,,,{}
OAUTH2_PKCE,Oauth2 PKCE,,,{}
EMPLOYA,Employa,,,{}
SAP_CLOUD_APPLICATIONS_STUDIO,SAP Cloud Applications Studio,,,{}
MANAGEENGINE_OPMANAGER,ManageEngine OpManager,,,{}
MARVIA,Marvia,,,{}
ACCESS_ERP,Access ERP,,,{}
A3_ERP,A3 ERP,,,{}
ZAPPROVED,Zapproved,,,{}
GAGGLEAMP,GaggleAMP,,,{}
DRAFTSIGHT,DraftSight,,,{}
ZONE_JS,Zone.js,,,{}
BIOVIA_CISPRO,BIOVIA CISPro,,,{}
ISTABILITY_LIMS,iStability LIMS,,,{}
STARLIMS,STARLIMS,,,{}
MUUT,Muut,,,{}
SQBX,Sqbx,,,{}
PROTIME,Protime,,,{}
ZILLIANT,Zilliant,,,{}
ONVENTIS,ONVENTIS,,,{}
PRENDIO,Prendio,,,{}
SAP_SUPPLIER_RELATIONSHIP_MANAGEMENT,SAP Supplier Relationship Management,,,{}
UNIMARKET,Unimarket,,,{}
ONEDESK,OneDesk,,,{}
SAP_QUALITY_MANAGEMENT,SAP Quality Management,,,{}
UNITRENDS_COMPLIANCE_MANAGER,Unitrends Compliance Manager,,,{}
MAXDB,MaxDB,,,{}
I_NEXUS,i-nexus,,,{}
CISCO_CATALYST_9500_SERIES,Cisco Catalyst 9500 Series,,,{}
ULTRAEDIT,ultraedit,,,{}
SAP_TRANSPORTATION_MANAGEMENT,SAP Transportation Management,,,{}
WINDOWS_365,Windows 365,,,{}
SAP_INTEGRATION_SUITE,SAP Integration Suite,,,{}
UBERFLIP,Uberflip,,,{}
MARKETSURGE,MarketSurge,,,{}
BUGCROWD,Bugcrowd,,,{}
CXENSE,cxense,,,{}
CORE_PRACTICE,Core Practice,,,{}
WPBAKERY,WPBakery,,,{}
OMNIGRAFFLE,OmniGraffle,,,{}
POPPULO,Poppulo,,,{}
FILE_EXPENSES,File Expenses,,,{}
HR_ACUITY,HR Acuity,,,{}
AEROSPIKE,Aerospike,,,{}
LEASE_ADMINISTRATION_SOFTWARE,Lease Administration Software,,,{}
SNAPSTREAM,SnapStream,,,{}
TVEYES,TVEyes,,,{}
WORKSCAPE,Workscape,,,{}
CONTENTKING,ContentKing,,,{}
ZEROHEIGHT,Zeroheight,,,{}
MARQETA,Marqeta,,,{}
PIANO_IO,Piano.io,,,{}
CLEARSLIDE,ClearSlide,,,{}
YOAST_SEO_PREMIUM,Yoast SEO Premium,,,{}
OPERATIVE_ONE,Operative.One,,,{}
JAVA_17,Java 17,,,{}
NODE_JS_17,Node.js 17,,,{}
CHEF,Chef,,,{}
SAS,SAS,,,{}
SAS_MACRO,SAS/MACRO,,,{}
REDUX_TOOLKIT,Redux Toolkit,,,{}
MUSTACHE,Mustache,,,{}
SPIFFE_SPIRE,SPIFFE/SPIRE,,,{}
HEXA_AND_IDQL,Hexa & IDQL,,,{}
GOOGLE_ZANZIBAR,Google Zanzibar,,,{}
CERBOS,Cerbos,,,{}
JAVA_21,Java 21,,,{}
SPRING_BOOT_3_X,Spring Boot 3.x,,,{}
OPENREWRITE,OpenRewrite,,,{}
EKS,EKS,,,{}
MSK,MSK,,,{}
EBS,EBS,,,{}
ELASTIC_LOAD_BALANCER,Elastic Load Balancer,,,{}
AUTO_SCALING,Auto Scaling,,,{}
ELASTIC_SEARCH,Elastic Search,,,{}
AMAZON_ECS_ELASTIC_CONTAINER_SERVICE,Amazon ECS (Elastic Container Service),,,{}
AAMAZON_RDS,Aamazon RDS,,,{}
INSTANA,Instana,,,{}
WSO2,WSO2,,,{}
AFFINO,Affino,,,{}
ACADECRAFT,Acadecraft,,,{}
VIRTUOSO,Virtuoso,,,{}
DYNAMICBOOKS_SOFTWARE,DynamicBooks Software,,,{}
ERADANI_CONNECT_APIS,Eradani Connect APIs,,,{}
AMAZON_INTEGRATION_APIS,Amazon Integration APIs,,,{}
ZBRUSH,ZBrush,,,{}
IBM_ENGINEERING_REQUIREMENTS_MANAGEMENT_DOORS_NEXT,IBM Engineering Requirements Management DOORS Next,,,{}
HELIX_ALM,Helix ALM,,,{}
JAZZHR,JazzHR,,,{}
LUMION,Lumion,,,{}
FOREFLIGHT,ForeFlight,,,{}
PARADIGM_IDENTITY_BLOCKCHAIN,Paradigm Identity Blockchain,,,{}
MICRO_FOCUS_DIMENSIONS_CM,Micro Focus Dimensions CM,,,{}
3DEXPERIENCE_CATIA,3DEXPERIENCE CATIA,,,{}
ZNANJA,znanja,,,{}
SAP_CUSTOMER_DATA_PLATFORM,SAP Customer Data Platform,,,{}
ORACLE_ASM,Oracle ASM,,,{}
FOUR_WINDS_INTERACTIVE,Four Winds Interactive,,,{}
FUTURE_SHOP,Future Shop,,,{}
INFOR_EAM,Infor EAM,,,{}
SOTI_MOBICONTROL,SOTI MobiControl,,,{}
GLOBAL_MAPPER,Global Mapper,,,{}
ORACLE_JDEVELOPER,Oracle JDeveloper,,,{}
EVERYTHING_DISC,Everything DiSC,,,{}
KIZEN,Kizen,,,{}
GENSIM,Gensim,,,{}
IPB,IPB,,,{}
ANSYS_ACT,Ansys ACT,,,{}
TARGETLINK,TargetLink,,,{}
MAGICDRAW,MagicDraw,,,{}
ORCAD_CAPTURE,OrCad Capture,,,{}
QUESTASIM,QuestaSim,,,{}
XTEND,Xtend,,,{}
TABILITY,Tability,,,{}
PROPRICER,PROPRICER,,,{}
REMOTEVIEW,RemoteView,,,{}
RADMIN,Radmin,,,{}
RATIONAL_DOORS,Rational DOORS,,,{}
3DVIA,3DVIA,,,{}
VERICUT,VERICUT,,,{}
ALTAIR_SIMSOLID,Altair SimSolid,,,{}
ANSYS_AIM,Ansys AIM,,,{}
ANSYS_DISCOVERY,Ansys Discovery,,,{}
HYPERWORKS,HyperWorks,,,{}
ALTAIR_FEKO,Altair FEKO,,,{}
ALTAIR_HYPERMESH,Altair Hypermesh,,,{}
ALTAIR_HYPERWORKS,Altair HyperWorks,,,{}
ANSYS_WORKBENCH,ANSYS Workbench,,,{}
CAPITAL,Capital,,,{}
INVENTOR_NASTRAN,Inventor Nastran,,,{}
MODELCENTER,ModelCenter,,,{}
MSC_PATRAN,MSC Patran,,,{}
CORUS,Corus,,,{}
CAMEO_SYSTEMS_MODELER,Cameo Systems Modeler,,,{}
TESTSTAND,TestStand,,,{}
UGCS,UgCS,,,{}
OPENSCAP,OpenSCAP,,,{}
APPLOVIN,AppLovin,,,{}
VENNGAGE,Venngage,,,{}
ALAMY,Alamy,,,{}
SAGE_BUSINESS_CLOUD_X3,Sage Business Cloud X3,,,{}
FORTIMANAGER,FortiManager,,,{}
ACTIVECOLLAB,ActiveCollab,,,{}
EASYVISTA_SERVICE_MANAGER,EasyVista Service Manager,,,{}
VIRALSWEEP,ViralSweep,,,{}
TALENTSOFT,Talentsoft,,,{}
HOSTACCESS,HostAccess,,,{}
CROWDCAST,Crowdcast,,,{}
WOCHIT,Wochit,,,{}
HTML_CSS,HTML/CSS,,,{}
MICROSOFT_DYNAMICS_D365,Microsoft Dynamics D365,,,{}
GOOGLE_CLOUD_S_COMPUTE_ENGINE,Google Cloud's Compute Engine,,,{}
CLOUD_STORAGE,Cloud Storage,,,{}
CLOUD_LOAD_BALANCING,Cloud Load Balancing,,,{}
STACKDRIVER_LOGGING,Stackdriver Logging,,,{}
PINECONE,Pinecone,,,{}
WEAVIATE,Weaviate,,,{}
AGILE_TOOLSET,Agile Toolset,,,{}
GOOGLE_WEB_ANALYTICS,Google Web Analytics,,,{}
MICROSOFT_AZURE_SQL,Microsoft Azure SQL,,,{}
SQL_DB_MANAGED_INSTANCE,SQL DB Managed Instance,,,{}
INFORMATICA_IICS,Informatica IICS,,,{}
CISCO_TIDAL_ENTERPRISE_SCHEDULER,Cisco Tidal Enterprise Scheduler,,,{}
CLEO_HARMONY,Cleo Harmony,,,{}
MICROSOFT_ENDPOINT,Microsoft Endpoint,,,{}
INFOR_OS,Infor OS,,,{}
SOFTEXPERT_BPM,SoftExpert BPM,,,{}
SURECLINICAL,SureClinical,,,{}
PRISYM_360,PRISYM 360,,,{}
SAP_DISCLOSURE_MANAGEMENT,SAP Disclosure Management,,,{}
LIFESPHERE,LifeSphere,,,{}
ASSET_MAP,Asset-Map,,,{}
INITIAL_STATE,Initial State,,,{}
SAP_DIGITAL_MANUFACTURING_CLOUD,SAP Digital Manufacturing Cloud,,,{}
CONDECO,Condeco,,,{}
ECOMPLIANCE,eCompliance,,,{}
TEAMS_MANAGER_FOR_MICROSOFT_TEAMS,Teams Manager for Microsoft Teams,,,{}
ATLAS_TI,ATLAS.ti,,,{}
EXEEVO,Exeevo,,,{}
TRACKWISE_DIGITAL,TrackWise Digital,,,{}
SAP_INTELLIGENT_ROBOTIC_PROCESS_AUTOMATION,SAP Intelligent Robotic Process Automation,,,{}
NQUERY,nQuery,,,{}
DNN_PLATFORM,DNN platform,,,{}
TYPO3,TYPO3,,,{}
HAUFE,Haufe,,,{}
FIREFISH,Firefish,,,{}
ATLASSIAN,Atlassian,,,{}
TRAVELTIME_API,TravelTime API,,,{}
BLACKBOX,Blackbox,,,{}
CHARLES,Charles,,,{}
NOPSTATION,NopStation,,,{}
WEEBLY_ECOMMERCE,Weebly eCommerce,,,{}
SANITY_IO,Sanity.io,,,{}
NEUROFLASH,neuroflash,,,{}
WORKMOTION,WorkMotion,,,{}
TEXTKERNEL,Textkernel,,,{}
MAILJET,Mailjet,,,{}
DEBOUNCE,DeBounce,,,{}
QUANTIVE,Quantive,,,{}
SALESVIEWER,SalesViewer,,,{}
N8N,n8n,,,{}
AZURE_VDI,Azure VDI,,,{}
IBM_CONTROLLER,IBM Controller,,,{}
SEARCHPILOT,SearchPilot,,,{}
SERVICEWARE,Serviceware,,,{}
MITATA,Mitata,,,{}
TINYBENCH,TinyBench,,,{}
ARIAKIT,Ariakit,,,{}
REAKIT,Reakit,,,{}
EMOTION,Emotion,,,{}
PANDACSS,PandaCSS,,,{}
CYPRESS_JMETER,Cypress Jmeter,,,{}
DOTNET_CORE,dotNet Core,,,{}
NOMAD,Nomad,,,{}
VAULT,Vault,,,{}
META_S_ROBYN,Meta's Robyn,,,{}
CDC,CDC,,,{}
AWS_MSK,AWS MSK,,,{}
DOCUMENTDB,DocumentDB,,,{}
MONGOSHELL,MongoShell,,,{}
ATLASSIAN_STACK,atlassian stack,,,{}
MSSQL_SERVER,MSSQL Server,,,{}
AI_CONTENT_SUITE_NEUROFLASH,AI Content Suite neuroflash,,,{}
CONTENTFLASH,Contentflash,,,{}
PERFORMANCEFLASH,PerformanceFlash,,,{}
SQL_SERVER_ENTERPRISE_DATA_WAREHOUSE,SQL Server Enterprise Data Warehouse,,,{}
CORITY_GX2,Cority GX2,,,{}
MIRTH_CONNECT,Mirth Connect,,,{}
DELL_BOOMI_PLATFORMS,Dell Boomi platforms,,,{}
AMCHARTS,amCharts,,,{}
PWA_STUDIO,PWA Studio,,,{}
CONTACT_FORM_7,Contact Form 7,,,{}
ANIMA,Anima,,,{}
PRISMA_IO,Prisma.io,,,{}
PYTHON_WEBSOCKET_API,Python WebSocket API,,,{}
TAILWINDCSS,Tailwindcss,,,{}
ERP,ERP,,,{}
BLUE_RIDGE,Blue Ridge,,,{}
MAGENTO,Magento,,,{}
PIXI_JS,Pixi.js,,,{}
ENSCAPE,Enscape,,,{}
ONEOPS,OneOps,,,{}
REACTIVEX,ReactiveX,,,{}
ORACLE_ANALYTICS_CLOUD,Oracle Analytics Cloud,,,{}
SAS_BUSINESS_INTELLIGENCE,SAS Business Intelligence,,,{}
FINSCAN,FinScan,,,{}
API_FORTRESS,API Fortress,,,{}
WSO2_API_MANAGER,WSO2 Api Manager,,,{}
42CRUNCH,42Crunch,,,{}
GOOGLE_APIGEE_SENSE,Google Apigee Sense,,,{}
DYNATRACE_APPLICATION_PERFORMANCE_MONITORING,Dynatrace Application Performance Monitoring,,,{}
ICINGA_MONITORING,Icinga Monitoring,,,{}
JBOSS_APPLICATION_SERVER,JBoss Application Server,,,{}
SAP_WEB_APPLICATION_SERVER,SAP Web Application Server,,,{}
AMO,AMO,,,{}
EXASOL,EXASOL,,,{}
APACHE_PIG,Apache Pig,,,{}
GREENPLUM,Greenplum,,,{}
OMNISCI,OmniSci,,,{}
AZURE_DATA_LAKE_STORE,Azure Data Lake Store,,,{}
DGX_A100,DGX A100,,,{}
AMAZON_LEX,Amazon lex,,,{}
MOZILLA_FIREFOX,Mozilla Firefox,,,{}
INSTABUG,Instabug,,,{}
TRACEMASTER,TraceMaster,,,{}
XPEDITER,Xpediter,,,{}
CHATTER,Chatter,,,{}
JAVA_BUSINESS_PROCESS_MODEL_JBPM,Java Business Process Model (jBPM),,,{}
IBM_BUSINESS_AUTOMATION_WORKFLOW,IBM Business Automation Workflow,,,{}
IBM_OPERATIONAL_DECISION_MANAGER,IBM Operational Decision Manager,,,{}
RED_HAT_PROCESS_AUTOMATION_MANAGER,Red Hat Process Automation Manager,,,{}
ORDER_PRO,Order Pro,,,{}
ACCELLION,Accellion,,,{}
OWNCLOUD,OwnCloud,,,{}
KUBECOST,Kubecost,,,{}
F5_VOLTERRA,F5 Volterra,,,{}
VERIZON_CLOUD,Verizon Cloud,,,{}
AZURE_RESOURCE_GRAPH,Azure Resource Graph,,,{}
AZURE_ARC,Azure Arc,,,{}
RED_HAT_CLOUDFORMS,Red Hat CloudForms,,,{}
AWS_CONTROL_TOWER,AWS Control Tower,,,{}
VERIZON_SECURE_CLOUD_INTERCONNECT,Verizon Secure Cloud Interconnect,,,{}
TRACTION_COMPLETE,Traction Complete,,,{}
CA_ENDEVOR,CA Endevor,,,{}
ADROLL_CMP_SYSTEM,AdRoll CMP System,,,{}
DATABUILD,Databuild,,,{}
NICE_ENGAGE_PLATFORM,NICE Engage Platform,,,{}
GENESYS_CLOUD_CX,Genesys Cloud CX,,,{}
ALVARIA_WORKFORCE,Alvaria Workforce,,,{}
MANAGEIQ,ManageIQ,,,{}
PORTWORX,PortWorx,,,{}
CDNJS,cdnjs,,,{}
CLOUDFLARE_CDN,Cloudflare CDN,,,{}
EDGECAST,EdgeCast,,,{}
EDGIO,Edgio,,,{}
SERVICE_VIRTUALIZATION,Service Virtualization,,,{}
APTTUS_CONTRACT_LIFECYCLE_MANAGEMENT,Apttus Contract Lifecycle Management,,,{}
SAP_ARIBA_CONTRACTS,SAP Ariba Contracts,,,{}
MASTERSTREAM,MasterStream,,,{}
PHOREST,Phorest,,,{}
RESULTS_CRM,Results CRM,,,{}
ADROLL,AdRoll,,,{}
TENX,TenX,,,{}
COOLADATA,CoolaData,,,{}
SAP_CUSTOMER_ACTIVITY_REPOSITORY_CAR,SAP Customer Activity Repository (CAR),,,{}
DIGITAL_CUSTOMER_ENABLEMENT,Digital Customer Enablement,,,{}
TERADATA_CUSTOMER_INTERACTION_MANAGER_CIM,Teradata Customer Interaction Manager (CIM),,,{}
WORTHIX,Worthix,,,{}
CIENA_MANAGE,Ciena Manage,,,{}
CONTROL_AND_PLAN_MCP,Control and Plan (MCP),,,{}
PROTEGRITY_VAULTLESS_TOKENIZATION,Protegrity Vaultless Tokenization,,,{}
IBM_CLOUD_PAK_FOR_DATA,IBM Cloud Pak for Data,,,{}
TERADATA_QUERYGRID,Teradata QueryGrid,,,{}
SYMANTEC_DATA_LOSS_PREVENTION,Symantec Data Loss Prevention,,,{}
ORACLE_BLUEKAI_DATA_MANAGEMENT_PLATFORM,Oracle BlueKai Data Management Platform,,,{}
ORACLE_DATA_MANAGEMENT_PLATFORM_FORMERLY_BLUEKAI,Oracle Data Management Platform (formerly BlueKai),,,{}
SAP_DATA_INTELLIGENCE,SAP Data Intelligence,,,{}
TALEND_CLOUD_DATA_MANAGEMENT,Talend Cloud Data Management,,,{}
TONIC,Tonic,,,{}
TRANSCEND,Transcend,,,{}
TRENDS_AND_TECHNOLOGIES,Trends & Technologies,,,{}
VIZLIB,Vizlib,,,{}
DB2_BIG_SQL,Db2 Big SQL,,,{}
DBARTISAN,DBArtisan,,,{}
IBM_SPUFI,IBM SPUFI,,,{}
TERADATA_PRESTO,Teradata Presto,,,{}
TERADATA_VANTAGE_ADVANCED_SQL_ENGINE,Teradata Vantage Advanced SQL Engine,,,{}
SQL_MONITOR,SQL Monitor,,,{}
WIREDRIVE,Wiredrive,,,{}
UBERAGENT,uberAgent,,,{}
ZERTO,Zerto,,,{}
DRIVE_COMMERCE,Drive Commerce,,,{}
ROUTE_PROTECTION_AND_TRACKING,Route Protection & Tracking,,,{}
AMAZON_WEBSTORE,Amazon Webstore,,,{}
HARVARD_MANAGEMENTOR_HMM,Harvard ManageMentor (HMM),,,{}
EM_CLIENT,eM Client,,,{}
VIRTRU_EMAIL_AND_DATA_ENCRYPTION,Virtru Email and Data Encryption,,,{}
EMAILCAMPAIGNS,EmailCampaigns,,,{}
LEADERSEND,Leadersend,,,{}
POWERMTA,PowerMTA,,,{}
DATAVALIDATION,DataValidation,,,{}
DYNAMIC_SIGNAL,Dynamic Signal,,,{}
WORKVIVO,Workvivo,,,{}
SYMANTEC_ENDPOINT_ENCRYPTION,Symantec Endpoint Encryption,,,{}
SYMANTEC_ENDPOINT_SECURITY,Symantec Endpoint Security,,,{}
CHEQROOM,CHEQROOM,,,{}
QWILT,Qwilt,,,{}
MOINMOIN,MoinMoin,,,{}
PUBNUB,PubNub,,,{}
MEDALLIA,Medallia,,,{}
NICE_SATMETRIX_CXM,NICE Satmetrix CXM,,,{}
OPINIONLAB,OpinionLab,,,{}
QUALTRICS_CUSTOMER_EXPERIENCE,Qualtrics Customer Experience,,,{}
MICROSOFT_365_DEFENDER,Microsoft 365 Defender,,,{}
IBM_TRIRIGA,IBM TRIRIGA,,,{}
CLEARWATER_ANALYTICS,Clearwater Analytics,,,{}
RESTFUL_JSON_API,RESTful JSON API,,,{}
PALO_ALTO_NETWORKS_NEXT_GENERATION_FIREWALL,Palo Alto Networks Next-Generation Firewall,,,{}
VERSA_FLEXVNF,VERSA FlexVNF,,,{}
CISCO_MERAKI_MX84,Cisco Meraki MX84,,,{}
VERIZON_CONNECT,Verizon Connect,,,{}
FLEETWAVE,FleetWave,,,{}
REDZONE,Redzone,,,{}
KAFKAI,Kafkai,,,{}
ORIENTDB,OrientDB,,,{}
VERINT_ENTERPRISE_RECORDING,Verint Enterprise Recording,,,{}
MILVUS,Milvus,,,{}
SUPPORT_COM_CLOUD,Support.com Cloud,,,{}
SUPPORTSYSTEM,SupportSystem,,,{}
INFINIDAT,infinidat,,,{}
EXOSTAR,Exostar,,,{}
FOREFRONT_IDENTITY_MANAGER_FIM,Forefront Identity Manager (FIM),,,{}
VERIZON_PRIVATE_IP,Verizon Private IP,,,{}
INVOCA,Invoca,,,{}
UPFLUENCE,Upfluence,,,{}
RED_HAT_OPENSTACK_PLATFORM,Red Hat OpenStack Platform,,,{}
POSTPAY,Postpay,,,{}
SINGLEVIEW,Singleview,,,{}
VERIZON_THINGSPACE,Verizon ThingSpace,,,{}
KII,Kii,,,{}
AZURE_EVENT_GRID,Azure Event Grid,,,{}
TALEND_CLOUD_DATA_INTEGRATION,Talend Cloud Data Integration,,,{}
DEVICE42,Device42,,,{}
RICHFACES,RichFaces,,,{}
SPOCK_FRAMEWORK,Spock Framework,,,{}
HIGLIGHT_JS,higlight.js,,,{}
JEST_FRAMEWORK,Jest Framework,,,{}
JIBX,JiBX,,,{}
SENCHA_TOUCH,Sencha Touch,,,{}
KANBOARD,Kanboard,,,{}
INFINISPAN,Infinispan,,,{}
ROCKSDB,RocksDB,,,{}
ROSETTA_STONE,Rosetta Stone,,,{}
THRIVE_APPRENTICE,Thrive Apprentice,,,{}
CYBERLIFE,CyberLife,,,{}
AMAZON_ALB,Amazon ALB,,,{}
F5_BIG_IP_LOCAL_TRAFFIC_MANAGER_LTM,F5 BIG-IP Local Traffic Manager (LTM),,,{}
LOADER_IO,Loader.io,,,{}
TRAEFIK,traefik,,,{}
INSOMNIA,Insomnia,,,{}
HUMIO,Humio,,,{}
TIBCO_LOGLOGIC,Tibco Loglogic,,,{}
DEGREED,Degreed,,,{}
UDEMY_BUSINESS,Udemy Business,,,{}
MIDAXO,Midaxo,,,{}
IBM_WATSON_KNOWLEDGE_CATALOG,IBM Watson Knowledge Catalog,,,{}
PROGRESS_MOVEIT,Progress MOVEit,,,{}
DXC_SUPPORT_SERVICES,DXC Support Services,,,{}
PEGA_MARKETING,Pega Marketing,,,{}
MARKETO_ENGAGE,Marketo Engage,,,{}
AZURE_DATA_CATALOG,Azure Data Catalog,,,{}
FILE_AID,File-AID,,,{}
TIBCO_ENTERPRISE_MESSAGE_SERVICE,TIBCO Enterprise Message Service,,,{}
PDF_JS,PDF.js,,,{}
AMAZON_PINPOINT,Amazon Pinpoint,,,{}
HTTPWATCH,HttpWatch,,,{}
POLICYPAK,PolicyPak,,,{}
GOCANVAS_BUSINESS_APPS_AND_SOLUTIONS,GoCanvas Business Apps and Solutions,,,{}
MOTOROLA_MOBILE,Motorola Mobile,,,{}
SAMSUNG_MOBILE,Samsung Mobile,,,{}
MICROSOFT_LANGUAGE_UNDERSTANDING_INTELLIGENT_SERVICE_LUIS,Microsoft Language Understanding Intelligent Service (LUIS),,,{}
MICROSOFT_TEXT_ANALYTICS,Microsoft Text Analytics,,,{}
ERICSSON_NETWORK_MANAGER,Ericsson Network Manager,,,{}
F5_ADVANCED_FIREWALL_MANAGER,F5 Advanced Firewall Manager,,,{}
FORTIANALYZER,FortiAnalyzer,,,{}
EVERNOTE,Evernote,,,{}
CLOUDIAN,Cloudian,,,{}
CLOUDIAN_HYPERSTORE,Cloudian HyperStore,,,{}
MINIO,MinIO,,,{}
SAFETYSYNC,SafetySync,,,{}
RISK_MANAGEMENT_TECHNOLOGIES,Risk Management Technologies,,,{}
SHAREPLEX,SharePlex,,,{}
CHILI_PIPER,Chili Piper,,,{}
UNIX_ORG,UNIX.org,,,{}
CUMULUS_LINUX,Cumulus Linux,,,{}
FREEBSD,FreeBSD,,,{}
JUNOS_OS,Junos OS,,,{}
MICROSOFT_NSLOOKUP,Microsoft Nslookup,,,{}
OPENVMS_VMS,OpenVMS (VMS),,,{}
ORACLE_SOLARIS,Oracle Solaris,,,{}
ROCKY_LINUX,Rocky Linux,,,{}
METASOLV_M6,Metasolv M6,,,{}
DEPLOYSTUDIO,DeployStudio,,,{}
SAS_MANAGEMENT_CONSOLE,SAS Management Console,,,{}
INTELLISIGHT,IntelliSight,,,{}
SCONS,SCons,,,{}
VALGRIND,Valgrind,,,{}
TVSQUARED_ADVANTAGE,TVsquared Advantage,,,{}
DOUBLEVERIFY,DoubleVerify,,,{}
POLITICO_PRO,Politico Pro,,,{}
PL_SQL_DEVELOPER,PL/SQL Developer,,,{}
CODEWARRIOR,CodeWarrior,,,{}
JBUILDER,JBuilder,,,{}
AFFIRMED_NETWORKS,Affirmed Networks,,,{}
WAPPALYZER,Wappalyzer,,,{}
ADOBE_DYNAMIC_TAG_MANAGEMENT_DTM,Adobe Dynamic Tag Management (DTM),,,{}
PAPIRFLY,Papirfly,,,{}
APACHE_ORC,Apache ORC,,,{}
REGRESSION_FORECASTING,Regression Forecasting,,,{}
REPAIR_PILOT,Repair Pilot,,,{}
SCROLLMAGIC,Scrollmagic,,,{}
GSTREAMER,GStreamer,,,{}
MARIONETTE,Marionette,,,{}
WATIR,Watir,,,{}
ETRAK,eTrak,,,{}
RAPYD,Rapyd,,,{}
PAYFIT,PayFit,,,{}
SQLMAP,SQLmap,,,{}
CONVERSANT,Conversant,,,{}
CADENCE,Cadence,,,{}
GENI_SYS,Geni-Sys,,,{}
SERVICENOW_AGILE_DEVELOPMENT,ServiceNow Agile Development,,,{}
CLIST,CLIST,,,{}
PRO_C,Pro C,,,{}
CLOUD_COACH,Cloud Coach,,,{}
UNIPHI,UniPhi,,,{}
JETTY,Jetty,,,{}
TRAPEZE_PASS,Trapeze PASS,,,{}
KINETICA,Kinetica,,,{}
SINGLESTORE_DB,SingleStore DB,,,{}
SPOTLIGHT,Spotlight,,,{}
MICROSOFT_QUICK_ASSIST,Microsoft Quick Assist,,,{}
COVIEW,Coview,,,{}
FIRSTDATA,FirstData,,,{}
BLACKHAWK_NETWORK_INCENTIVES,Blackhawk Network Incentives,,,{}
KRYON,Kryon,,,{}
BLUEJEANS_ROOMS,BlueJeans Rooms,,,{}
DOCAVE,DocAve,,,{}
INSIDESALES,InsideSales,,,{}
CISCO_SD_WAN,Cisco SD-WAN,,,{}
VMWARE_SD_WAN_BY_VELOCLOUD,VMware SD-WAN by VeloCloud,,,{}
PRISMA_SASE,Prisma SASE,,,{}
JUNIPER_NETWORKS_SECURE_ANALYTICS,Juniper Networks Secure Analytics,,,{}
MICRO_FOCUS_ARCSIGHT_ENTERPRISE_SECURITY_MANAGER_ESM,Micro Focus ArcSight Enterprise Security Manager (ESM),,,{}
SPLUNK_SOAR_SECURITY_ORCHESTRATION,Splunk SOAR (Security Orchestration,,,{}
AUTOMATION_AND_RESPONSE,Automation and Response),,,{}
SEARCHIQ,SearchiQ,,,{}
DEDICATED_SERVER,Dedicated Server,,,{}
VMWARE_SERVER,VMware Server,,,{}
VSPHERE_HIGH_AVAILABILITY_HA,vSphere High Availability (HA),,,{}
COREDNS,CoreDNS,,,{}
USERREPLAY,UserReplay,,,{}
VERIZON_INTERSECTION_SAFETY_ANALYTICS,Verizon Intersection Safety Analytics,,,{}
VERIZON_REAL_TIME_RESPONSE_SYSTEM,Verizon Real Time Response System,,,{}
FITNESSE,FitNesse,,,{}
RATIONAL_PERFORMANCE_TESTER,Rational Performance Tester,,,{}
TRUCLIENT,TruClient,,,{}
VERINT_SPEECH_ANALYTICS,Verint Speech Analytics,,,{}
AMAZON_POLLY,Amazon Polly,,,{}
BROADBEAN,Broadbean,,,{}
DOZUKI,Dozuki,,,{}
CPPCHECK,Cppcheck,,,{}
SCULLY,Scully,,,{}
HPE_PROLIANT_DL360,HPE ProLiant DL360,,,{}
ORACLE_SPARC_SERVERS,Oracle SPARC Servers,,,{}
SAP_ARIBA_SOURCING,SAP Ariba Sourcing,,,{}
SPRING_XD,Spring XD,,,{}
ONEBILL,OneBill,,,{}
NEW_RELIC_APM,New Relic APM,,,{}
TEALIUM_IQ_TAG_MANAGEMENT,Tealium iQ Tag Management,,,{}
CENSIA,Censia,,,{}
WEBEX_CONTACT_CENTER,Webex Contact Center,,,{}
CDROUTER,CDRouter,,,{}
FRONTPAGE,FrontPage,,,{}
THREATSTREAM,ThreatStream,,,{}
DRUID,Druid,,,{}
SOLVI,Solvi,,,{}
UNIFIED_THREAT_MANAGEMENT,Unified Threat Management,,,{}
MICROSOFT_DEFENDER_FOR_IDENTITY_MDI,Microsoft Defender for Identity (MDI),,,{}
AWS_CODECOMMIT,AWS CodeCommit,,,{}
BLUEJEANS_MEETINGS,BlueJeans Meetings,,,{}
PREMIER_VIRTUAL,Premier Virtual,,,{}
AZURE_EXPRESSROUTE,Azure ExpressRoute,,,{}
NETMOTION,NetMotion,,,{}
CISCO_CLOUD_SERVICES_ROUTER_1000V,Cisco Cloud Services Router 1000V,,,{}
VERIZON_VNS_ROUTING,Verizon VNS - Routing,,,{}
BLUEWORX,Blueworx,,,{}
COLLECTION_SPACE,Collection Space,,,{}
STRIVR,STRIVR,,,{}
VERSA_NETWORKS_ENTERPRISE_SD_WAN,Versa Networks Enterprise SD-WAN,,,{}
CISCO_INTELLIGENT_WAN_IWAN,Cisco Intelligent WAN (IWAN),,,{}
ROXEN_CMS,Roxen CMS,,,{}
ZURB_FOUNDATION,ZURB Foundation,,,{}
PRIMEREACT,PrimeReact,,,{}
YAHOO_SMALL_BUSINESS,Yahoo Small Business,,,{}
LIGHTTPD,Lighttpd,,,{}
BLUEJEANS_EVENTS,BlueJeans Events,,,{}
BRIGHTTALK,BrightTALK,,,{}
WEBEX_WEBINARS,Webex Webinars,,,{}
PLUTORA,Plutora,,,{}
FORGEROCK_OPENAM,ForgeRock OpenAM,,,{}
WIREFRAME_CC,Wireframe.cc,,,{}
FLEXR,Flexr,,,{}
APACHE_OOZIE,Apache Oozie,,,{}
OAUTH2,OAuth2,,,{}
TERADATA_PARALLEL_TRANSPORTER,Teradata Parallel Transporter,,,{}
JDBC,JDBC,,,{}
GCP_BIGQUERY,GCP BigQuery,,,{}
GCP_AIRFLOW,GCP Airflow,,,{}
QLIK,Qlik,,,{}
KNIME,Knime,,,{}
EXPRESS,Express,,,{}
PALO_ALTO_FIREWALLS,Palo Alto Firewalls,,,{}
JUNIPER_SRX,Juniper SRX,,,{}
PANORAMA,Panorama,,,{}
PRISMA_SD_WAN,PRISMA SD-WAN,,,{}
WILDFIRE,WildFire,,,{}
NEXUS_NX_OS,Nexus NX-OS,,,{}
LLAMAINDEX,LlamaIndex,,,{}
HUGGINGFACE_MODELS,HuggingFace Models,,,{}
PYTHON_FLASK,Python Flask,,,{}
AWS_AZURE_SERVICES,AWS/Azure Services,,,{}
CACHEPOINT,CachePoint,,,{}
NETMAX,Netmax,,,{}
RED_HAT_PRO_CESS_AUTOMATION_MANAGER,Red Hat Pro Cess Automation Manager,,,{}
PRO_CORE,Pro Core,,,{}
ROUTE_PRO_CTION_AND_TRACKING,Route Pro Ction & Tracking,,,{}
SYMANTEC_ENDPOINT_PRO_CTION,Symantec Endpoint Pro Ction,,,{}
CISCO_ADVANCED_MALWARE_PRO_CTION,Cisco Advanced Malware Pro Ction,,,{}
CROWDSTRIKE_FALCON_ENDPOINT_PRO_CTION,CrowdStrike Falcon: Endpoint Pro Ction,,,{}
BUSINESS_PRO_CESS_EXECUTION_LANGUAGE_BPEL,Business Pro Cess Execution Language (BPEL),,,{}
ORACLE_EBS_PRO_CTS,Oracle EBS Pro Cts,,,{}
PRO_CT_DRIVE,Pro Ct Drive,,,{}
PORTFOLIO_PRO_CT_MANAGER_BY_VIRTO,Portfolio Pro Ct Manager by Virto,,,{}
SAP_INTELLIGENT_ROBOTIC_PRO_CESS_AUTOMATION,SAP Intelligent Robotic Pro Cess Automation,,,{}
ROLLWORKS,RollWorks,,,{}
HIGHTOUCH,Hightouch,,,{}
PREACT,Preact,,,{}
PLAID,Plaid,,,{}
BETTERCLOUD,BetterCloud,,,{}
CAPTIVATEIQ,CaptivateIQ,,,{}
V8,V8,,,{}
TIDB,TiDB,,,{}
CILIUM,Cilium,,,{}
ORACLE_BUSINESS_INTELLIGENCE_ENTERPRISE_EDITION,Oracle Business Intelligence Enterprise Edition,,,{}
ORACLE_DATA_MINING,Oracle Data Mining,,,{}
SAP_BUSINESSOBJECTS,SAP BusinessObjects,,,{}
SAS_ANALYTICS,SAS Analytics,,,{}
SAP_MASTER_DATA_GOVERNANCE,SAP Master Data Governance,,,{}
JD_EDWARDS_ENTERPRISEONE,JD Edwards EnterpriseOne,,,{}
INFOR_ENTERPRISE_RESOURCE_PLANNING,Infor Enterprise Resource Planning,,,{}
ASANA,Asana,,,{}
SMARTSHEETS,Smartsheets,,,{}
JDA,JDA,,,{}
JD_EDWARDS,JD Edwards,,,{}
SAP_ELECTRONIC_DATA_INTERCHANGE_EDI,SAP Electronic Data Interchange (EDI),,,{}
VIRTUAL_SITUATION_ROOM,Virtual Situation Room,,,{}
SCALEFUSION_UEM,Scalefusion UEM,,,{}
INFOR_S_WMS_AND_AWS,Infor's WMS and AWS,,,{}
ALIGNED_ELEMENTS,Aligned Elements,,,{}
L7_ESP,L7|ESP,,,{}
FORTIGATE,FortiGate,,,{}
FULLCALENDAR,FullCalendar,,,{}
VONAGE_CONTACT_CENTER_FORMERLY_NEWVOICEMEDIA,Vonage Contact Center (formerly NewVoiceMedia),,,{}
AZURE_FRONT_DOOR,Azure Front Door,,,{}
PERCOLATE,Percolate,,,{}
IO_INTEGRATION,IO Integration,,,{}
VIRTUEMART,VirtueMart,,,{}
SUPERMETRICS,Supermetrics,,,{}
GLYPHICONS,Glyphicons,,,{}
NINJARMM,NinjaRMM,,,{}
VEROGEN,Verogen,,,{}
QIAGEN_CLINICAL_INSIGHT,QIAGEN Clinical Insight,,,{}
DX_FREIGHT,DX Freight,,,{}
SAP_TREASURY_AND_RISK_MANAGEMENT,SAP Treasury and Risk Management,,,{}
SAP_BTP,SAP BTP,,,{}
AZURE_AD,Azure AD,,,{}
CHARTJS,ChartJS,,,{}
POSTGRES,Postgres,,,{}
TOMCAT,Tomcat,,,{}
KARMA,Karma,,,{}
ISTANBUL,Istanbul,,,{}
I18N,i18n,,,{}
ANGULAR_10,Angular 10 +,,,{}
MOQ,Moq,,,{}
SHAREPOINT_ONLINE,SharePoint Online,,,{}
CAMMS,CAMMS,,,{}
BUILDROOT,Buildroot,,,{}
MACOLA,Macola,,,{}
COMPUCAL,CompuCal,,,{}
PHENOM_TXM_PLATFORM,Phenom TXM Platform,,,{}
FORCEPOINT_CASB_CLOUD_ACCESS_SECURITY_BROKER,Forcepoint CASB Cloud Access Security Broker,,,{}
CLOUDLOCK,Cloudlock,,,{}
SUSE_MANAGER,SUSE Manager,,,{}
VMWARE_ARIA_OPERATIONS,VMware Aria Operations,,,{}
SDL_TRADOS_STUDIO,SDL Trados Studio,,,{}
MICRO_FOCUS_DEPLOYMENT_AUTOMATION,Micro Focus Deployment Automation,,,{}
LMS365,LMS365,,,{}
VENDAVO,Vendavo,,,{}
CONFIGURE_ONE,Configure One,,,{}
HPE_SYNERGY,HPE Synergy,,,{}
SAP_GLOBAL_LABEL_MANAGEMENT_SAP_GLM,SAP Global Label Management (SAP GLM),,,{}
FORCEPOINT_DATA_LOSS_PREVENTION_DLP,Forcepoint Data Loss Prevention (DLP),,,{}
SAP_ROAMBI,SAP Roambi,,,{}
SAP_DATA_WAREHOUSE_CLOUD,SAP Data Warehouse Cloud,,,{}
SAP_ABAP_OBJECTS,SAP ABAP Objects,,,{}
ADOBE_DYNAMIC_MEDIA_CLASSIC,Adobe Dynamic Media Classic,,,{}
EPICOR_ISCALA,Epicor iScala,,,{}
BESAFE,BeSafe,,,{}
LINKEDIN_ELEVATE,LinkedIn Elevate,,,{}
SAP_PLANT_MAINTENANCE,SAP Plant Maintenance,,,{}
EXPENSEIT,ExpenseIt,,,{}
SERVICEMAX,ServiceMax,,,{}
ROBOCOPY,Robocopy,,,{}
BATTELLE,Battelle,,,{}
NEOCASE_HR,Neocase HR,,,{}
AZURE_STACK_HCI,Azure Stack HCI,,,{}
APPRENTICE,Apprentice,,,{}
WINLIMS,WinLims,,,{}
SERVICENOW_APP_ENGINE,ServiceNow App Engine,,,{}
ENACT,Enact,,,{}
SMARTSOLVE,SmartSolve,,,{}
KLAXOON,Klaxoon,,,{}
QSTREAM,Qstream,,,{}
WEBIX,Webix,,,{}
WOORANK,Woorank,,,{}
HITACHI_CONTENT_PLATFORM,Hitachi Content Platform,,,{}
STORAGEGRID,StorageGRID,,,{}
CHEMCAD,CHEMCAD,,,{}
AGILEONE,AgileOne,,,{}
EXACT_DATA,Exact Data,,,{}
LEAD_DOCKET,Lead Docket,,,{}
SIMATIC_IT_EBR,SIMATIC IT eBR,,,{}
GRAPHENE,Graphene,,,{}
SOLARWINDS_PATCH_MANAGER,SolarWinds Patch Manager,,,{}
CORTELLIS,Cortellis,,,{}
THOMAS_INTERNATIONAL,Thomas International,,,{}
AERA_TECHNOLOGY,Aera Technology,,,{}
HP_SERVICE_MANAGER,HP Service Manager,,,{}
PINGACCESS,PingAccess,,,{}
IBM_PROCESS_MINING,IBM Process Mining,,,{}
SOLIDWORKS_MANAGE,SolidWorks Manage,,,{}
AGILITY,Agility,,,{}
MICRO_FOCUS_VISUAL_COBOL,Micro Focus Visual COBOL,,,{}
SAP_PPM,SAP PPM,,,{}
PROJECT_INSIGHT,Project Insight,,,{}
ARTICA_PROXY,Artica Proxy,,,{}
VEEVA_VAULT_RIM,Veeva Vault RIM,,,{}
UPSCOPE,Upscope,,,{}
ODASEVA_FOR_SALESFORCE,Odaseva for Salesforce,,,{}
SAP_ASSET_MANAGER,SAP Asset Manager,,,{}
SAP_DOCUMENT_MANAGEMENT_SYSTEM_DMS,SAP Document Management System (DMS),,,{}
CHRONOPOST,Chronopost,,,{}
ELECTRIC_P8,Electric P8,,,{}
SLIDESHARE,SlideShare,,,{}
CIMPLICITY,CIMPLICITY,,,{}
TRACELINK,TraceLink,,,{}
SURVEYPOCKET,SurveyPocket,,,{}
QUESTIONPRO,QuestionPro,,,{}
TO_DO,To-Do,,,{}
LOKALISE,Lokalise,,,{}
CITRIX_WORKSPACE,Citrix Workspace,,,{}
SAP_ACCESS_CONTROL,SAP Access Control,,,{}
STREEM,Streem,,,{}
CONTINUUM,Continuum,,,{}
IOBEYA,iObeya,,,{}
VRED,VRed,,,{}
MCLOUD,Mcloud,,,{}
PALANTIR,Palantir,,,{}
HTTP_HEADER_BASED_AUTHENTICATION,HTTP header-based authentication,,,{}
SCIM,SCIM,,,{}
ADLDS,ADLDS,,,{}
LDAP,LDAP,,,{}
AZURE_MFA,Azure MFA,,,{}
FIDO,FIDO,,,{}
ICINGA_MONITORING_AND_ALERTING_SYSTEM,Icinga Monitoring and Alerting System,,,{}
CYBERARK,CyberArk,,,{}
POWERAPPS,PowerApps,,,{}
IT,IT,,,{}
OT,OT,,,{}
GLOBAL_ELECTRONIC_LAB_NOTEBOOK,Global Electronic Lab Notebook,,,{}
DIGITIZATION,Digitization,,,{}
DATA_WAREHOUSE,data warehouse,,,{}
HIGH_PERFORMANCE_COMPUTING,High-Performance Computing,,,{}
MES,MES,,,{}
LIMS,LIMS,,,{}
FIELDGLASS,Fieldglass,,,{}
JOULES,Joules,,,{}
FIORI,Fiori,,,{}
DEV_UX_INTEGRATION_STUDIO,Dev/UX/Integration Studio,,,{}
LENOVO_LAPTOPS,Lenovo laptops,,,{}
IPHONES,iPhones,,,{}
IPADS,iPads,,,{}
ANDROID_PHONES,Android phones,,,{}
SFTP_SERVER,SFTP server,,,{}
HPE_APPLICATION_LIFECYCLE_MANAGEMENT_SOFTWARE,HPE Application Lifecycle Management Software,,,{}
EMPOWER_APPLICATION,Empower Application,,,{}
EMPOWER_CDS,Empower CDS,,,{}
CHROMATOGRAPHY_INSTRUMENTS,Chromatography instruments,,,{}
NODES,Nodes,,,{}
FILESERVER,Fileserver,,,{}
QUORA_PIXEL,Quora Pixel,,,{}
APPLE_SIGN_IN,Apple Sign-in,,,{}
GUMGUM,GumGum,,,{}
DETECTIFY_DEEP_SCAN,Detectify Deep Scan,,,{}
GRAMMARLY_BUSINESS,Grammarly Business,,,{}
SOURCEGRAPH,Sourcegraph,,,{}
LOADABLE_COMPONENTS,Loadable-Components,,,{}
IMPACT_LEARN,Impact Learn,,,{}
OKTA_IDENTITY_CLOUD,Okta Identity Cloud,,,{}
JAVA_SPRING,Java Spring,,,{}
SWIFT,Swift,,,{}
JETPACK_COMPOSE,Jetpack Compose,,,{}
GOOGLE_DOCS_API,Google Docs API,,,{}
PV_ELITE,PV Elite,,,{}
MATERIALISE_MAGICS,Materialise Magics,,,{}
POLARION_ALM,Polarion ALM,,,{}
SENSORS_DATA,Sensors Data,,,{}
APIGEE_DEVELOPER_PORTAL,Apigee Developer Portal,,,{}
GNU_RADIO,GNU Radio,,,{}
AKAMAI_MPULSE,Akamai mPulse,,,{}
ELASTIC_APM,Elastic APM,,,{}
V_RAY,V-Ray,,,{}
QFM,QFM,,,{}
AVEVA_ASSET_PERFORMANCE_MANAGEMENT,Aveva Asset Performance Management,,,{}
BIZIBLE,Bizible,,,{}
VEEAM_AVAILABILITY_SUITE,Veeam Availability Suite,,,{}
BARCODE_GENERATOR,Barcode Generator,,,{}
TEKLYNX,TEKLYNX,,,{}
BIDTRACER,Bidtracer,,,{}
HVR,HVR,,,{}
SAP_BILLING_AND_REVENUE_INNOVATION_MANAGEMENT,SAP Billing and Revenue Innovation Management,,,{}
BIM_AND_CO,BIM & CO,,,{}
BECKHOFF_TWINCAT,Beckhoff TwinCAT,,,{}
AUTOCAD_MEP,AutoCAD MEP,,,{}
BUILDING_DESIGN_FOR_FABRICATION,building design for fabrication,,,{}
STAAD_PRO,STAAD.Pro,,,{}
UNISIM,Unisim,,,{}
CODE_COMPOSER_STUDIO,Code Composer Studio,,,{}
CPPBUILDER,C++Builder,,,{}
RAD_STUDIO,RAD Studio,,,{}
MOMENTUM_CRM,Momentum CRM,,,{}
BOOKING_AUTOMATION,Booking Automation,,,{}
NASUNI,Nasuni,,,{}
CITRIX_ADC,Citrix ADC,,,{}
VEEAM_ONE,Veeam ONE,,,{}
VMWARE_HCX,VMware HCX,,,{}
RED_HAT_JBOSS,Red Hat jBoss,,,{}
QUINDOS,QUINDOS,,,{}
ONPLAN,OnPlan,,,{}
BLUEBEAM_REVU,Bluebeam Revu,,,{}
3CLOGIC_CLOUD_CALL_CENTER,3CLogic Cloud Call Center,,,{}
CEROS,Ceros,,,{}
ADDTHIS,AddThis,,,{}
NTRACTS,Ntracts,,,{}
AXIS_LEARNING_MANAGEMENT_SYSTEM,Axis Learning Management System,,,{}
TRACORP_LMS,TraCorp LMS,,,{}
SAP_CPQ,SAP CPQ,,,{}
PROS_SMART_CPQ,PROS Smart CPQ,,,{}
QUOTEWERKS,QuoteWerks,,,{}
FLIGHTDECK,Flightdeck,,,{}
SALES_OPTIMIZER,Sales Optimizer,,,{}
PRIVACERA,Privacera,,,{}
PAXATA,Paxata,,,{}
EMC_RECOVERPOINT,EMC RecoverPoint,,,{}
WINDOWS_VIRTUAL_PC,Windows Virtual PC,,,{}
SQL_DELTA,SQL Delta,,,{}
C_TREEAMS_REPLICATION_AGENT,c-treeAMS: Replication Agent,,,{}
SAP_SYBASE,SAP Sybase,,,{}
STUDIO_3T,Studio 3T,,,{}
OPSERA,Opsera,,,{}
MOBILEWARE,MobileWare,,,{}
SILKTIDE,Silktide,,,{}
VMWARE_SITE_RECOVERY_MANAGER,VMware Site Recovery Manager,,,{}
DOCUMINT,Documint,,,{}
ACUNETIX_BY_INVICTI,Acunetix by Invicti,,,{}
WORKAREA,Workarea,,,{}
AUMENT,Aument,,,{}
FIREEYE_ENDPOINT_SECURITY,FireEye Endpoint Security,,,{}
MICROSOFT_DEFENDER_FOR_ENDPOINT,Microsoft Defender for Endpoint,,,{}
ACRONIS_CYBER_PROTECT,Acronis Cyber Protect,,,{}
DEEP_INSTINCT,Deep Instinct,,,{}
ENERGYIP,EnergyIP,,,{}
ENERGYCAP,EnergyCAP,,,{}
MICROSOFT_ENTERPRISE_MOBILITY,Microsoft Enterprise Mobility,,,{}
MICROSOFT_DYNAMICS_AX,Microsoft Dynamics AX,,,{}
SYSPRO,Syspro,,,{}
QLIK_REPLICATE,Qlik Replicate,,,{}
SMOKE_CUSTOMER_INTELLIGENCE,Smoke Customer Intelligence,,,{}
SERVIGISTICS,Servigistics,,,{}
APPZEN,AppZen,,,{}
PALO_ALTO_PA_3220,Palo Alto PA-3220,,,{}
SONICWALL_FIREWALLS,Sonicwall Firewalls,,,{}
SOPHOS_FIREWALL,Sophos Firewall,,,{}
SOS_INVENTORY,SOS Inventory,,,{}
KIVY,Kivy,,,{}
CADWORX,CADWorx,,,{}
SOLID_EDGE,Solid Edge,,,{}
QUALTRAX,Qualtrax,,,{}
ANARK,Anark,,,{}
SPICEWORKS_CLOUD_HELP_DESK,Spiceworks Cloud Help Desk,,,{}
AZURE_STORAGE_EXPLORER,Azure Storage Explorer,,,{}
IPFOLIO,IPfolio,,,{}
RAPIDRESPONSE,RapidResponse,,,{}
CLEAR_SPIDER,Clear Spider,,,{}
HBS_SYSTEMS,HBS Systems,,,{}
MICRIUM_UC_OS,Micrium uC/OS,,,{}
ADOBE_CLIENT_DATA_LAYER,Adobe Client Data Layer,,,{}
AKAMAI_BOOMERANG,Akamai Boomerang,,,{}
ANIME_JS,anime.js,,,{}
VELOCITY_JS,Velocity.js,,,{}
WEKAN,Wekan,,,{}
FAREYE,FarEye,,,{}
MOTADATA_DATA_ANALYTICS_PLATFORM,Motadata Data Analytics Platform,,,{}
VREALIZE_LOG_INSIGHT,vRealize Log Insight,,,{}
IAR_EMBEDDED_WORKBENCH,IAR Embedded Workbench,,,{}
TRACKVIA,TrackVia,,,{}
GRAPHLAB_CREATE_API,GraphLab Create API,,,{}
FACTORY_MES,Factory MES,,,{}
POMSNET,POMSnet,,,{}
SOLUMINA,Solumina,,,{}
DELMIA_APRISO,DELMIA Apriso,,,{}
CAESAR_II,CAESAR II,,,{}
YELO,Yelo,,,{}
EISERVER,EIServer,,,{}
MINDMANAGER,MindManager,,,{}
WEBMIN,Webmin,,,{}
GENYMOTION,Genymotion,,,{}
SAP_MOBILE_PLATFORM,SAP Mobile Platform,,,{}
PALO_ALTO_NETWORKS_GLOBALPROTECT,Palo Alto Networks GlobalProtect,,,{}
SIGMANEST,SigmaNEST,,,{}
SOLARWINDS_NETWORK_MANAGEMENT,Solarwinds Network Management,,,{}
SAFETYSUITE,SafetySuite,,,{}
ABBYY_FINEREADER_SERVER,ABBYY FineReader Server,,,{}
LIBREOFFICE,LibreOffice,,,{}
ASPEN_HYSYS,Aspen HYSYS,,,{}
ASPENONE_PIMS_PLATINUM,aspenONE PIMS Platinum,,,{}
REMOTE_DATA_BACKUP,Remote Data Backup,,,{}
VEEAM_BACKUP_AND_REPLICATION,Veeam Backup & Replication,,,{}
THREADX,ThreadX,,,{}
WINDOWS_CE,Windows CE,,,{}
CONEXIOM,Conexiom,,,{}
WORKSCOPE,Workscope,,,{}
OPENVINO,OpenVino,,,{}
ANSYS_CFX,Ansys CFX,,,{}
MAGMASOFT,Magmasoft,,,{}
MICROWAVE_OFFICE,Microwave Office,,,{}
OPTICSTUDIO,OpticStudio,,,{}
TOPS_PRO,TOPS Pro,,,{}
IBM_RATIONAL_RHAPSODY,IBM Rational Rhapsody,,,{}
HTRI_SOFTWARE,HTRI Software,,,{}
SKM_POWERTOOLS,SKM PowerTools,,,{}
BLISK,Blisk,,,{}
APACHE_IVY,Apache ivy,,,{}
OPENFLOWS_SEWERGEMS,OpenFlows SewerGEMS,,,{}
LABWINDOWS_CVI,LabWindows/CVI,,,{}
QT_CREATOR,Qt Creator,,,{}
SIGFOX,Sigfox,,,{}
GUARD_TOUR_SYSTEM_MANAGEMENT_SOFTWARE,Guard Tour System Management Software,,,{}
SAP_LOGISTICS_EXECUTION_LE,SAP Logistics Execution (LE),,,{}
SALESFORCE_CHANNEL_SALES,Salesforce Channel Sales,,,{}
M_ANALYST,M-Analyst,,,{}
PAYMENTCOLLECT_FOR_QUICKBOOKS,PaymentCollect for QuickBooks,,,{}
SERRALA,Serrala,,,{}
WORLDPAY,Worldpay,,,{}
ANSYS_REDHAWK,Ansys RedHawk,,,{}
ANSYS_SHERLOCK,Ansys Sherlock,,,{}
AUTODESK_EAGLE,Autodesk Eagle,,,{}
KICAD_EDA,KiCad EDA,,,{}
SURROUND_SCM,Surround SCM,,,{}
PENTEST_TOOLS_COM,Pentest-Tools.com,,,{}
VIDEOXPERT,VideoXpert,,,{}
ACCOLADE_PRODUCT_LIFECYCLE_MANAGEMENT,Accolade Product Lifecycle Management,,,{}
VALISPACE,Valispace,,,{}
CAMPAIGN_PARTNER,Campaign Partner,,,{}
ASPEN_MTELL,Aspen Mtell,,,{}
PROS_PRICING,PROS Pricing,,,{}
NTOPOLOGY,nTopology,,,{}
SIMCENTER,Simcenter,,,{}
WINDCHILL_PDMLINK,Windchill PDMLink,,,{}
AUTOCODE,Autocode,,,{}
CYTHON,Cython,,,{}
FORTRAN,Fortran,,,{}
KORNSHELL_KSH,KornShell (KSH),,,{}
PASCAL,Pascal,,,{}
CORA_PPM,Cora PPM,,,{}
COPPER_PROJECT,Copper Project,,,{}
BEYOND_SOFTWARE,Beyond Software,,,{}
MICROSOFT_DYNAMICS_SL,Microsoft Dynamics SL,,,{}
MARVELAPP,Marvelapp,,,{}
SYNPLIFY,Synplify,,,{}
APACHE_GIRAPH,Apache Giraph,,,{}
CLOUDERA_DATA_SCIENCE,Cloudera Data Science,,,{}
HISTORIAN,Historian,,,{}
MOBAXTERM,MobaXTerm,,,{}
VULNDB,VulnDB,,,{}
IRVISION,iRVision,,,{}
KLYCK_IO,Klyck.io,,,{}
SAFEBUS,SafeBus,,,{}
ATTACKTREE,AttackTree,,,{}
BING_WEBMASTER_TOOLS,Bing Webmaster Tools,,,{}
VMWARE_VIRTUAL_INFRASTRUCTURE,VMware Virtual Infrastructure,,,{}
MODELSIM,ModelSim,,,{}
SIMSCAPE,Simscape,,,{}
SMARTPLANT_INSTRUMENTATION,SmartPlant Instrumentation,,,{}
ANSYS_MECHANICAL_PREMIUM,ANSYS Mechanical Premium,,,{}
ANSYS_NCODE_DESIGNLIFE,Ansys nCode DesignLife,,,{}
CAMWORKS,CAMWorks,,,{}
CREO_SIMULATE,Creo Simulate,,,{}
GIBBSCAM,GibbsCAM,,,{}
MATHCAD_PRIME,Mathcad Prime,,,{}
NASTRAN,Nastran,,,{}
NI_MULTISIM,NI Multisim,,,{}
NX_CAM,NX CAM,,,{}
POWERFLOW,PowerFLOW,,,{}
PSCAD,PSCAD,,,{}
ROBOGUIDE,Roboguide,,,{}
TECNOMATIX,Tecnomatix,,,{}
SYNTHESIO,Synthesio,,,{}
BLACK_DUCK_SOFTWARE_COMPOSITION_ANALYSIS,Black Duck Software Composition Analysis,,,{}
VECTORCAST,VectorCAST,,,{}
IBM_ENGINEERING_TEST_MANAGEMENT,IBM Engineering Test Management,,,{}
TESTLOG,TestLog,,,{}
WEBLOAD,WebLOAD,,,{}
BIG_NETWORK,Big Network,,,{}
SYNOPSYS_COVERITY,Synopsys Coverity,,,{}
CODEPEER,CodePeer,,,{}
LDRA_TESTBED,LDRA TestBed,,,{}
INFINITYQS_PROFICIENT,InfinityQS ProFicient,,,{}
DELL_POWERSTORE,Dell PowerStore,,,{}
CHARGIFY,Chargify,,,{}
CITECT,Citect,,,{}
NAVISPHERE,Navisphere,,,{}
SUPPLY_CHAIN_GURU,Supply Chain Guru,,,{}
CISCO_CATALYST_3650_SERIES,Cisco Catalyst 3650 Series,,,{}
CISCO_CATALYST_9200_SERIES,Cisco Catalyst 9200 Series,,,{}
IBM_ENGINEERING_LIFECYCLE_OPTIMIZATION_ENGINEERING_INSIGHTS,IBM Engineering Lifecycle Optimization - Engineering Insights,,,{}
STUDIO_5000,Studio 5000,,,{}
SAKON,Sakon,,,{}
OPKEY,OpKey,,,{}
SUBJECT7,Subject7,,,{}
TESTSIGMA,Testsigma,,,{}
BITDEFENDER_ADVANCED_THREAT_INTELLIGENCE,Bitdefender Advanced Threat Intelligence,,,{}
OPENTSDB,openTSDB,,,{}
CLOCKIFY,Clockify,,,{}
PRODUCT_CONFIGURATOR_CPQ,Product configurator (CPQ),,,{}
DRIVEWORKS,DriveWorks,,,{}
DEMATIC_IQ,Dematic iQ,,,{}
PHOENIX_SITE,Phoenix Site,,,{}
CASCADE_CMS,Cascade CMS,,,{}
DOCVISION,DocVision,,,{}
TEAMGURU,TeamGuru,,,{}
PREDICTIVE_AI,Predictive AI,,,{}
WMS,WMS,,,{}
PLM_TEAMCENTER,PLM Teamcenter,,,{}
MICROSOFT_AZURE_DATA_FACTORY,Microsoft Azure Data Factory,,,{}
DESIGN_SOFTWARE,Design Software,,,{}
FINANCE_AND_ACCOUNTING_SOFTWARE,Finance & Accounting Software,,,{}
GENERATIVE_AI_SOFTWARE,Generative AI Software,,,{}
IT_INFRASTRUCTURE_SOFTWARE,IT Infrastructure Software,,,{}
ANALYTICS_AND_BUSINESS_INTELLIGENCE_BI,Analytics & Business Intelligence (BI),,,{}
SOFTWARE_DEVELOPMENT,Software Development,,,{}
HUMAN_CAPITAL_MANAGEMENT_SOFTWARE_HCM,Human Capital Management Software (HCM),,,{}
IT_MANAGEMENT_SOFTWARE,IT Management Software,,,{}
BIG_DATA_SOFTWARE,Big Data Software,,,{}
DATA_MANAGEMENT_SOFTWARE,Data Management Software,,,{}
SERVERS,Servers,,,{}
SECURITY_SOFTWARE,Security Software,,,{}
ARTIFICIAL_INTELLIGENCE_AND_MACHINE_LEARNING_AI_AND_ML,Artificial Intelligence & Machine Learning (AI & ML),,,{}
PRODUCTIVITY_AND_COLLABORATION_SOFTWARE,Productivity & Collaboration Software,,,{}
ROBOTIC_PROCESS_AUTOMATION_SOFTWARE_RPA,Robotic Process Automation Software (RPA),,,{}
OFFICE_ADMINISTRATION_SOFTWARE,Office Administration Software,,,{}
PROJECT_PORTFOLIO_MANAGEMENT_SOFTWARE,Project Portfolio Management Software,,,{}
E_COMMERCE_SOFTWARE,E-Commerce Software,,,{}
HOSPITALITY_SOFTWARE,Hospitality Software,,,{}
LIFE_SCIENCES_SOFTWARE,Life Sciences Software,,,{}
CONTENT_MANAGEMENT_SOFTWARE_CMS,Content Management Software (CMS),,,{}
DEVOPS_SOFTWARE,DevOps Software,,,{}
CLOUD_SOFTWARE,Cloud Software,,,{}
CONSTRUCTION_SOFTWARE,Construction Software,,,{}
CUSTOMER_SERVICE_SOFTWARE,Customer Service Software,,,{}
MARKETING_SOFTWARE,Marketing Software,,,{}
HOSTING_PROVIDERS,Hosting Providers,,,{}
SALES_SOFTWARE,Sales Software,,,{}
DATABASE_SOFTWARE,Database Software,,,{}
SUPPLY_CHAIN_AND_LOGISTICS_SOFTWARE,Supply Chain & Logistics Software,,,{}
COMPUTING_DEVICES,Computing Devices,,,{}
ENTERPRISE_RESOURCE_PLANNING_SOFTWARE_ERP,Enterprise Resource Planning Software (ERP),,,{}
LABORATORY_SOFTWARE,Laboratory Software,,,{}
UTILITIES_SOFTWARE,Utilities Software,,,{}
ASSET_MANAGEMENT_SOFTWARE,Asset Management Software,,,{}
ENVIRONMENT_HEALTH_AND_SAFETY_MANAGEMENT_SOFTWARE_EHS,"Environment, Health & Safety Management Software (EHS)",,,{}
COMPUTER_AIDED_ENGINEERING_DESIGN_AND_MANUFACTURING_CAE_CAD_AND_CAM,"Computer-Aided Engineering, Design & Manufacturing (CAE, CAD & CAM)",,,{}
GEOGRAPHIC_INFORMATION_SYSTEM_SOFTWARE_GIS,Geographic Information System Software (GIS),,,{}
GOVERNANCE_RISK_AND_COMPLIANCE_SOFTWARE_GRC,"Governance, Risk & Compliance Software (GRC)",,,{}
HEALTH_CARE_SOFTWARE,Health Care Software,,,{}
MANUFACTURING_EXECUTION_SYSTEM_MES,Manufacturing Execution System (MES),,,{}
OIL_AND_GAS_SOFTWARE,Oil & Gas Software,,,{}
EDUCATION_SOFTWARE,Education Software,,,{}
OPERATING_SYSTEMS_SOFTWARE,Operating Systems Software,,,{}
PAYMENT_SOFTWARE,Payment Software,,,{}
PRODUCT_LIFECYCLE_MANAGEMENT_SOFTWARE_PLM,Product Lifecycle Management Software (PLM),,,{}
PROCUREMENT_SOFTWARE,Procurement Software,,,{}
PROGRAMMING_LANGUAGE,Programming Language,,,{}
REAL_ESTATE_SOFTWARE,Real Estate Software,,,{}
PUBLIC_SAFETY_SOFTWARE,Public Safety Software,,,{}
IBM_DATAPOWER,IBM DataPower,,,{}
BENIFY,Benify,,,{}
SAP_ARIBA_CATALOG,SAP Ariba Catalog,,,{}
VISTEX,Vistex,,,{}
KITEWORKS,Kiteworks,,,{}
S_DRIVE,S-Drive,,,{}
INCAPSULA,Incapsula,,,{}
SALESFORCE_JOURNEY_BUILDER,Salesforce Journey Builder,,,{}
DIGITAL_GUARDIAN_PLATFORM,Digital Guardian Platform,,,{}
COGENT_DATAHUB,Cogent DataHub,,,{}
SAP_CRYSTAL,SAP Crystal,,,{}
SAS_ACCESS,SAS/ACCESS,,,{}
YANDEX_METRICA,Yandex Metrica,,,{}
CHEMIA,Chemia,,,{}
PERCEPTIVE_CONTENT,Perceptive Content,,,{}
CADENCY,Cadency,,,{}
QCAD,QCAD,,,{}
REGIOGRAPH,RegioGraph,,,{}
HIRE2RETIRE,Hire2Retire,,,{}
PRISMJS,Prismjs,,,{}
WEBENGAGE,WebEngage,,,{}
CONTROLUP,ControlUp,,,{}
PRIORITY_ERP,Priority ERP,,,{}
SMARTDEPLOY,SmartDeploy,,,{}
VOICEFLOW,Voiceflow,,,{}
P2P_GLOBAL,P2P Global,,,{}
SCIFORMA,Sciforma,,,{}
CINC,CINC,,,{}
ALTAIR_SIMLAB,Altair SimLab,,,{}
CONTROLANT,Controlant,,,{}
CITRIX_WORKSPACE_FEATURING_CITRIX_VIRTUAL_APPS_AND_DESKTOPS,Citrix Workspace (featuring Citrix Virtual Apps and Desktops),,,{}
IMPERVA_WEB_APPLICATION_FIREWALL_WAF,Imperva Web Application Firewall (WAF),,,{}
OPTIMIZELY_CONTENT_MANAGEMENT,Optimizely Content Management,,,{}
ALLYABLE,Allyable,,,{}
SYNEL,Synel,,,{}
3D_SYSTEMS_ON_DEMAND_MANUFACTURING_FORMERLY_QUICKPARTS,3D Systems On Demand Manufacturing (formerly Quickparts),,,{}
METADATA_IO,Metadata.io,,,{}
CIRCONUS,Circonus,,,{}
SALESFORCE_ANALYTICS_CLOUD,Salesforce Analytics Cloud,,,{}
APIARY,Apiary,,,{}
TYK_API_MANAGEMENT_PLATFORM,Tyk API Management Platform,,,{}
STARTDATE_ATS,StartDate ATS,,,{}
CISCO_CLOUDCENTER,Cisco CloudCenter,,,{}
EPSAGON,Epsagon,,,{}
UVICORN,Uvicorn,,,{}
ACQUEON_ENGAGEMENT,Acqueon Engagement,,,{}
FREEBSD_10,FreeBSD 10,,,{}
NODEJS_WEB_STACK,NodeJS Web Stack,,,{}
EXPLORATORY,Exploratory,,,{}
UCS_MINI,UCS Mini,,,{}
CATALYST_CLOUD,Catalyst Cloud,,,{}
BOTKIT,Botkit,,,{}
MINDMELD,MindMeld,,,{}
ROOKOUT,Rookout,,,{}
ANYPOINT_MQ,Anypoint MQ,,,{}
MATTERMOST,Mattermost,,,{}
CLOUD_EPC,Cloud EPC,,,{}
SMARTCARE,Smartcare,,,{}
IMICONNECT,IMIconnect,,,{}
TELNYX,Telnyx,,,{}
JUPITERONE,JupiterOne,,,{}
PCLOUD,pCloud,,,{}
PYRACLOUD,PyraCloud,,,{}
ARMORBLOX,Armorblox,,,{}
SPLUNK_OBSERVABILITY_CLOUD,Splunk Observability Cloud,,,{}
CLOUD_MANAGEMENT_PLATFORM_CMP,Cloud Management Platform (CMP),,,{}
BMC_SOFTWARE,BMC Software,,,{}
CISCO_INTERSIGHT_SERVICES,Cisco Intersight Services,,,{}
IBM_MASS_DATA_MIGRATION,IBM Mass Data Migration,,,{}
TRIGGERMESH,Triggermesh,,,{}
CLOUDAWARE,CloudAware,,,{}
GUARDICORE,GuardiCore,,,{}
LIVEBOARD,LiveBoard,,,{}
APACHE_PINOT,Apache Pinot,,,{}
JIFFLENOW,Jifflenow,,,{}
ANSIBLE_AWX,Ansible AWX,,,{}
MICRO_FOCUS_ZENWORKS_CONFIGURATION_MANAGEMENT,Micro Focus ZENworks Configuration Management,,,{}
NIXOS,NixOS,,,{}
CISCO_PACKAGED_CONTACT_CENTER_ENTERPRISE_PCCE,Cisco Packaged Contact Center Enterprise (PCCE),,,{}
REPLEX,Replex,,,{}
APPRENDA,Apprenda,,,{}
CONTIV,Contiv,,,{}
ANCHORE,Anchore,,,{}
STYRA,Styra,,,{}
BUILDBOT,Buildbot,,,{}
XL_DEPLOY,XL Deploy,,,{}
CONCOURSE_CI,Concourse CI,,,{}
NETFORMX_SALESXPERT,Netformx SalesXpert,,,{}
DIGITAL_SALES_PLATFORM,Digital Sales Platform,,,{}
CONTACTHUB,ContactHub,,,{}
NEXTIVA,Nextiva,,,{}
PEGA_CUSTOMER_SERVICE,Pega Customer Service,,,{}
CISCO_NEXUS_DASHBOARD,Cisco Nexus Dashboard,,,{}
SUNBIRD_DCIM,Sunbird DCIM,,,{}
VMWARE_VCENTER_OPERATIONS,VMware vCenter Operations,,,{}
CISCO_HYPERFABRIC,Cisco Hyperfabric,,,{}
CISCO_CLOUD_APIC,Cisco Cloud APIC,,,{}
CISCO_HYPERSHIELD,Cisco Hypershield,,,{}
ORACLE_VISUAL_BUILDER_CLOUD_SERVICE,Oracle Visual Builder Cloud Service,,,{}
DATAMEER,Datameer,,,{}
SECURITI,Securiti,,,{}
IBM_WATSON_STUDIO,IBM Watson Studio,,,{}
IBM_WATSON_MACHINE_LEARNING,IBM Watson Machine Learning,,,{}
PORT_OPERATE,PORT Operate,,,{}
ORACLE_REAL_APPLICATION_CLUSTERS_RAC,Oracle Real Application Clusters (RAC),,,{}
ORACLE_DATA_MASKING,Oracle Data Masking,,,{}
HP_DESKTOP,HP Desktop,,,{}
SCRIBUS,Scribus,,,{}
CREATELY,Creately,,,{}
BRANDFOLDER,Brandfolder,,,{}
POSTAL_IO,Postal.io,,,{}
MICROSOFT_DYNAMICS_GP,Microsoft Dynamics GP,,,{}
POUCHDB,PouchDB,,,{}
KALEIDO,Kaleido,,,{}
DIGITAL_RIVER,Digital River,,,{}
NEXTGEN_ENTERPRISE,NextGen Enterprise,,,{}
CISCO_REGISTERED_ENVELOPE_SERVICE,Cisco Registered Envelope Service,,,{}
SENDSAFELY,SendSafely,,,{}
ZIMBRA_COLLABORATION,Zimbra Collaboration,,,{}
INFORMACAST,InformaCast,,,{}
OSQUERY,Osquery,,,{}
TIBCO_CLOUD_INTEGRATION_INCLUDING_BUSINESSWORKS_AND_SCRIBE,TIBCO Cloud Integration (including BusinessWorks and Scribe),,,{}
SOCIO,Socio,,,{}
EVENTPRO,EventPro,,,{}
HAPPAY,Happay,,,{}
WEBEX_EXPERIENCE_MANAGEMENT,Webex Experience Management,,,{}
CISCO_SECUREX,Cisco SecureX,,,{}
BUILDOPS,BuildOps,,,{}
CISCO_FIREPOWER_4100,Cisco Firepower 4100,,,{}
PALO_ALTO_PA_3200_SERIES,Palo Alto PA-3200 Series,,,{}
KOUNT,Kount,,,{}
OPENNEBULA,OpenNebula,,,{}
WINMERGE,winmerge,,,{}
CFLOW_WORKFLOW_FOR_GOOGLE_APPS_FOR_G_SUITE,Cflow - Workflow for Google Apps for G Suite,,,{}
ALLEGRO_LIBRARY,Allegro library,,,{}
DGRAPH,Dgraph,,,{}
DILIGENT_HIGHBOND,Diligent HighBond,,,{}
STRAPI,Strapi,,,{}
DESKPRO,Deskpro,,,{}
FIREHYDRANT,FireHydrant,,,{}
THEHIVE,TheHive,,,{}
AUGMENTIR,Augmentir,,,{}
SCALEWAY,Scaleway,,,{}
OSSEC,Ossec,,,{}
CISCO_KINETIC,Cisco Kinetic,,,{}
AWS_IOT_ANALYTICS,AWS IoT Analytics,,,{}
TINYOS,TinyOS,,,{}
ASSET_VISION,Asset Vision,,,{}
CIMCON_SOFTWARE,CIMCON Software,,,{}
TECTIA,Tectia,,,{}
XREGEXP,XRegExp,,,{}
ECHARTS,ECharts,,,{}
PINIA,Pinia,,,{}
LEASEQUERY_ACCOUNTING_SOFTWARE,LeaseQuery Accounting Software,,,{}
SMARTSUPP,Smartsupp,,,{}
VIRTUAL_CHAT,Virtual Chat,,,{}
PROSPA,Prospa,,,{}
GODADDY_DIGITAL_MARKETING_SUITE,GoDaddy Digital Marketing Suite,,,{}
CMX_ENGAGE,CMX Engage,,,{}
AKKA,Akka,,,{}
AMAZON_TRANSLATE,Amazon Translate,,,{}
LEADSPACE,Leadspace,,,{}
INSIDEVIEW_APEX,InsideView Apex,,,{}
APPDIRECT,AppDirect,,,{}
REALYTICS,Realytics,,,{}
CLOUDMQTT,CloudMQTT,,,{}
HORNETQ,HornetQ,,,{}
PYSCRIPT,PyScript,,,{}
IONIC_APPFLOW,Ionic Appflow,,,{}
SERVERLESS_FRAMEWORK,Serverless Framework,,,{}
VODAFONE_PHONE,Vodafone Phone,,,{}
F5_BIG_IP_ACCESS_POLICY_MANAGER_APM,F5 BIG-IP Access Policy Manager (APM),,,{}
INFOBLOX_NIOS,Infoblox NIOS,,,{}
AZURE_VIRTUAL_WAN,Azure Virtual WAN,,,{}
EKAHAU_PRO,Ekahau Pro,,,{}
IBM_SEVONE_NETWORK_PERFORMANCE_MANAGEMENT,IBM SevOne Network Performance Management,,,{}
IXCHARIOT,IxChariot,,,{}
SCIENCELOGIC,ScienceLogic,,,{}
SEDONA_SYSTEMS,Sedona Systems,,,{}
SMOKEPING,SmokePing,,,{}
STATSEEKER,Statseeker,,,{}
CISCO_DEFENSE_ORCHESTRATOR,Cisco Defense Orchestrator,,,{}
CISCO_SECURE_CLOUD_ANALYTICS_STEALTHWATCH_CLOUD,Cisco Secure Cloud Analytics (Stealthwatch Cloud),,,{}
CISCO_STEALTHWATCH,Cisco Stealthwatch,,,{}
OBJECTDB,Objectdb,,,{}
OBJECTSTORE,ObjectStore,,,{}
WORKBOARD,Workboard,,,{}
MEETING_SCHEDULER,Meeting Scheduler,,,{}
APPBOT,Appbot,,,{}
PAN_OS,PAN-OS,,,{}
CISCO_DNA_SPACES,Cisco DNA Spaces,,,{}
MAILINATOR,Mailinator,,,{}
FICO_BLAZE_ADVISOR,FICO Blaze Advisor,,,{}
CISCO_ONEPK,Cisco OnePK,,,{}
AWS_SCHEMA_CONVERSION_TOOL,AWS Schema Conversion Tool,,,{}
QUANTELA,Quantela,,,{}
CALABRIO_CALL_RECORDING,Calabrio Call Recording,,,{}
SOURCEME,SourceMe,,,{}
CONTENTQUO,ContentQuo,,,{}
WINDOWS_COMMUNICATION_FOUNDATION,Windows Communication Foundation,,,{}
1PASSWORD,1Password,,,{}
PDQ_DEPLOY,PDQ Deploy,,,{}
MIRRO,Mirro,,,{}
IGODIGITAL,iGoDigital,,,{}
KEYNOTE,KeyNote,,,{}
BUSINESS_WIRE,Business Wire,,,{}
MICRO_FOCUS_OPEN_ENTERPRISE_SERVER,Micro Focus Open Enterprise Server,,,{}
CYBERARK_CONJUR,CyberArk Conjur,,,{}
ORACLE_PROCUREMENT_CLOUD,Oracle Procurement Cloud,,,{}
ERLANG,Erlang,,,{}
STRUCTURED_QUERY_LANGUAGE_SQL,Structured query language (SQL),,,{}
TARGETPROCESS,Targetprocess,,,{}
ROBOHEAD,RoboHead,,,{}
WEB2PY,web2py,,,{}
UCS_C220_M4_RACK_SERVER,UCS C220 M4 Rack Server,,,{}
COCKROACHDB,CockroachDB,,,{}
APACHE_GUACAMOLE,Apache Guacamole,,,{}
VMWARE_HORIZON_CLIENT,VMware Horizon Client,,,{}
TEMPO_PLANNER,Tempo Planner,,,{}
ORACLE_RETAIL_XSTORE_POINT_OF_SERVICE,Oracle Retail Xstore Point-of-Service,,,{}
ORACLE_REVENUE_MANAGEMENT_CLOUD,Oracle Revenue Management Cloud,,,{}
BEHAVIOSEC,BehavioSec,,,{}
ESOF,ESOF,,,{}
KENNA_SECURITY,Kenna Security,,,{}
HIGHFIVE,Highfive,,,{}
WEBEX_ROOM_SERIES,Webex Room Series,,,{}
CISCO_8000_SERIES_ROUTERS,Cisco 8000 Series Routers,,,{}
CISCO_ROUTER_ASR_5000,Cisco Router ASR 5000,,,{}
CISCO_ROUTER_ASR_900,Cisco Router ASR 900,,,{}
HG_INSIGHTS,HG Insights,,,{}
CLOUDGENIX,CloudGenix,,,{}
CISCO_EMAIL_SECURITY,Cisco Email Security,,,{}
CISCO_UMBRELLA,Cisco Umbrella,,,{}
CISCO_SECURITY_MANAGER,Cisco Security Manager,,,{}
STORAGE_DIRECTOR,Storage Director,,,{}
CANONICAL_LANDSCAPE,Canonical Landscape,,,{}
CISCO_NFV,Cisco NFV,,,{}
SUSE_LINUX_ENTERPRISE_SERVER,SUSE Linux Enterprise Server,,,{}
IVANTI_NEURONS,Ivanti Neurons,,,{}
APACHE_THRIFT,Apache Thrift,,,{}
SMARTSTACK,SmartStack,,,{}
CISCO_TETRATION_APPLICATION,Cisco Tetration Application,,,{}
CISCO_SECURE_WORKLOAD_TETRATION,Cisco Secure Workload (Tetration),,,{}
LOGROCKET,LogRocket,,,{}
SMARTLOOK,Smartlook,,,{}
SEUR,SEUR,,,{}
POSTE_ITALIANE,Poste Italiane,,,{}
SIMUL8,Simul8,,,{}
TEXTLOCAL,Textlocal,,,{}
POEDITOR,POEditor,,,{}
QASE,Qase,,,{}
ESPNET,ESPnet,,,{}
DELPHIX,Delphix,,,{}
CISCO_NEXUS_7000_SERIES,Cisco Nexus 7000 Series,,,{}
CISCO_NEXUS_9000_SERIES,Cisco Nexus 9000 Series,,,{}
CISCO_NEXUS_5000_SERIES,Cisco Nexus 5000 Series,,,{}
RANGEFORCE,RangeForce,,,{}
ORANGE_BUSINESS_SERVICES,Orange Business Services,,,{}
SEALIGHTS,SeaLights,,,{}
ACTIVESTATE_PLATFORM,ActiveState Platform,,,{}
ETHERPAD,Etherpad,,,{}
MASERGY,Masergy,,,{}
SAFEGUARD_CYBER,SafeGuard Cyber,,,{}
WYNTER,Wynter,,,{}
LIVESTORM,Livestorm,,,{}
PEXIP,Pexip,,,{}
STARTMEETING,StartMeeting,,,{}
CISCO_PACKET_TRACER,Cisco Packet Tracer,,,{}
SVRF,Svrf,,,{}
JASPER,Jasper,,,{}
VOICEITT,voiceitt,,,{}
CISCO_BUSINESS_EDITION_6000,Cisco Business Edition 6000,,,{}
EVOLVE_IP_PHONE_SYSTEM,Evolve IP Phone System,,,{}
APPCHECK,APPCHECK,,,{}
WAN_OPTIMIZATION,WAN Optimization,,,{}
ASCIIDOC,AsciiDoc,,,{}
INGENIUX,Ingeniux,,,{}
WEBGUI,WebGUI,,,{}
MANTINE,Mantine,,,{}
ACCUWEATHER,AccuWeather,,,{}
FLUID_UI,Fluid UI,,,{}
CISCO_WORKLOAD_OPTIMIZATION_MANAGER,Cisco Workload Optimization Manager,,,{}
CISCO_ZERO_TRUST_NETWORK,Cisco Zero Trust network,,,{}
PHENOM,Phenom,,,{}
DUO,Duo,,,{}
TEMPORAL,Temporal,,,{}
SERVERLESS_WORKFLOW,Serverless Workflow,,,{}
REQUESTS_API,Requests API,,,{}
ROBOT,Robot,,,{}
MS_AZURE,MS Azure,,,{}
GKE,GKE,,,{}
CX_CLOUD_MOBILE_APP,CX Cloud Mobile App,,,{}
CX_CLOUD_NOTIFICATIONS,CX Cloud Notifications,,,{}
CX_CLOUD_DESKTOP,CX Cloud Desktop,,,{}
TEMPORAL_WORKFLOWS,Temporal Workflows,,,{}
PROTOBUF,Protobuf,,,{}
ROUTE53,Route53),,,{}
JIRA_ALIGN,Jira Align,,,{}
JSM,JSM,,,{}
THANOS,Thanos,,,{}
PLAYWRIGHT,Playwright,,,{}
ARGOS_CI,Argos-CI,,,{}
DETOX,Detox,,,{}
CURRENTS,Currents,,,{}
NIGHTWATCHJS,NightwatchJS,,,{}
JAVASCRIPT_D3,JavaScript/d3,,,{}
CSS_SASS,CSS/Sass,,,{}
SVELTE,Svelte,,,{}
APOLLO,Apollo,,,{}
LEADIQ,LeadIQ,,,{}
VARNISH,Varnish,,,{}
BOOSTR,Boostr,,,{}
GOOGLE_MEET,Google Meet,,,{}
FLOURISH,Flourish,,,{}
EMARKETER,EMARKETER,,,{}
PREBID,Prebid,,,{}
PAPERCUP,Papercup,,,{}
SPAN_SYSTEM,Span System,,,{}
ORSOFT_MANUFACTURING_WORKBENCH,ORSOFT Manufacturing Workbench,,,{}
BEAMEX_CALIBRATION_SOFTWARE,Beamex Calibration Software,,,{}
DOCCONTROL,DocControl,,,{}
MEMOQ_TRANSLATOR_PRO,memoQ translator pro,,,{}
TENNAXIA,Tennaxia,,,{}
ORACLE_CRM_ON_DEMAND,Oracle CRM On Demand,,,{}
AYASDI,Ayasdi,,,{}
SIGMAPLOT,SigmaPlot,,,{}
STAE,stae,,,{}
VALIMAIL,Valimail,,,{}
KOLLECTIVE,Kollective,,,{}
CHEMATIX,Chematix,,,{}
INFOR_XA,Infor XA,,,{}
TOOLTIME,ToolTime,,,{}
OPENTEXT_INTELLIGENT_CAPTURE,OpenText Intelligent Capture,,,{}
AD_ENTERPRISE,AD Enterprise,,,{}
DROMO,Dromo,,,{}
FREEZERWORKS,Freezerworks,,,{}
OPEN_BROADCASTER_SOFTWARE,Open Broadcaster Software,,,{}
METAPHACTORY,metaphactory,,,{}
IBM_STERLING_B2B_INTEGRATOR,IBM Sterling B2B Integrator,,,{}
SIGMAXL,SigmaXL,,,{}
SPOTME,SpotMe,,,{}
AVEVA_DIAGRAMS,AVEVA Diagrams,,,{}
VALIDATION_MANAGER,Validation Manager,,,{}
KEEPASS,KeePass,,,{}
AVEVA_E3D_DESIGN,AVEVA E3D Design,,,{}
STARDOG,Stardog,,,{}
SAP_TAX_COMPLIANCE,SAP Tax Compliance,,,{}
ELCAD,ELCAD,,,{}
TESTPARTNER,TestPartner,,,{}
POOLPARTY,PoolParty,,,{}
COOPERATE_MARKETING,Cooperate Marketing,,,{}
ECLASS,eClass,,,{}
SITE5,Site5,,,{}
LAGOON,Lagoon,,,{}
VEEVA,Veeva,,,{}
VUE,Vue,,,{}
GRAPHSQL,GraphSQL,,,{}
RACKSPACE,Rackspace,,,{}
GPT,GPT,,,{}
LLAMA,Llama,,,{}
FALCON,Falcon,,,{}
MISTRAL,Mistral,,,{}
PYTHON_3,Python 3,,,{}
UNIPROT,UniProt,,,{}
TOAD_DATAPOINT,TOAD DataPoint,,,{}
SSO_INTEGRATION,SSO Integration,,,{}
POWERPLATFORM,PowerPlatform,,,{}
INMATION,Inmation,,,{}
ITEMS,ITEMS,,,{}
ASG_REMOTE_DESKTOP,ASG Remote Desktop,,,{}
CONFLUENCE_ATLASSIAN,Confluence (Atlassian),,,{}
2B_ADVICE,2B Advice,,,{}
SAP_BEX,SAP BEx,,,{}
DIRECTION_LOCAL,Direction Local,,,{}
CRITICAL_START,Critical Start,,,{}
CONTRACTS_ADVANCE,Contracts Advance,,,{}
HYPERMIL,hyperMIL,,,{}
KASPERSKY,Kaspersky,,,{}
Q_PULSE,Q-Pulse,,,{}
UPS_HEALTHCARE,UPS Healthcare,,,{}
ICOLOGIQ,Icologiq,,,{}
PLUMBR,Plumbr,,,{}
MEDIAOCEAN,Mediaocean,,,{}
GOFUNDME,GoFundMe,,,{}
WORKDAY_PRISM_ANALYTICS,Workday Prism Analytics,,,{}
AWS_STORAGE_GATEWAY,AWS Storage Gateway,,,{}
ADSWIZZ,Adswizz,,,{}
ROCKET_SERVERGRAPH,Rocket Servergraph,,,{}
BACKGROUND_REMOVAL,Background Removal,,,{}
THE_SEO_FRAMEWORK,The SEO Framework,,,{}
LINEUP,Lineup,,,{}
MAGNOLIA,Magnolia,,,{}
MONTE_CARLO,Monte Carlo,,,{}
DRONE,Drone,,,{}
PAYFLOW,Payflow,,,{}
CLOUDSQL,CloudSQL,,,{}
LOOKERML,LookerML,,,{}
CLOUD_VISION_API,Cloud Vision API,,,{}
DRUPAL_9,Drupal 9,,,{}
WORDPRESS_ORG,WordPress.org,,,{}
ADDTOANY,AddToAny,,,{}
AMAZON_ASSOCIATES,Amazon Associates,,,{}
COMMERCE_JS,Commerce.js,,,{}
MEDIAWIKI,MediaWiki,,,{}
SWOOGO,Swoogo,,,{}
PEX,PEX,,,{}
BOUNCEX,BounceX,,,{}
SERRAVIEW,Serraview,,,{}
MACBOOK_AIR,MacBook Air,,,{}
ONEPLUS_NORD_CE_3_LITE_5G,OnePlus Nord CE 3 Lite 5G,,,{}
FRONTEND_MASTERS,Frontend Masters,,,{}
AKAMAI_CLIENT_REPUTATION,Akamai Client Reputation,,,{}
SALESFORCE_ORDER_MANAGEMENT,Salesforce Order Management,,,{}
STN_VIDEO,STN Video,,,{}
WEBINY,Webiny,,,{}
ADOBE_BRIDGE,Adobe Bridge,,,{}
CLEARSCOPE,Clearscope,,,{}
PI_DATAMETRICS,Pi Datametrics,,,{}
GLPI,GLPI,,,{}
CITIZENNET,CitizenNet,,,{}
SHAREABLEE,Shareablee,,,{}
CHARGEBEE,Chargebee,,,{}
DEPARTURES,Departures,,,{}
ARDOQ,Ardoq,,,{}
DATASTAX_ENTERPRISE,DataStax Enterprise,,,{}
N_SOLID,N|Solid,,,{}
WHATSUP_GOLD,WhatsUp Gold,,,{}
SPECLE,Specle,,,{}
AD_EXCHANGE,Ad Exchange,,,{}
MODE,Mode,,,{}
QSEARCH,Qsearch,,,{}
EMPLIFI,Emplifi,,,{}
ECS_FARGATE,ECS Fargate,,,{}
AWS_KINESIS,AWS Kinesis,,,{}
AWS_OPENSEARCH,AWS OpenSearch,,,{}
LIGHTGBM,LightGBM,,,{}
GOOGLE_ANALYTICS_360,Google Analytics 360,,,{}
CLEAR_ANALYTICS,Clear Analytics,,,{}
FENERGO_PLATFORM,Fenergo Platform,,,{}
ORACLE_ACCOUNT_RECONCILIATION_CLOUD,Oracle Account Reconciliation Cloud,,,{}
MOCKOON,Mockoon,,,{}
INFORMATICA_CLOUD_APPLICATION_INTEGRATION,Informatica Cloud Application Integration,,,{}
KONG_GATEWAY,Kong Gateway,,,{}
ALIGHT_SOLUTIONS,Alight Solutions,,,{}
APACHE_HOP,Apache Hop,,,{}
MAXMIND,MaxMind,,,{}
IDEATE_SOFTWARE,Ideate Software,,,{}
WEBIO,Webio,,,{}
PARKMYCLOUD,ParkMyCloud,,,{}
AWS_SHIELD,AWS Shield,,,{}
SAMBA_SERVER_READY_TO_GO,Samba Server - Ready to Go,,,{}
AMAZON_FSX,Amazon FSx,,,{}
XACTLY_COMMISSIONS_SOFTWARE,Xactly Commissions Software,,,{}
PYTHON_WEBSOCKET,python websocket,,,{}
SUMMITSYNC,SummitSync,,,{}
PUPPET_ENTERPRISE,Puppet Enterprise,,,{}
MIRANTIS_KUBERNETES_ENGINE_FORMERLY_DOCKER_ENTERPRISE,Mirantis Kubernetes Engine (formerly Docker Enterprise),,,{}
CENTREON,centreon,,,{}
JQUERY_CDN,jQuery CDN,,,{}
FINASTRA,Finastra,,,{}
THOUGHT_INDUSTRIES,Thought Industries,,,{}
LEARNUPON_LMS,LearnUpon LMS,,,{}
ZUORA_CPQ,Zuora CPQ,,,{}
SALESFORCE_DESK,Salesforce Desk,,,{}
BITSTAMP,Bitstamp,,,{}
BLUECONIC,BlueConic,,,{}
LINKEDIN_SIGN_IN,Linkedin Sign-in,,,{}
TOTANGO,Totango,,,{}
AWS_DATA_EXCHANGE,AWS Data Exchange,,,{}
DATA_WORLD,data.world,,,{}
LABELBOX,Labelbox,,,{}
ORACLE_AUTONOMOUS_DATA_WAREHOUSE,Oracle Autonomous Data Warehouse,,,{}
ORACLE_MYSQL_CLOUD_SERVICE,Oracle MySQL Cloud Service,,,{}
XCEPTOR,Xceptor,,,{}
QUARK_PUBLISHING_PLATFORM,Quark Publishing Platform,,,{}
PLAUSIBLE_ANALYTICS,Plausible Analytics,,,{}
THOUGHT_MACHINE,Thought Machine,,,{}
SDL_TRIDION,SDL Tridion,,,{}
SENDOSO,Sendoso,,,{}
MISYS,MISys,,,{}
QUORA_ADS,Quora Ads,,,{}
CLEAR_ENTERPRISE,Clear Enterprise,,,{}
XPERTDOC,Xpertdoc,,,{}
CLICKTOOLS,Clicktools,,,{}
BLOOMFIRE,Bloomfire,,,{}
RAINFOCUS,RainFocus,,,{}
CVENT_REGISTRATION,Cvent Registration,,,{}
CLIENT_CULTURE,Client Culture,,,{}
WELLVIEW,WellView,,,{}
KENSHO,Kensho,,,{}
SANDP_GLOBAL_MARKET_INTELLIGENCE,S&P Global Market Intelligence,,,{}
SANDP_CAPITAL_IQ_PRO,S&P Capital IQ Pro,,,{}
KONA_WEB_APPLICATION_FIREWALL,Kona Web Application Firewall,,,{}
IHS_KINGDOM,IHS Kingdom,,,{}
MAPUBLISHER,MAPublisher,,,{}
INSTABASE,Instabase,,,{}
ALLVUE,Allvue,,,{}
BEDROCK_DATA,Bedrock Data,,,{}
NEW_RELIC_ALERTS,New Relic Alerts,,,{}
LIGHTBOX,Lightbox,,,{}
OWL_CAROUSEL,OWL Carousel,,,{}
DURANDALJS,DurandalJS,,,{}
MUSTACHE_WEB_TEMPLATE,Mustache Web Template,,,{}
FACTBOX,FactBox,,,{}
LOADUI,LoadUI,,,{}
ARCGIS_INSIGHTS,ArcGIS Insights,,,{}
LOGZ_IO,Logz.io,,,{}
SANDP_CAPITAL_IQ,S&P Capital IQ,,,{}
FUNNEL,Funnel,,,{}
MQ_MANAGER,MQ Manager,,,{}
CROWDCOMPASS,CrowdCompass,,,{}
MOZ_PRO,Moz Pro,,,{}
DIANOMI,Dianomi,,,{}
SKUID,Skuid,,,{}
GROOPER,Grooper,,,{}
GEO_SUITE,GEO SUITE,,,{}
IBM_INFOSPHERE_INFORMATION_SERVER,IBM InfoSphere Information Server,,,{}
DILIGENT_ENTITIES,Diligent Entities,,,{}
TARGET_DASHBOARD,Target Dashboard,,,{}
XTRACTION,Xtraction,,,{}
NET_4_5,.NET 4.5,,,{}
YAHOO_ADVERTISING,Yahoo Advertising,,,{}
EVESTMENT,eVestment,,,{}
PRECORO,Precoro,,,{}
SYNERTRADE_ACCELERATE,SynerTrade Accelerate,,,{}
JIRA_CORE,Jira Core,,,{}
ROCKETDOCS,RocketDocs,,,{}
PENPOT,Penpot,,,{}
ONE_BY_AOL,ONE by AOL,,,{}
ILEVEL,iLEVEL,,,{}
KOFAX_RPA,Kofax RPA,,,{}
COPILOT_AI,CoPilot AI,,,{}
SOLEADIFY,Soleadify,,,{}
AURYC,Auryc,,,{}
REACTFLOW,Reactflow,,,{}
OKTOPOST,Oktopost,,,{}
APPLANGA,Applanga,,,{}
MICRO_FOCUS_LOADRUNNER_PROFESSIONAL,Micro Focus LoadRunner Professional,,,{}
JOBDIVA,JobDiva,,,{}
TEALBOOK,tealbook,,,{}
LEANFT,LeanFT,,,{}
OCTOPERF,Octoperf,,,{}
DRAGON_SPEECH_RECOGNITION_SOFTWARE,Dragon Speech Recognition Software,,,{}
TEADS,Teads,,,{}
EXPRESS_SCRIBE,Express Scribe,,,{}
CROWNPEAK,CrownPeak,,,{}
DEVEXTREME,DevExtreme,,,{}
ELEMENT_UI,Element UI,,,{}
TIDAL_AUTOMATION,Tidal Automation,,,{}
ZSCALER,ZScaler,,,{}
MFA,MFA,,,{}
TRELLIX,Trellix,,,{}
AIRWATCH_XENMOBILE,Airwatch/Xenmobile,,,{}
LANDESK_SCCM,Landesk/SCCM,,,{}
LENOVO_PREMIER_SUPPORT,Lenovo Premier Support,,,{}
FTP_GO_ANYWHERE,FTP Go Anywhere,,,{}
VMC,VMC,,,{}
AVI_LB,AVI LB,,,{}
BIGIP_F5,BigIP F5,,,{}
SSL_CERTIFICATES,SSL Certificates,,,{}
AZURE_MACHINE_LEARNING,Azure Machine Learning,,,{}
SOLARIS,Solaris,,,{}
UNIX,UNIX,,,{}
STREAMING_REPLICATION,Streaming Replication,,,{}
LOW_CODE_NO_CODE_PLATFORMS,Low-Code/No-Code platforms,,,{}
IIS_7_5,IIS 7.5,,,{}
LEAFLET_JS,Leaflet.js,,,{}
GOOGLE_PUBLISHER_TAG,Google Publisher Tag,,,{}
APPLE_DESKTOP,Apple Desktop,,,{}
ATI_TESTING,ATI Testing,,,{}
PSI_BRIDGE,PSI Bridge,,,{}
KEEPER_PASSWORD_MANAGER,Keeper Password Manager,,,{}
PATCH_MY_PC,Patch My PC,,,{}
PROGRESS_SITEFINITY,Progress Sitefinity,,,{}
ZSCALER_ZIA,Zscaler ZIA,,,{}
WEB_PROXY,Web Proxy,,,{}
EDR,EDR,,,{}
FIREWALL,Firewall,,,{}
MAIL_SECURITY,Mail Security,,,{}
LOADRUNNER,LoadRunner,,,{}
SITESCOPE,SiteScope,,,{}
UNITY_GAMING_ENGINE,Unity gaming engine,,,{}
MICROSOFT_AZURE_TEXT_TO_SPEECH,Microsoft/Azure text-to-speech,,,{}
SPEECH_TO_TEXT,speech-to-text,,,{}
SAS_VISUAL_ANALYTICS,SAS Visual Analytics,,,{}
BIMCOLLAB,BIMcollab,,,{}
SOLIBRI,Solibri,,,{}
SAP_DATASPHERE,SAP Datasphere,,,{}
BREVO,Brevo,,,{}
ZENQMS,ZenQMS,,,{}
ATOSS,ATOSS,,,{}
ANIMATION_MASTER,Animation Master,,,{}
GOOGLE_ANALYTICS_ENHANCED_ECOMMERCE,Google Analytics Enhanced eCommerce,,,{}
NEOBRAIN,Neobrain,,,{}
HEAPHERO,HeapHero,,,{}
CORTEX_XPANSE,Cortex Xpanse,,,{}
NETGATE_TNSR_VROUTER_EDGE_ACCESS_VPN,Netgate TNSR vRouter (Edge / Access / VPN),,,{}
FINGERPRINTJS,FingerprintJS,,,{}
KASADA,Kasada,,,{}
BIGTINCAN_ZUNOS,Bigtincan Zunos,,,{}
COLLECTION_MASTER,Collection-Master,,,{}
ATLASSIAN_DATA_CENTER,Atlassian Data Center,,,{}
CLARIP,Clarip,,,{}
DATAIKU_DSS,Dataiku DSS,,,{}
ANALYTICDB,AnalyticDB,,,{}
LOJA_INTEGRADA,Loja Integrada,,,{}
SALESFLOOR,Salesfloor,,,{}
LENGOW,Lengow,,,{}
TARGET2SELL,Target2Sell,,,{}
ATTENSI,Attensi,,,{}
EXPENSYA,Expensya,,,{}
FORTER,Forter,,,{}
TRAACKR,Traackr,,,{}
FOHR,Fohr,,,{}
CLAIMABLE,Claimable,,,{}
WAALAXY,waalaxy,,,{}
CONTROL_M_MANAGED_FILE_TRANSFER,Control-M Managed File Transfer,,,{}
MAUTIC,Mautic,,,{}
BRANDING_BRAND,Branding Brand,,,{}
SAP_LIST_VIEWER_ALV,SAP List Viewer (ALV),,,{}
ZIFLOW,Ziflow,,,{}
AGILENCE,Agilence,,,{}
FACEBOOK_LOGIN,Facebook Login,,,{}
CONTACTABILITY,Contactability,,,{}
STORYLY,Storyly,,,{}
ALTERNATIVE_PAYMENTS,Alternative Payments,,,{}
CONEKTA,Conekta,,,{}
KIBO_PERSONALIZATION_FORMERLY_MONETATE_AND_CERTONA,Kibo Personalization (formerly Monetate and Certona),,,{}
LASCOM,Lascom,,,{}
POWTOON,Powtoon,,,{}
ACCENGAGE,Accengage,,,{}
PLANVIEW_PPM_PRO,Planview PPM Pro,,,{}
RELEX_PROMOTION_AND_MARKDOWN_OPTIMIZATION,RELEX Promotion and markdown optimization,,,{}
INTELLIGENCE_RETAIL,Intelligence Retail,,,{}
RETAILCLOUD,retailcloud,,,{}
OKTA_ADAPTIVE_MULTI_FACTOR_AUTHENTICATION,Okta Adaptive Multi-Factor Authentication,,,{}
OMNICHANNEL_PROMOTION_PRICING,Omnichannel Promotion Pricing,,,{}
HERMES_SHIPPING,Hermes Shipping,,,{}
OLAPIC,Olapic,,,{}
DREAMCAST,Dreamcast,,,{}
TECSYS,Tecsys,,,{}
WAREHOUSE_MANAGEMENT,Warehouse Management,,,{}
AKAMAI_WEB_APPLICATION_PROTECTOR,Akamai Web Application Protector,,,{}
CHAKRA_UI,Chakra UI,,,{}
CIVICTHEME,CivicTheme,,,{}
ROTAGEEK,Rotageek,,,{}
STOREFORCE,Storeforce,,,{}
PRODUCTGRAPH,ProductGraph,,,{}
JENKINS_REACT_AND_ANGULAR_JAVA_AND_NODE_JS,Jenkins React and Angular  Java and Node.js,,,{}
NETFLIX_S_DGS_FRAMEWORK,Netflix’s DGS framework,,,{}
REACT_JSJERSEY,React.JSJersey,,,{}
EQUIFAX_I9_TOOL,Equifax I9 Tool,,,{}
SPRING_MVC,Spring MVC,,,{}
HIBERNATE_ORM,Hibernate ORM,,,{}
ACTIVEMQ,ActiveMQ,,,{}
GOOGLE_S_GEMINI,Google's Gemini,,,{}
DML,DML,,,{}
SHIPYARD,Shipyard,,,{}
WEVO,WEVO,,,{}
QGUAR,Qguar,,,{}
1010_DATA,1010 Data,,,{}
MAPP,Mapp,,,{}
KISSFLOW,Kissflow,,,{}
KNOWLEDGE_VAULT,Knowledge Vault,,,{}
AMAZON_DRIVE,Amazon Drive,,,{}
FIIX,Fiix,,,{}
BEQOM,beqom,,,{}
NETX360,NetX360,,,{}
SELLSY,Sellsy,,,{}
EMBER_FUND,Ember Fund,,,{}
VEERA,Veera,,,{}
AZURE_MACHINE_LEARNING_STUDIO,Azure Machine Learning Studio,,,{}
COM_PLETE,Com-plete,,,{}
INPAGE,InPage,,,{}
KANTAR,Kantar,,,{}
AMS_SOFTWARE,AMS Software,,,{}
CHANGE_AUDITOR,Change Auditor,,,{}
PROFITERO,Profitero,,,{}
INSTASHOP,InstaShop,,,{}
PRODUCTSUP,Productsup,,,{}
HOOTSUITE_AMPLIFY,Hootsuite Amplify,,,{}
PERFORMANCE_PRO,Performance Pro,,,{}
FORTANIX,Fortanix,,,{}
COMARCH,Comarch,,,{}
TRADEEDGE_MARKET_CONNECT,TradeEdge Market Connect,,,{}
OMNI_ERP,Omni ERP,,,{}
MOBILEXPENSE,MobileXpense,,,{}
RESCO_MOBILE_CRM,Resco Mobile CRM,,,{}
UBER_FREIGHT,Uber Freight,,,{}
IRAF,IRAF,,,{}
ENVESTNET,Envestnet,,,{}
JAVA_DATA_OBJECTS_JDO,Java Data Objects (JDO),,,{}
INSTAPAGE,Instapage,,,{}
IBOTTA,Ibotta,,,{}
JUNIPER_APSTRA,Juniper Apstra,,,{}
HYPERSCIENCE,Hyperscience,,,{}
CIRCUS_STREET,Circus Street,,,{}
COMMERCEIQ,CommerceIQ,,,{}
AMAZON_SPONSORED_ADS,Amazon Sponsored Ads,,,{}
PRIME_ANALYTICS,Prime Analytics,,,{}
NSQ,Nsq,,,{}
AMPPS,AMPPS,,,{}
CLOUDFLARE_TURNSTILE,Cloudflare Turnstile,,,{}
PROMOBOXX,Promoboxx,,,{}
PPRO,PPRO,,,{}
PAYEEZY,Payeezy,,,{}
PAYU,PayU,,,{}
CAM350,CAM350,,,{}
MARS_PLM,Mars PLM,,,{}
SQUADCAST,SquadCast,,,{}
PROCEPTION2,Proception2,,,{}
ABBYY_TIMELINE,ABBYY Timeline,,,{}
LOTUSSCRIPT,LotusScript,,,{}
PRIORITY_MATRIX,Priority Matrix,,,{}
REPSLY,Repsly,,,{}
TRAX_RETAIL,Trax Retail,,,{}
PACVUE,Pacvue,,,{}
NIELSEN_ASO,Nielsen ASO,,,{}
SYMPHONY_AI_PLATFORM,Symphony AI platform,,,{}
SALESFORCE_ENGAGE,Salesforce Engage,,,{}
EASYLOG,Easylog,,,{}
AZURE_DEVTEST_LABS,Azure DevTest Labs,,,{}
CASCADE_STRATEGY,Cascade Strategy,,,{}
SUZY,Suzy,,,{}
ZAPPI,Zappi,,,{}
PERSEFONI,Persefoni,,,{}
TERVENE,Tervene,,,{}
EXCEEDRA,Exceedra,,,{}
DEMAND_PLANNING,Demand planning,,,{}
BEELINE,Beeline,,,{}
LGC,LGC,,,{}
DRUPAL_MULTISITE,Drupal Multisite,,,{}
QUINYX,Quinyx,,,{}
CLOUDFLARE_ANALYTICS,Cloudflare Analytics,,,{}
APPCHECKER,AppChecker,,,{}
CATCHPOINT_CORE_WEB_VITALS,Catchpoint Core Web Vitals,,,{}
ITIL,ITIL,,,{}
CARNOW,CarNow,,,{}
AZURE_DATA_FACTORY_ADF,Azure Data Factory (ADF),,,{}
SYNAPSE_ANALYTICS,Synapse Analytics,,,{}
DELTA_LIVE_TABLES_DLT,Delta Live Tables (DLT),,,{}
DIGICERTONE,DigicertOne,,,{}
VENAFI,Venafi,,,{}
BLUE_YONDER,Blue Yonder,,,{}
WEB_API,Web API,,,{}
AZURE_MLOPS,Azure MLOps,,,{}
YESWARE,Yesware,,,{}
SYMANTEC_WEB_SECURITY_SERVICE,Symantec Web Security Service,,,{}
CONCUR,Concur,,,{}
XSLT,XSLT,,,{}
DSC,DSC,,,{}
AI_PROMPTCRAFT,AI Promptcraft,,,{}
OPEN_CLOUD_FOUNDRY,Open Cloud Foundry,,,{}
SCSS,SCSS,,,{}
TRANSACT_SQL,Transact-SQL,,,{}
CLOUD_FOUNDRY,Cloud Foundry,,,{}
OFFICE365,Office365,,,{}
VEOCI,Veoci,,,{}
SKYMETRIX,Skymetrix,,,{}
ROI_CALCULATOR,ROI calculator,,,{}
TRAX_MAINTENANCE,TRAX Maintenance,,,{}
MVISION_ENDPOINT_SECURITY_PLATFORM,MVISION Endpoint Security Platform,,,{}
NEXUS,NEXUS,,,{}
BACKBONE,Backbone,,,{}
HYPER_V,Hyper-V,,,{}
OFFICE_365_LOTUS_NOTES,Office 365 Lotus Notes,,,{}
JIRA_QIRA,JIRA/QIRA,,,{}
JUPYTERLAB,JupyterLab,,,{}
AIRBRIDGE,Airbridge,,,{}
CONVERSIO,Conversio,,,{}
APACHE_OPENJPA,Apache OpenJPA,,,{}
ONSHIFT,OnShift,,,{}
PMXPERT,PMXpert,,,{}
COMPETITOR_MONITOR,Competitor Monitor,,,{}
OSANO,Osano,,,{}
SERVICEWARE_KNOWLEDGE,Serviceware Knowledge,,,{}
SKYWORD,Skyword,,,{}
CHINACDN,ChinaCDN,,,{}
HELLOWORLD,HelloWorld,,,{}
BOXEVER,Boxever,,,{}
ASSURE_SECURITY,Assure Security,,,{}
MONITOR_ERP,MONITOR ERP,,,{}
RDOC,RDoc,,,{}
ECLINICALWORKS,eClinicalWorks,,,{}
REDCAP,REDCap,,,{}
SALESFORCE_INBOX,Salesforce Inbox,,,{}
MEETINGBOX,Meetingbox,,,{}
NETRADYNE,Netradyne,,,{}
ZINGHR,ZingHR,,,{}
CONTIKI,Contiki,,,{}
COHERENCE,Coherence,,,{}
DOTDIGITAL_ENGAGEMENT_CLOUD,dotdigital Engagement Cloud,,,{}
SKEDDA,Skedda,,,{}
MAJESTIC,Majestic,,,{}
SWRVE,Swrve,,,{}
SALESFORCE_AUTHENTICATOR,Salesforce Authenticator,,,{}
CORUSON,Coruson,,,{}
BACKBOX_ORG,BackBox.org,,,{}
GENETEC_SECURITY_CENTER,Genetec Security Center,,,{}
DCS_RMS,DCS RMS,,,{}
SCRUMDESK,ScrumDesk,,,{}
WAVETEC,Wavetec,,,{}
HUB_PLANNER,Hub Planner,,,{}
CYBERWATCH,Cyberwatch,,,{}
CHERWELL_ITSM,Cherwell ITSM,,,{}
TRENGO,Trengo,,,{}
GEODIS,GEODIS,,,{}
ORACLE_ACCESS_MANAGEMENT,Oracle Access Management,,,{}
AIGENT,Aigent,,,{}
SPOND,Spond,,,{}
DELL_EMC_POWERMAX,Dell EMC PowerMax,,,{}
SIMATIC_WINCC,SIMATIC WinCC,,,{}
SDL_WORLDSERVER,SDL WorldServer,,,{}
AKAMAI_ADAPTIVE_MEDIA_DELIVERY,Akamai Adaptive Media Delivery,,,{}
GTMETRIX,GTmetrix,,,{}
AKAMAI_KONA_SITE_DEFENDER,Akamai Kona Site Defender,,,{}
MICROSOFT_PURVIEW,Microsoft Purview,,,{}
MIMECAST_API,Mimecast API,,,{}
SPRING_WEBFLUX,Spring Webflux,,,{}
ORACLE_WEBLOGIC_SERVER,Oracle WebLogic Server,,,{}
YUGABYTEDB,YugabyteDB,,,{}
REACTIVE_PROGRAMMING,Reactive Programming,,,{}
MICROSERVICES_ARCHITECTURE,Microservices Architecture,,,{}
EVENT_DRIVEN_ARCHITECTURE,Event-Driven Architecture,,,{}
SOAP_REST_WEB_SERVICES,SOAP/ Rest Web Services,,,{}
ORACLEDB,OracleDB,,,{}
JBOSS_EAP,JBoss EAP,,,{}
AZURE_DATA_LAKE_STORAGE_ADLS,Azure Data Lake Storage (ADLS),,,{}
NOSQL_DATABASES,NoSQL databases,,,{}
SOLACE_EVEN_MESH,Solace Even Mesh,,,{}
GOOGLE_DIALOGFLOW,Google Dialogflow,,,{}
QNA_MAKER,QnA Maker,,,{}
MICRO_SERVICE_ARCHITECTURE,Micro Service Architecture,,,{}
OXYGEN_FRAMEWORK,Oxygen Framework,,,{}
BOTIUM,Botium,,,{}
DIALOGFLOW,Dialogflow,,,{}
MICROSOFT_BOT_FRAMEWORK_SDK,Microsoft Bot Framework SDK,,,{}
BOT_FRAMEWORK_EMULATOR_AND_COMMAND_LINE_TOOLS,Bot Framework Emulator and command-line tools,,,{}
BOT_FRAMEWORK_COMPOSER,Bot Framework Composer,,,{}
EXPLAIN_EVERYTHING,Explain Everything,,,{}
IBM_WATSON_DISCOVERY,IBM Watson Discovery,,,{}
IQMS_ERP,IQMS ERP,,,{}
SAP_S_4HANA_CLOUD,SAP S/4HANA Cloud,,,{}
PATSNAP,PatSnap,,,{}
DATABOOK,Databook,,,{}
KAKAO_PAY,Kakao Pay,,,{}
PETREL_EANDP_SOFTWARE_PLATFORM,Petrel E&P Software Platform,,,{}
CERTIFID,CertifID,,,{}
TRIPWIRE_IP360,Tripwire IP360,,,{}
ASSIMA_TRAINING_SUITE,Assima Training Suite,,,{}
ANSYS_GRANTA_SELECTOR,Ansys Granta Selector,,,{}
ASSET_PERFORMANCE_MANAGEMENT,Asset Performance Management,,,{}
SMART_SIGNAL,Smart Signal,,,{}
MAERSK_4PL,Maersk 4PL,,,{}
LANDMARK,Landmark,,,{}
CHECKIT,Checkit,,,{}
INCOPRO,Incopro,,,{}
BOTMAKER,Botmaker,,,{}
SPACELIFT,Spacelift,,,{}
CATPLAN,CATPlan,,,{}
VOXPOPME,Voxpopme,,,{}
ARISE,Arise,,,{}
TUBEMOGUL,TubeMogul,,,{}
SAP_DIRECT_STORE_DELIVERY,SAP Direct Store Delivery,,,{}
INFOR_VISUAL,Infor VISUAL,,,{}
MIGHTYHIVE,MightyHive,,,{}
LOJA_VIRTUAL,Loja Virtual,,,{}
SHOPER,Shoper,,,{}
OMPROMPT,OmPrompt,,,{}
NVOLVE,Nvolve,,,{}
SAP_EAM,SAP EAM,,,{}
XPLENTY,Xplenty,,,{}
Q_PULSE_QMS,Q-Pulse QMS,,,{}
DIVIDO,Divido,,,{}
TUNGSTEN_NETWORK,Tungsten Network,,,{}
NEXTCHAPTER,NextChapter,,,{}
FUZZYWUZZY,FuzzyWuzzy,,,{}
CIO_DIRECT,CIO Direct,,,{}
GLOBALDATA,GlobalData,,,{}
C2FO,C2FO,,,{}
MYNEWSDESK,Mynewsdesk,,,{}
WIMI,Wimi,,,{}
RAAMP,Raamp,,,{}
COUPA_PROCUREMENT,Coupa Procurement,,,{}
PRICING_ASSISTANT,Pricing Assistant,,,{}
PLANORAMA,Planorama,,,{}
SYMANTEC_VIP,Symantec VIP,,,{}
WORKFUSION_INTELLIGENT_AUTOMATION_CLOUD,Workfusion Intelligent Automation Cloud,,,{}
EXTENDSIM,Extendsim,,,{}
COUPA_SOFTWARE,Coupa software,,,{}
SAS_VISUAL_STATISTICS,SAS Visual Statistics,,,{}
IGNITION_HMI,Ignition HMI,,,{}
XTEL,XTEL,,,{}
ALPEGA_TMS,Alpega TMS,,,{}
QUALYS_VM,Qualys VM,,,{}
WHM,WHM,,,{}
INDEAVOR_SCHEDULE,Indeavor Schedule,,,{}
IBP_360,IBP 360,,,{}
IMAGE_BUILDER,Image Builder,,,{}
DATAPROC,Dataproc,,,{}
SAP_S4HANA,SAP S4HANA,,,{}
THE_DIVER_PLATFORM,The Diver Platform,,,{}
DOT_VU,Dot.vu,,,{}
MAGAZINE_MANAGER,Magazine Manager,,,{}
SPLASHBI,SplashBI,,,{}
FRESHWORKS,Freshworks,,,{}
SEGMENTIFY,Segmentify,,,{}
ALLWORK,AllWork,,,{}
SAP_SERVICE_CLOUD,SAP Service Cloud,,,{}
SAP_CUSTOMER_EXPERIENCE_SUITE_CEC_SUITE,SAP Customer Experience Suite (CEC Suite),,,{}
FRESHDESK_MESSAGING,Freshdesk Messaging,,,{}
PERSONACLICK,PersonaClick,,,{}
LS_RETAIL,LS Retail,,,{}
KNOWBE4_SECURITY_AWARENESS_TRAINING,KnowBe4 Security Awareness Training,,,{}
EXAGRID,Exagrid,,,{}
BPOST,Bpost,,,{}
FANPAGE_KARMA,Fanpage Karma,,,{}
FREEPBX,FreePBX,,,{}
REDWOOD_SOFTWARE_RUNMYJOBS,Redwood Software RunMyJobs,,,{}
MICROSOFT_365_O365,Microsoft 365 (O365),,,{}
COPILOT_ANALYTICS,Copilot Analytics,,,{}
ASW_GEN_AI_FRAMEWORK,ASW GEN AI Framework,,,{}
TRADETRACKER,TradeTracker,,,{}
HOPSWORKS,Hopsworks,,,{}
SIGOPT,SigOpt,,,{}
MIRAGE,Mirage,,,{}
AML_INSIGHT,AML Insight,,,{}
MINERALTREE,MineralTree,,,{}
SAP_ARIBA_PAYABLES,SAP Ariba Payables,,,{}
XL_RELEASE,XL Release,,,{}
FLYWIRE,Flywire,,,{}
AUDIENSE,Audiense,,,{}
ARINCDIRECT,ARINCDirect,,,{}
FLIGHTPRO,FlightPro,,,{}
EBENEFITS,eBenefits,,,{}
FINANCIAL_ENGINES,Financial Engines,,,{}
RALLY_PLATFORM,Rally Platform,,,{}
R3_CORDA,R3 Corda,,,{}
CEQUENCE_SECURITY,Cequence Security,,,{}
BOTPRESS,Botpress,,,{}
WIT_AI,Wit.ai,,,{}
MENLO_SECURITY,Menlo Security,,,{}
GULPJS,Gulpjs,,,{}
AZURE_DDOS_PROTECTION,Azure DDoS Protection,,,{}
COPPEREGG,CopperEgg,,,{}
CONTRACTOR_S_CLOUD,Contractor's Cloud,,,{}
GENESYS_MULTICLOUD_CX,Genesys Multicloud CX,,,{}
VIRTUAL_OBSERVER,Virtual Observer,,,{}
RED_HAT_ADVANCED_CLUSTER_MANAGEMENT,Red Hat Advanced Cluster Management,,,{}
JENKINS_X,Jenkins X,,,{}
ZELLE,Zelle,,,{}
ALLCLIENTS,AllClients,,,{}
SELLF,Sellf,,,{}
MAPR_DB,MapR-DB,,,{}
IMMUTA,Immuta,,,{}
DATAGRAIL,DataGrail,,,{}
DATAGUISE,Dataguise,,,{}
YELLOWBRICK,Yellowbrick,,,{}
APACHE_KYLIN,Apache Kylin,,,{}
AZURE_INFORMATION_PROTECTION,Azure Information Protection,,,{}
ANALYST_NOTEBOOK,Analyst Notebook,,,{}
7Z,7Z,,,{}
ENTERPRISE_ENCRYPTION_MANAGEMENT_PLATFORM,Enterprise Encryption Management Platform,,,{}
IBM_INFORMATION_LIFECYCLE_GOVERNANCE,IBM Information Lifecycle Governance,,,{}
CENDYN_OVATIONS,Cendyn Ovations,,,{}
LYYTI,Lyyti,,,{}
APACHE_SAMZA,Apache Samza,,,{}
HAZELCAST_JET,Hazelcast Jet,,,{}
TRADESHIFT_GO,Tradeshift Go,,,{}
ORACLE_FINANCIAL_SERVICES_ANALYTICAL_APPLICATION_OFSAA,Oracle Financial Services Analytical Application (OFSAA),,,{}
CODAT,Codat,,,{}
CYBEROAM,Cyberoam,,,{}
CURATE_PROPOSALS,Curate Proposals,,,{}
SIGNIFYD,Signifyd,,,{}
LEXISNEXIS_EMAILAGE,LexisNexis Emailage,,,{}
QGIV,Qgiv,,,{}
THE_DIRECTOR_S_ASSISTANT,The Director's Assistant,,,{}
BRIDGER_INSIGHT_XG,Bridger Insight XG,,,{}
NUTANIX_AOS,Nutanix AOS,,,{}
PAYFONE,Payfone,,,{}
TRULIOO,Trulioo,,,{}
PAYFLEX,Payflex,,,{}
BASIS2,basis2,,,{}
DYNAMIC_INVENTORY,Dynamic Inventory,,,{}
SYSTRACK,SysTrack,,,{}
VMWARE_VREALIZE_ORCHESTRATOR,VMware vRealize Orchestrator,,,{}
PHANTOMJS,PhantomJS,,,{}
ZIPRECRUITER,ZipRecruiter,,,{}
HP_ZBOOK,HP Zbook,,,{}
CISCO_PC,Cisco PC,,,{}
SIRENA,Sirena,,,{}
MICRO_FOCUS_LOADRUNNER_CLOUD,Micro Focus LoadRunner Cloud,,,{}
CA_PLEX,CA Plex,,,{}
INRULE,InRule,,,{}
DYNAMICS_365_BUSINESS_CENTRAL,Dynamics 365 Business Central,,,{}
VIVO_MOBILE,Vivo Mobile,,,{}
AKIPS,AKIPS,,,{}
ABEND_AID,Abend-AID,,,{}
RISKDATA,Riskdata,,,{}
TRUEWORK,Truework,,,{}
HYPERSPEED,Hyperspeed,,,{}
CRYSTAL_XCELSIUS,Crystal Xcelsius,,,{}
PAYGATE,PayGate,,,{}
AMEX_EXPRESS_CHECKOUT,Amex Express Checkout,,,{}
MASTERPAYMENT,Masterpayment,,,{}
OUTPAYCE,Outpayce,,,{}
KLOCWORK,Klocwork,,,{}
RHODECODE,RhodeCode,,,{}
CORNERSTONE_PERFORMANCE,Cornerstone Performance,,,{}
ALTER,Alter,,,{}
POLICYTECH,PolicyTech,,,{}
SALESFORCE_DIGITAL_ENGAGEMENT,Salesforce Digital Engagement,,,{}
CLUBHOUSE,ClubHouse,,,{}
VACATION_TRACKER,Vacation Tracker,,,{}
PROSPECTS_CRM_BASIC,Prospects CRM (Basic),,,{}
UPDATE_CAPITAL,Update Capital,,,{}
RESY,Resy,,,{}
SUMUP,SumUp,,,{}
BIOCATCH,BioCatch,,,{}
PROCESSUNITY,ProcessUnity,,,{}
HASHICORP_CONSUL,HashiCorp Consul,,,{}
DEVZING,devZing,,,{}
SOAPUI_PRO,SoapUI Pro,,,{}
FINDBUGS,FindBugs,,,{}
POLYANALYST,PolyAnalyst,,,{}
INFORMATICA_CLOUD_INTEGRATION_HUB,Informatica Cloud Integration Hub,,,{}
IBM_INFOSPHERE_OPTIM,IBM InfoSphere Optim,,,{}
NIPENDO,Nipendo,,,{}
PROHANCE,ProHance,,,{}
APPLEARN,AppLearn,,,{}
SABRE_RED_360,Sabre Red 360,,,{}
GETTHERE,GetThere,,,{}
NOTILUS,Notilus,,,{}
GPOADMIN,GPOADmin,,,{}
ADOBE_FIREWORKS,Adobe Fireworks,,,{}
IVPN,IVPN,,,{}
WOOQER,Wooqer,,,{}
HADOOP_USER_EXPERIENCE_HUE,Hadoop User Experience (HUE),,,{}
IMPALA,Impala,,,{}
GOOGLE_CLOUD_BIGQUERY,Google Cloud (BigQuery),,,{}
HIVE_TERMINAL,Hive Terminal,,,{}
AZURE_VWAN_HUB,Azure vWAN Hub,,,{}
PALO_ALTO_CLOUD_NGFW,Palo Alto Cloud NGFW,,,{}
AZURE_NSG,Azure NSG,,,{}
UDR,UDR,,,{}
ROUTE_TABLES,Route Tables,,,{}
PRIVATE_LINK_ENDPOINT,Private Link/Endpoint,,,{}
LOAD_BALANCERS,Load Balancers,,,{}
INFOBLOX_DDI,Infoblox DDI,,,{}
EXPRESSROUTE,ExpressRoute,,,{}
PRISMA_CLOUD_COMPUTE,Prisma Cloud Compute,,,{}
SD_WAN,SD-WAN,,,{}
AWS_VPCS,AWS VPCs,,,{}
VPNS,VPNs,,,{}
TRANSIT_GATEWAY,Transit Gateway,,,{}
AZURE_PRIVATE_ENDPOINTS,Azure Private Endpoints,,,{}
OSPF,OSPF,,,{}
EIGRP,EIGRP,,,{}
BGP,BGP,,,{}
FORTINET,Fortinet,,,{}
NETCOOL,Netcool,,,{}
AWS_DATABRICKS,AWS Databricks,,,{}
AWS_QUICKSIGHT,AWS QuickSight,,,{}
AWS_CLOUDWATCH,AWS CloudWatch,,,{}
AWS_X_RAY,AWS X-Ray,,,{}
EDR_CROWDSTRIKE,EDR (CrowdStrike),,,{}
SIEM_SPLUNK,SIEM Splunk,,,{}
SPLUNK_ES,Splunk ES,,,{}
VIRUS_TOTAL,Virus Total,,,{}
URLSCAN_IO,urlscan.io,,,{}
ANY_RUN,Any Run,,,{}
MX_TOOLBOX,MX Toolbox,,,{}
MICROSOFT_DEFENDER_FOR_OFFICE_365,Microsoft Defender for Office 365,,,{}
TENABLE_NESSUS,Tenable Nessus,,,{}
CROWDSTRIKE_FALCON_PROTECTION,CrowdStrike Falcon Protection,,,{}
MANTA,Manta,,,{}
SSMS,SSMS,,,{}
APIGW,APIGW,,,{}
CISCO_WEBEX_SUITE_MEETING_PLATFORM,Cisco Webex Suite Meeting Platform,,,{}
CISCO_WEBEX_MEETING_CENTER,Cisco Webex Meeting Center,,,{}
CISCO_WEBEX_CONTROL_HUB,Cisco Webex Control Hub,,,{}
WEBEX_ASSISTANT,Webex Assistant,,,{}
PERSONAL_INSIGHTS,Personal Insights,,,{}
PEOPLE_INSIGHTS,People Insights,,,{}
XLR_RELEASE,XLR Release,,,{}
CONTENTLY_SOFTWARE,Contently software,,,{}
ADDEPAR,Addepar,,,{}
MONEYGUIDEPRO,MoneyGuidePro,,,{}
HP_UNIFIED_FUNCTIONAL_TESTING,HP Unified Functional Testing,,,{}
QTP,QTP,,,{}
M365,M365,,,{}
NCINO,nCino,,,{}
CAMPAIGN_MANAGER_PLUS,Campaign Manager +,,,{}
ARC_XP,Arc XP,,,{}
SALESFORCE_AUDIENCE_STUDIO,Salesforce Audience Studio,,,{}
WORKOS_STITCHES,WorkOS Stitches,,,{}
ZEUS_TECHNOLOGY,Zeus Technology,,,{}
INTEGRAL_AD_SCIENCE,Integral Ad Science,,,{}
PREBID_ADVERTISING,Prebid Advertising,,,{}
RADIX_UI,Radix UI,,,{}
SITEGROUND,SiteGround,,,{}
EXPRESS_NODE,Express/Node,,,{}
AWS_REDSHIFT_SQL,AWS Redshift SQL,,,{}
ROLLUP,Rollup,,,{}
WAPO_S_ELLIPSIS,WaPo's Ellipsis,,,{}
ANGLERFISH,Anglerfish,,,{}
WEBSKED,WebSked,,,{}
AVFOUNDATION,AVFoundation,,,{}
CORE_DATA,Core Data,,,{}
WIDGETKIT,WidgetKit,,,{}
COMBINE,Combine,,,{}
ODOO_ERP,Odoo ERP,,,{}
PISANO,Pisano,,,{}
BESTMIX,BESTMIX,,,{}
CORVIUM,Corvium,,,{}
CLAROTY,Claroty,,,{}
INFORMATICA_CLOUD_DATA_INTEGRATION,Informatica Cloud Data Integration,,,{}
VERSIONDOG,versiondog,,,{}
LMS_PORTALS,LMS Portals,,,{}
STORYTAP,StoryTap,,,{}
STRATPLAN,StratPlan,,,{}
TRIPTEASE,TripTease,,,{}
DRIVER_TALENT,Driver Talent,,,{}
EQUADIS,Equadis,,,{}
ADONISJS,AdonisJS,,,{}
CRM_RECRUITMENT,CRM-Recruitment,,,{}
AXOS,Axos,,,{}
TRUECLICKS,TrueClicks,,,{}
LINKFLUENCE,Linkfluence,,,{}
REVENEW,Revenew,,,{}
ZENON,zenon,,,{}
FOURKITES,FourKites,,,{}
COMMANDERS_ACT_TAGCOMMANDER,Commanders Act TagCommander,,,{}
BLUEPLANNER,BluePlanner,,,{}
GOGS,Gogs,,,{}
GENERIX_GROUP_SOLOCHAIN_WMS,Generix Group SoloChain WMS,,,{}
ITEROP,Iterop,,,{}
NATURAL_INSIGHT,Natural Insight,,,{}
STONEBRANCH,Stonebranch,,,{}
WAVEMAKER,WaveMaker,,,{}
IBM_APP_CONNECT_ACE,IBM App Connect (ACE),,,{}
IBM_WEBSPHERE_MQ,IBM WebSphere MQ,,,{}
ABB_800XA_PLATFORM,ABB 800xA platform,,,{}
COMMVAULT,Commvault,,,{}
GETIR,Getir,,,{}
GOPUFF,Gopuff,,,{}
GORILLAS,Gorillas,,,{}
AMAZON_3P,Amazon 3P,,,{}
ANDROID_SDK,Android SDK,,,{}
SOFT1_ERP,Soft1 ERP,,,{}
LIVERAMP_DATA_COLLABORATION_PLATFORM,LiveRamp Data Collaboration Platform,,,{}
RAMPID,RampID,,,{}
MACH_SOLUTION_COMMERCETOOLS,MACH-solution commercetools,,,{}
EVOLVE,Evolve,,,{}
COGNOS,Cognos,,,{}
AZURE_DATA_SERVICES,Azure Data Services,,,{}
BASECAMP,Basecamp,,,{}
LANDESK,LANDesk,,,{}
DRAWIO,DrawIO,,,{}
XFLOW,XFLow,,,{}
AMEX,AMEX,,,{}
BIBLIOSUITE,Bibliosuite,,,{}
CRESTRON,Crestron,,,{}
MICROSOFT_DEFENDER_XDR,Microsoft Defender XDR,,,{}
MICROSOFT_ENTRA_ID,Microsoft Entra ID,,,{}
VARONIS,Varonis,,,{}
GOOGLE_ADMIN_CONSOLE,Google Admin Console,,,{}
VERTEX_TAX_MANAGEMENT,Vertex Tax Management,,,{}
ERADANI_API_INTEGRATION_PLATFORM,Eradani API Integration Platform,,,{}
IBM_COGNOS_ANALYTICS_SAAS,IBM Cognos Analytics SaaS,,,{}
ORACLE_ERP,Oracle ERP,,,{}
ECLIPSE_SUBSCRIPTIONS_MANAGEMENT,Eclipse Subscriptions Management,,,{}
ANIMATE_CSS,Animate.css,,,{}
MICROSOFT_365_E5,Microsoft 365 E5,,,{}
ORACLE_E_BUSINESS_SUITE_ERP,Oracle E-Business Suite ERP,,,{}
FLYDOCS,flydocs,,,{}
DRUPAL_7,Drupal 7,,,{}
SITE_METER,Site Meter,,,{}
HAVA_IO,hava.io,,,{}
FLEETCONNECT,FleetConnect,,,{}
SEMARCHY_XDM,Semarchy xDM,,,{}
CONNECTWISE_CONTROL,ConnectWise Control,,,{}
TENSTREET,Tenstreet,,,{}
CASHMANAGER_ACCOUNTING_SOFTWARE,CashManager Accounting Software,,,{}
AWS_BUDGETS,AWS Budgets,,,{}
NETAPP_CLOUD_MANAGER,NetApp Cloud Manager,,,{}
AWS_MIGRATION_HUB,AWS Migration Hub,,,{}
MESSAGEPOINT,Messagepoint,,,{}
AZURE_PURVIEW,Azure Purview,,,{}
CLOUDENDURE_DISASTER_RECOVERY,CloudEndure Disaster Recovery,,,{}
TWILIO_SENDGRID_MARKETING_CAMPAIGNS,Twilio SendGrid Marketing Campaigns,,,{}
AVRIO,Avrio,,,{}
EQUINIX_METAL,Equinix Metal,,,{}
ACCENTURE_LIFE_INSURANCE_AND_ANNUITY_PLATFORM_ALIP,Accenture Life Insurance & Annuity Platform (ALIP),,,{}
SIMCORP_DIMENSION,SimCorp Dimension,,,{}
THINGSBOARD,ThingsBoard,,,{}
AWS_IOT_CORE,AWS IoT Core,,,{}
MOVERE,Movere,,,{}
IBM_TIVOLI_NETCOOL_OMNIBUS,IBM Tivoli NetCool/OMNIbus,,,{}
FACTORS_AI,Factors.AI,,,{}
AWS_FIREWALL_MANAGER,AWS Firewall Manager,,,{}
QUERYSURGE,QuerySurge,,,{}
ITCAM,ITCAM,,,{}
TERADATA_VANTAGE,Teradata Vantage,,,{}
UPGUARD,UpGuard,,,{}
JUNIPER_SWITCH,Juniper Switch,,,{}
CAPTERRA,Capterra,,,{}
FORTIWEB,FortiWeb,,,{}
BMC_HELIX_PLATFORM,BMC Helix Platform,,,{}
CONCIERTO_CLOUD,Concierto.Cloud,,,{}
AWS_MGN,AWS MGN,,,{}
GEMINI,Gemini,,,{}
VERTEXAI,VertexAI,,,{}
GEMINI_2_0,Gemini 2.0,,,{}
GEMINI_MULTIMODAL_API,Gemini Multimodal API,,,{}
COMPUTE_ENGINE,Compute Engine,,,{}
APP_ENGINE,App Engine,,,{}
ANGULAR_17,Angular 17,,,{}
NVIDIA_OMNIVERSE_CLOUD,NVIDIA Omniverse Cloud,,,{}
MODEL_VIEWER,model-viewer,,,{}
ALEKS,ALEKS,,,{}
QUALITY_ANALYTICS_TV_AND_RADIO_ATTRIBUTION,Quality Analytics - TV & Radio Attribution,,,{}
INTEL_SERVER,Intel Server,,,{}
TRADEWEB,Tradeweb,,,{}
MOXO,Moxo,,,{}
STORNEXT,StorNext,,,{}
SALESFORCE_CONNECT,Salesforce Connect,,,{}
STUDYTUBE,Studytube,,,{}
WORKDAY_ADAPTIVE_PLANNING,Workday Adaptive Planning,,,{}
MIRAI,Mirai,,,{}
ZOHO_CRM_PLUS,Zoho CRM Plus,,,{}
ADOBE_CREATIVE_CLOUD_EXPRESS,Adobe Creative Cloud Express,,,{}
LITERA,Litera,,,{}
MAILTESTER_COM,Mailtester.com,,,{}
CRISIS_TRACK,Crisis Track,,,{}
KMS_LIGHTHOUSE,KMS Lighthouse,,,{}
SAP_BUSINESS_BYDESIGN,SAP Business ByDesign,,,{}
TITO,Tito,,,{}
ONECOMMERCE,oneCommerce,,,{}
VMWARE_VSHIELD_EDGE,VMware vShield Edge,,,{}
VECTEEZY,Vecteezy,,,{}
VMWARE_CLOUD_DIRECTOR,VMware Cloud Director,,,{}
BUY_ME_A_COFFEE,Buy me a coffee,,,{}
GODOT,Godot,,,{}
SCALE_GENAI_PLATFORM,Scale GenAI Platform,,,{}
AI_WRITER,AI Writer,,,{}
GOOGLE_BEACON_PLAFTORM,Google Beacon Plaftorm,,,{}
1UPHEALTH,1upHealth,,,{}
LIVECOM_6G,Livecom 6G,,,{}
FISHBOWL_INVENTORY,Fishbowl Inventory,,,{}
SIQ,SiQ,,,{}
MITHRIL_JS,Mithril.js,,,{}
ANTHROPIC,Anthropic,,,{}
BLEND,Blend,,,{}
MANDIANT,Mandiant,,,{}
ISPOT_TV,ispot.tv,,,{}
TECHNEWS,TechNews,,,{}
SHOPTIMISED,Shoptimised,,,{}
SITEJABBER,Sitejabber,,,{}
FEDORA,Fedora,,,{}
TRADINGVIEW,TradingView,,,{}
DISPLAYDATA,DisplayData,,,{}
MESH_GATEWAY,Mesh Gateway,,,{}
EVERFLOW,Everflow,,,{}
NUVEI,Nuvei,,,{}
PR_COM,PR.com,,,{}
VISTAAR,Vistaar,,,{}
POSTHOG,PostHog,,,{}
AUTOTASK_PROFESSIONAL_SERVICES_AUTOMATION_PSA,Autotask Professional Services Automation (PSA),,,{}
RETAILEDGE,RetailEdge,,,{}
ZOHO_ONE,Zoho One,,,{}
KEYWORD_TOOL,Keyword Tool,,,{}
INSALES,InSales,,,{}
RADIAN6,Radian6,,,{}
ELEMENTUM,elementum,,,{}
OPENPHONE,OpenPhone,,,{}
PIPEGEN,PipeGen,,,{}
APACHE_JACKRABBIT,Apache Jackrabbit,,,{}
CARRD,Carrd,,,{}
GATSBY_CLOUD,Gatsby Cloud,,,{}
ALLPLAN,ALLPLAN,,,{}
PIWIK_PRO,Piwik PRO,,,{}
HCL_UNICA,HCL Unica,,,{}
LANSWEEPER,Lansweeper,,,{}
DOTDIGITAL,Dotdigital,,,{}
FANUC_ROBOTIC_ARMS,Fanuc robotic arms,,,{}
CISCO_VIPTELA,Cisco Viptela,,,{}
CISCO_CATALYST,Cisco Catalyst,,,{}
SDWAN,SDWAN,,,{}
LAN,LAN,,,{}
SPUNK,SPUNK,,,{}
MERAKI,Meraki,,,{}
BDPU_GUARD,BDPU guard,,,{}
PDQ,PDQ,,,{}
BITSIGHT,BitSight,,,{}
SYMANTEC,Symantec,,,{}
TARGIT,Targit,,,{}
SAGE_FIXED_ASSETS,Sage Fixed Assets,,,{}
BARTENDER,BarTender,,,{}
BENEFITSOLVER,Benefitsolver,,,{}
TEKLA_STRUCTURES,Tekla Structures,,,{}
COMPONENTS_ENGINE,Components Engine,,,{}
CARLSON_CIVIL,Carlson Civil,,,{}
HYDROCAD,HydroCAD,,,{}
OPENROADS_DESIGNER,OpenRoads Designer,,,{}
VIVIDAS,Vividas,,,{}
COMPANALYST,CompAnalyst,,,{}
HRIS,HRIS,,,{}
CSC_CORPTAX,CSC Corptax,,,{}
SQLDBM,SqlDBM,,,{}
CONCRETE_SOFTWARE,Concrete Software,,,{}
BIGCOMMERCE,BigCommerce,,,{}
KWIKTAG,KwikTag,,,{}
DRONEDEPLOY,DroneDeploy,,,{}
KESPRY,Kespry,,,{}
EVENTOGY,Eventogy,,,{}
ORACLE_FINANCIAL_CONSOLIDATION_AND_CLOSE,Oracle Financial Consolidation and Close,,,{}
WHIP_AROUND,Whip Around,,,{}
LYTX,Lytx,,,{}
ADP_SMARTCOMPLIANCE_R,ADP SmartCompliance(r),,,{}
STONEMONTQC,StonemontQC,,,{}
HAULSIM,HAULSIM,,,{}
ISNETWORLD,ISNetworld,,,{}
CLICK_BOARDING,Click Boarding,,,{}
INFOR_FACTORY_TRACK,Infor Factory Track,,,{}
RSVIEW32,RSView32,,,{}
AATRIX,Aatrix,,,{}
PRINTERLOGIC,PrinterLogic,,,{}
PLANVIEW_CLARIZEN,Planview Clarizen,,,{}
XACTIMATE,Xactimate,,,{}
GOLDSIM,Goldsim,,,{}
POWERMILL,PowerMILL,,,{}
SOURCE_INSIGHT,Source Insight,,,{}
PAGETIGER,PageTiger,,,{}
GO_CANVAS,Go Canvas,,,{}
UKG_WORKFORCE_CENTRAL,UKG Workforce Central,,,{}
NOVATIME,NOVAtime,,,{}
DELINEA_SECRET_SERVER_CLOUD,Delinea Secret Server Cloud,,,{}
NETWRIX,Netwrix,,,{}
MID_SERVER,MID Server,,,{}
COMMAND_ALKON,Command Alkon,,,{}
COUPA,Coupa,,,{}
NET_MAUI,.NET MAUI,,,{}
PROGRESS_SITEFINITY_DXP,Progress Sitefinity DXP,,,{}
SCANPRINT,ScanPrint,,,{}
FULL_CIRCLE_INSIGHTS,Full Circle Insights,,,{}
DATADOME,DataDome,,,{}
CONTRACTPODAI,ContractPodAi,,,{}
BRIGHTFLAG,Brightflag,,,{}
RAMDA,Ramda,,,{}
JSZIP,Jszip,,,{}
PERFMON,Perfmon,,,{}
IT_CENTRAL_STATION,IT Central Station,,,{}
AMAZON_TIMESTREAM,Amazon Timestream,,,{}
ZOOM_SESSIONS,Zoom Sessions,,,{}
CLOUDSHARE,CloudShare,,,{}
INSTRUQT,Instruqt,,,{}
PUPPET,Puppet,,,{}
ANSIBLE_CENTOS,Ansible (CentOS,,,{}
AMAZON_LINUX,Amazon Linux),,,{}
HELM_KUBERNETES,Helm (Kubernetes),,,{}
CONNECTWISE,ConnectWise,,,{}
TERRAFORM_TERRAGRUNT,Terraform/Terragrunt,,,{}
JAVA_J2EE,JAVA/J2EE,,,{}
RAMDAJS,RamdaJs,,,{}
JSX,JSX,,,{}
DATA_BUILD_TOOL_DBT,Data Build Tool (DBT),,,{}
SALESFORCE_AURA,Salesforce (Aura,,,{}
LIGHTNING_WEB_COMPONENTS,Lightning Web Components,,,{}
EKS_KUBERNETES,EKS/Kubernetes,,,{}
PROMETHEUS_GRAFANA,Prometheus/Grafana,,,{}
HAZELCAST,Hazelcast,,,{}
GONG_S_REVENUE_INTELLIGENCE_PLATFORM,Gong's Revenue Intelligence platform,,,{}
DAX,DAX,,,{}
MS_SENTINEL,MS Sentinel,,,{}
IPS,IPS,,,{}
VIRTUAL_GUARD_365_CCTV,Virtual Guard 365 CCTV,,,{}
ANPR_BARRIER_SYSTEM,ANPR Barrier System,,,{}
ANDROID_APP,Android App,,,{}
OVERDRIVE,OverDRIVE,,,{}
CORE,Core,,,{}
ORACLE_HYPERION_FINANCIAL_DATA_QUALITY_MANAGEMENT_FDM,Oracle Hyperion Financial Data Quality Management (FDM),,,{}
ADTELLIGENT,Adtelligent,,,{}
DOUBLECLICK,DoubleClick,,,{}
MEDIAVINE,Mediavine,,,{}
OPERATIVE,Operative,,,{}
SMARTCLIP,smartclip,,,{}
IBM_INOTES_FORMERLY_IBM_LOTUS_INOTES,IBM iNotes (formerly IBM Lotus iNotes),,,{}
MICROSOFT_EXCHANGE_SERVER,Microsoft Exchange Server,,,{}
NOTION_SOFTWARE,Notion Software,,,{}
PLANVIEW_ENTERPRISE,Planview Enterprise,,,{}
TEAMWORK,Teamwork,,,{}
DIRECTLY,Directly,,,{}
NICKELLED,Nickelled,,,{}
GENIUS_SPORTS,Genius Sports,,,{}
GOOGLE_AJAX_LIBRARIES_API,Google AJAX Libraries API,,,{}
GOOGLE_API,Google API,,,{}
IRON_MOUNTAIN,Iron Mountain,,,{}
ORACLE_REAL_APPLICATION_CLUSTERS,Oracle Real Application Clusters,,,{}
ORACLE_RECOVERY_MANAGER,Oracle Recovery Manager,,,{}
RIAK,Riak,,,{}
SARKA_SPIP,Sarka-SPIP,,,{}
WEBMETHODS,WebMethods,,,{}
APPSENSE,AppSense,,,{}
HP_THIN_CLIENTS,HP Thin Clients,,,{}
RESOLVE,Resolve,,,{}
VMWARE_ESX_SERVER,VMware ESX Server,,,{}
EFI_DIGITAL_STOREFRONT,EFI Digital StoreFront,,,{}
PAYPAL_BRAINTREE,PayPal Braintree,,,{}
F5_BIG_IP_NETWORK_HARDWARE,F5 BIG-IP Network Hardware,,,{}
IBM_AS_400,IBM AS/400,,,{}
BUILT_IN,Built In,,,{}
DOKEOS,Dokeos,,,{}
GUPY_HR_MANAGER,Gupy HR Manager,,,{}
IDEAL,Ideal,,,{}
KALLIDUS,Kallidus,,,{}
LITMOS,Litmos,,,{}
SELECTLEADERS,SelectLeaders,,,{}
TIME_AND_ATTENDANCE_ESSENTIAL,Time and Attendance eSSential,,,{}
AWS_WEB_HOSTING,AWS Web Hosting,,,{}
AMAZON_AWS,Amazon AWS,,,{}
BLACKBERRY,BlackBerry,,,{}
CSC_CORP_MAIL_SERVER,CSC Corp (mail server),,,{}
CHERWELL_IT_SERVICE_MANAGEMENT,Cherwell IT Service Management,,,{}
DAILY,Daily,,,{}
DELL_EMC_SAN,Dell EMC SAN,,,{}
DYN_MANAGED_DNS,Dyn Managed DNS,,,{}
DYN_RUM,Dyn RUM,,,{}
GOOGLE_MAIL_SERVER,Google (mail server),,,{}
IBM_POWER7,IBM POWER7,,,{}
IBM_POWERHA,IBM PowerHA,,,{}
MICROSOFT_ACTIVE_DIRECTORY,Microsoft Active Directory,,,{}
MICROSOFT_HOSTING,Microsoft Hosting,,,{}
MICROSOFT_IIS_APPLICATION_REQUEST_ROUTING_ARR,Microsoft IIS Application Request Routing (ARR),,,{}
MICROSOFT_WINDOWS_SERVER_2003,Microsoft Windows Server 2003,,,{}
NETNAMES,NetNames,,,{}
NETNAMES_LIMITED_MAIL_SERVER,NetNames Limited (mail server),,,{}
OUTLOOK_COM,Outlook.com,,,{}
SUN_SOLARIS_CLUSTER,Sun Solaris Cluster,,,{}
IBM_COGNOS_BI_ON_CLOUD,IBM Cognos BI on Cloud,,,{}
IBM_COGNOS_ENTERPRISE,IBM Cognos Enterprise,,,{}
IBM_COGNOS_SERIES_7,IBM Cognos Series 7,,,{}
L2,L2,,,{}
MICROSOFT_MODE,Microsoft Mode,,,{}
SAP_BUSINESSOBJECTS_BUSINESS_INTELLIGENCE,SAP BusinessObjects Business Intelligence,,,{}
SINGULAR,Singular,,,{}
BRIERLEY,Brierley,,,{}
BUXTON,Buxton,,,{}
CLARITAS,Claritas,,,{}
TWITTER_CARDS,Twitter Cards,,,{}
VERINT_IMPACT_360,Verint Impact 360,,,{}
YOAST,Yoast,,,{}
CIRCUIT_GPS,Circuit GPS,,,{}
LLAMASOFT_ROUTE,LLamasoft Route,,,{}
SAP_MATERIALS_MANAGEMENT,SAP Materials Management,,,{}
SAP_NETWEAVER_BPM,SAP NetWeaver BPM,,,{}
SAP_PRODUCTION_PLANNING,SAP Production Planning,,,{}
SCADA,SCADA,,,{}
CKEDITOR,CKEditor,,,{}
DUBLIN_CORE,Dublin Core,,,{}
JAVA_ENTERPRISE_EDITION,Java Enterprise Edition,,,{}
MICROSOFT_WINDOWS_POWERSHELL,Microsoft Windows PowerShell,,,{}
OPEN_GRAPH_PROTOCOL,Open Graph Protocol,,,{}
SAP_ABAP,SAP ABAP,,,{}
TWITTER_TYPEAHEADJS,Twitter TypeaheadJS,,,{}
X_UA_COMPATIBLE,X-UA-Compatible,,,{}
XHTML,XHTML,,,{}
YAHOO_USER_INTERFACE_LIBRARY_YUI,Yahoo User Interface Library (YUI),,,{}
AMBITION,Ambition,,,{}
BATCH,Batch,,,{}
BLITZEN,Blitzen,,,{}
MICROSOFT_DYNAMICS_POS,Microsoft Dynamics POS,,,{}
SIMPLYBOOK_ME,SimplyBook.me,,,{}
ZINGFIT,Zingfit,,,{}
CHECK_POINT,Check Point,,,{}
CISCO_ADAPTIVE_SECURITY_APPLIANCE_ASA,Cisco Adaptive Security Appliance (ASA),,,{}
SAP_SECURITY,SAP Security,,,{}
SAFE_SYSTEMS,Safe Systems,,,{}
SECURITY_WEAVER,Security Weaver,,,{}
ASPENTECH_PROCESS_OPTIMIZATION_SOLUTION,AspenTech Process Optimization Solution,,,{}
AUTODESK_AUTOCAD,Autodesk AutoCAD,,,{}
BUILT_DESIGN_MANAGER,Built Design Manager,,,{}
LEAN_MANUFACTURING,Lean Manufacturing,,,{}
SAP_CARBON_IMPACT_ONDEMAND,SAP Carbon Impact OnDemand,,,{}
ADOBE_TYPEKIT,Adobe Typekit,,,{}
CACHE_CONTROL,Cache Control,,,{}
COOKIEYES,Cookieyes,,,{}
ESRI_ARCGIS_SERVER,Esri ArcGIS Server,,,{}
GOOGLE_FONT_API,Google Font API,,,{}
JIVE_X,Jive-x,,,{}
TWEET_BUTTON,Tweet Button,,,{}
WEB_COM,Web.com,,,{}
IUBENDA,iubenda,,,{}
ORACLE_EPM,Oracle EPM,,,{}
OMNIVERSE,Omniverse,,,{}
ADOBE_DIMENSION,Adobe Dimension,,,{}
SUBSTANCE_DESIGNER,Substance Designer,,,{}
TOOLBAG,Toolbag,,,{}
INFOR_SUNSYSTEMS,Infor SunSystems,,,{}
DIGITAL_ID,Digital iD,,,{}
VSTITCHER,VStitcher,,,{}
DIGITAL_AI_RELEASE,Digital.ai Release,,,{}
KEMAL,Kemal,,,{}
TWINMOTION,Twinmotion,,,{}
PLAYNETWORK,PlayNetwork,,,{}
RENDER,Render,,,{}
SHIELDX,ShieldX,,,{}
AKAMAI_EDGEWORKERS,Akamai EdgeWorkers,,,{}
ESHOPWORLD,eshopworld,,,{}
SHIBUYA,Shibuya,,,{}
OPTIMIZELY_DATA_PLATFORM_FORMERLY_ZAIUS,Optimizely Data Platform (formerly Zaius),,,{}
MEETSALES,Meetsales,,,{}
MONOTOTE,Monotote,,,{}
FAST_CHECKOUT,Fast Checkout,,,{}
ALERTMEDIA,AlertMedia,,,{}
SAP_POWERDESIGNER,SAP PowerDesigner,,,{}
SERVICEONE,ServiceONE,,,{}
GAMEMAKER,GameMaker,,,{}
ONSPRING,Onspring,,,{}
TIMETRACK,TimeTrack,,,{}
CAPTIV8,Captiv8,,,{}
KLEAR,Klear,,,{}
ORACLE_SOA_CLOUD_SERVICE,Oracle SOA Cloud Service,,,{}
SOA_SUITE,SOA Suite,,,{}
IMMUTABLE,Immutable,,,{}
EPIC_CONSUMER,EPIC Consumer,,,{}
SPLUNK_LOG_OBSERVER,Splunk Log Observer,,,{}
APTELIGENT,Apteligent,,,{}
MOENGAGE,MoEngage,,,{}
AMAZON_COMPREHEND,Amazon Comprehend,,,{}
ZOCDOC,Zocdoc,,,{}
FULLCASE,FullCase,,,{}
MARKO_JS,Marko.js,,,{}
CAPTURE_ONE_PRO,Capture One Pro,,,{}
JAGGAER,Jaggaer,,,{}
DIGITAL_AI_AGILITY,Digital.ai Agility,,,{}
YODIZ,Yodiz,,,{}
PREVIEW_APP,Preview App,,,{}
ZELLO,Zello,,,{}
HIRETUAL,Hiretual,,,{}
SAP_IQ,SAP IQ,,,{}
PRICEPOINT,Pricepoint,,,{}
ZYLO,Zylo,,,{}
TRANSMART,Transmart,,,{}
HEYMARKET,Heymarket,,,{}
CENTIRO,Centiro,,,{}
FEATURED_CUSTOMERS,Featured Customers,,,{}
WORKSOFT_CERTIFY,Worksoft Certify,,,{}
QUNIT,Qunit,,,{}
GURUCUL,GuruCul,,,{}
OPTIMIZELY_CONTENT_CLOUD,Optimizely Content Cloud,,,{}
VUETIFY,Vuetify,,,{}
KRONOS_WORKFORCE_DIMENSIONS,Kronos Workforce Dimensions,,,{}
LEGION_WFM,Legion WFM,,,{}
AUTOSYS_WORKLOAD_AUTOMATION,AutoSys Workload Automation,,,{}
PINC_YARD_MANAGEMENT,PINC Yard Management,,,{}
ARTIFACTORY,Artifactory,,,{}
WORKFLOWS,Workflows,,,{}
DELTA,Delta,,,{}
AURORA_MYSQL,Aurora MySQL,,,{}
MEMORYDB,MemoryDB,,,{}
EVENTBRIDGE,EventBridge,,,{}
SNS,SNS,,,{}
AMOS,AMOS,,,{}
AZURE_BATCH,Azure Batch,,,{}
APPWAY_PLATFORM,Appway Platform,,,{}
FINPRO,FinPro,,,{}
ASSENTIS,Assentis,,,{}
AKKIO,Akkio,,,{}
POSTGRES_ENTERPRISE_MANAGER,Postgres Enterprise Manager,,,{}
APACHE_FREEMARKER,Apache FreeMarker,,,{}
MICROSOFT_BUSINESS_INTELLIGENCE_MSBI,Microsoft Business Intelligence (MSBI),,,{}
ALFRESCO_CONTENT_SERVICES,Alfresco Content Services,,,{}
ELASTIC_SECURITY,Elastic Security,,,{}
CRUSHFTP,CrushFTP,,,{}
TRADE_IDEA,Trade Idea,,,{}
COMPLYSCI,ComplySci,,,{}
SAGA_GIS,SAGA GIS,,,{}
RATIONAL_APPLICATION_DEVELOPER_FOR_WEBSPHERE_SOFTWARE,Rational Application Developer for WebSphere Software,,,{}
LOCAL_VIEW,Local View,,,{}
DYNAMICS_365_MARKETING,Dynamics 365 Marketing,,,{}
TIBCO_EBX,TIBCO EBX,,,{}
UNQORK,Unqork,,,{}
FORM_IO,Form.io,,,{}
QWIK,Qwik,,,{}
ENGAGE_PROCESS_MODELER,Engage Process Modeler,,,{}
CLOUD_SOAR,Cloud SOAR,,,{}
LENSES,Lenses,,,{}
SONATYPE,Sonatype,,,{}
UNGERBOECK,Ungerboeck,,,{}
CONTINUITY_SOFTWARE,Continuity Software,,,{}
CLOUDFLARE_WAF,Cloudflare WAF,,,{}
ALFRESCO,Alfresco,,,{}
M365_COPILOT,M365 Copilot,,,{}
AZURE_ML,Azure ML,,,{}
APACHE_PYSPARK,Apache PySpark,,,{}
CONTOUR,Contour,,,{}
WORKSHOP,Workshop,,,{}
QUIVER,Quiver,,,{}
ALIBABA,Alibaba,,,{}
MONGO,Mongo,,,{}
AZURE_KUBERNETES_SERVICES_AKS,Azure Kubernetes Services (AKS),,,{}
TESTCONTAINERS,Testcontainers,,,{}
SYNAPSE,Synapse,,,{}
PDW,PDW,,,{}
DXC_SICS,DXC SICS,,,{}
SAP_DISASTER_RECOVERY,SAP Disaster Recovery,,,{}
SAP_AUTO_SCALING,SAP Auto Scaling,,,{}
COMMVAULT_COMPLETE_BACKUP_AND_RECOVERY,Commvault Complete™ Backup & Recovery,,,{}
CISCO_FLASHSTACK,Cisco FlashStack,,,{}
PURE_STORAGE,Pure Storage,,,{}
COMODO_POSITIVESSL_CERTIFICATES,Comodo PositiveSSL Certificates,,,{}
SEVICENOW,SeviceNow,,,{}
PRONTI_ERP,Pronti ERP,,,{}
LEVER,Lever,,,{}
PAYMENTUS,Paymentus,,,{}
SCOUT_RFP,Scout RFP,,,{}
SMAPLY_CUSTOMER_EXPERIENCE_HUB,Smaply Customer Experience Hub,,,{}
THEYDO,TheyDo,,,{}
ADMINER,Adminer,,,{}
CISCO_FIREPOWER_2100_FIREWALL,Cisco Firepower 2100 Firewall,,,{}
MORTGAGE_SALES_AND_ORIGINATIONS,Mortgage Sales & Originations,,,{}
SHOWINGTIME_APPOINTMENT_CENTER,ShowingTime Appointment Center,,,{}
LOWERMYBILLS,LowerMyBills,,,{}
BRIVO,Brivo,,,{}
APPFOLIO_PROPERTY_MANAGER,AppFolio Property Manager,,,{}
REALPAGE,RealPage,,,{}
PLACEMENTS_IO,Placements.io,,,{}
SHOWMOJO,ShowMojo,,,{}
SHOPIFY_POS,Shopify POS,,,{}
CODESIGNAL,CodeSignal,,,{}
AIRDNA,AirDNA,,,{}
CONCEPTBOARD,Conceptboard,,,{}
OUTBRAIN_AMPLIFY,Outbrain Amplify,,,{}
SITECORE_ENGAGEMENT_CLOUD,Sitecore Engagement Cloud,,,{}
SEQUEL_PRO,Sequel Pro,,,{}
DETAIL_ONLINE,Detail Online,,,{}
XAKIA,Xakia,,,{}
BRIGHTREE,Brightree,,,{}
PAGEUP,PageUp,,,{}
VMWARE_SERVICE_MANAGER,VMware Service Manager,,,{}
SCHEDULEONCE,ScheduleOnce,,,{}
MATHWORKS_SIMULINK,Mathworks Simulink,,,{}
WASM,WASM,,,{}
CLOUD_WAN,Cloud WAN,,,{}
VPC_ENDPOINTS,VPC endpoints,,,{}
NACL,NACL,,,{}
SWIFTUI,SwiftUI,,,{}
SALESFORCE_CLASSIC,Salesforce Classic,,,{}
VISUAL_REMOTE_ASSISTANCE_TECHNOLOGY,Visual Remote Assistance Technology,,,{}
IBM_WEB_CONTENT_MANAGER_PLATFORM,IBM® Web Content Manager Platform,,,{}
FITTING_TO_OUTCOMES_EXPERT_FOX,Fitting to Outcomes eXpert (FOX),,,{}
MOODY_S_GGY_AXIS,Moody’s GGY Axis,,,{}
MILLIMAN_INTEGRATE,Milliman Integrate,,,{}
RANCHER,Rancher,,,{}
CONCOURSE,Concourse,,,{}
EMPTORIS,Emptoris,,,{}
TELLIUS,Tellius,,,{}
TIBCO_WEBFOCUS,TIBCO WebFOCUS,,,{}
BETTERTEAM,Betterteam,,,{}
CLOUDPAGES,CloudPages,,,{}
NIELSEN_ATTRIBUTION_FORMERLY_VISUAL_IQ,Nielsen Attribution (formerly Visual IQ),,,{}
AZURE_DISK_STORAGE,Azure Disk Storage,,,{}
AZURE_BLOCKCHAIN_WORKBENCH,Azure Blockchain Workbench,,,{}
ASSET_AND_INVESTMENT_MANAGER_AIM,Asset and Investment Manager (AIM),,,{}
BUNDELING,Bundeling,,,{}
ARENA_SIMULATION,Arena Simulation,,,{}
IBM_TURBONOMIC,IBM Turbonomic,,,{}
IBM_SECURITY_ZSECURE,IBM Security zSecure,,,{}
COGNIGY_AI,Cognigy.AI,,,{}
LECTORA,Lectora,,,{}
QUADIENT_INSPIRE,Quadient Inspire,,,{}
PINGONE_FOR_CUSTOMERS,PingOne for Customers,,,{}
MICROSOFT_PURVIEW_DATA_LOSS_PREVENTION_DLP,Microsoft Purview Data Loss Prevention (DLP),,,{}
ADVANCED_QUERY_TOOL,Advanced Query Tool,,,{}
WINSQL,WinSQL,,,{}
ORACLE_ADVANCED_SECURITY,Oracle Advanced Security,,,{}
FILE_AND_SERVEXPRESS,File and ServeXpress,,,{}
NATIONWIDE_LEGAL,Nationwide Legal,,,{}
ENCASE_ENDPOINT_SECURITY,EnCase Endpoint Security,,,{}
HYLAND_NUXEO_PLATFORM,Hyland Nuxeo Platform,,,{}
SMP_E,SMP/E,,,{}
FINDUR,Findur,,,{}
PINDROP_PASSPORT,Pindrop Passport,,,{}
GENESYS_1_USER_WEM_UPGRADE_II,Genesys 1 User WEM Upgrade II,,,{}
PEOPLECLOUD,PeopleCloud,,,{}
SAP_AGENT_PERFORMANCE_MANAGEMENT,SAP Agent Performance Management,,,{}
GUIDEWIRE_CLAIMCENTER,Guidewire ClaimCenter,,,{}
ORACLE_INSURANCE_POLICY_ADMINISTRATION,Oracle Insurance Policy Administration,,,{}
GUIDEWIRE_INSURANCENOW,Guidewire InsuranceNow,,,{}
PROMETRIX,ProMetrix,,,{}
MICROSOFT_POWER_VIRTUAL_AGENTS,Microsoft Power Virtual Agents,,,{}
INVESTRAN,Investran,,,{}
IBM_DATAPOWER_GATEWAY,IBM datapower gateway,,,{}
SERVICENOW_CUSTOMER_SERVICE_MANAGEMENT,ServiceNow Customer Service Management,,,{}
MARKED_JS,Marked.js,,,{}
PERFECTLAW,PerfectLaw,,,{}
CENTERBASE,Centerbase,,,{}
YEXT,Yext,,,{}
LANGUAGE_STUDIO,Language Studio,,,{}
KALIDO,Kalido,,,{}
SPLUNKD,Splunkd,,,{}
GOOGLE_CODE_PRETTIFY,Google Code Prettify,,,{}
UXCAM,UXCam,,,{}
SALESFORCE_SHIELD,Salesforce Shield,,,{}
POLICYBOX,PolicyBox,,,{}
REFERENCECONNECT,ReferenceConnect,,,{}
RUBYMINE,RubyMine,,,{}
AMAZON_SIMPLE_WORKFLOW_SWF,Amazon Simple Workflow (SWF),,,{}
DROOLS,Drools,,,{}
ACTIVEX,ActiveX,,,{}
MASTERTAX,MasterTax,,,{}
RUBY_PROGRAMMING_LANGUAGE,Ruby (Programming Language),,,{}
PINDROP_PROTECT,Pindrop Protect,,,{}
SALESVUE,Salesvue,,,{}
GEOPOINTE,Geopointe,,,{}
HEXAWISE,Hexawise,,,{}
RATIONAL_FUNCTIONAL_TESTER,Rational Functional Tester,,,{}
GOOGLE_CLOUD_PUB_SUB,Google Cloud Pub/Sub,,,{}
ENSIGHTEN_MANAGE,Ensighten Manage,,,{}
CASS_INFORMATION_SYSTEMS,Cass Information Systems,,,{}
BRANDMUSCLE,BrandMuscle,,,{}
RADIANT_LOGIC,Radiant Logic,,,{}
USERLEAP,UserLeap,,,{}
IBM_CLOUD_PRIVATE,IBM Cloud Private,,,{}
WORLDSPACE_COMPLY,WorldSpace Comply,,,{}
STONEBRANCH_UAC,Stonebranch UAC,,,{}
IBM_IIB_MESSAGE_BROKER,IBM IIB Message Broker,,,{}
CICS,CICS,,,{}
DB2,DB2,,,{}
Z_OS,z/OS,,,{}
GUIDEWIRE_BILLINGCENTER,Guidewire BillingCenter,,,{}
SAS_ENTERPRISSE_GUIDE,SAS Enterprisse Guide,,,{}
MUREX,Murex,,,{}
1E_PLATFORM,1E Platform,,,{}
ENDPOINT_AUTOMATION,Endpoint Automation,,,{}
EXPERIENCE_ANALYTICS,Experience Analytics,,,{}
H2O_3,H2O-3,,,{}
TIMESHIFTX,TimeShiftX,,,{}
ONCOMMAND_CLOUD_MANAGER,OnCommand Cloud Manager,,,{}
OPTIMOVE,Optimove,,,{}
POSTMAN_API_DOCUMENTATION,Postman API Documentation,,,{}
ONFIDO,Onfido,,,{}
PTC_IMPLEMENTER,PTC Implementer,,,{}
THREATMETRIX,ThreatMetrix,,,{}
DECIBEL,Decibel,,,{}
IBM_STERLING_INTEGRATOR,IBM Sterling Integrator,,,{}
STERLING_FILE_GATEWAY,Sterling File Gateway,,,{}
NETAPP_CLOUD_MANAGER_INIGHTS,Netapp Cloud Manager/Inights,,,{}
CLOUD_TIERING,Cloud Tiering,,,{}
CLOUD_BACKUPS,Cloud Backups,,,{}
SCEPTRE,Sceptre,,,{}
ORACLE_EXADATA_CLOUD_INFRASTRUCTURE_X9M,Oracle Exadata Cloud Infrastructure X9M,,,{}
APM,APM,,,{}
VODAFONE_BUSINESS_STORM,Vodafone Business storm,,,{}
CONTENT_GURU_S_STORM_PLATFORM,Content Guru's Storm Platform,,,{}
USERTESTING_PLATFORM,UserTesting Platform,,,{}
CIVICA_COMPLAINTS_MANAGEMENT,Civica Complaints Management,,,{}
WEBEX_CONNECT_PLATFORM,Webex Connect Platform,,,{}
CDATA_SOFTWARE,CData Software,,,{}
KIRA_INTRANET_PLATFORM,Kira Intranet Platform,,,{}
SIGNATURE_BANKING_PLATFORM,Signature® Banking Platform,,,{}
APPNEXUS,AppNexus,,,{}
DOUBLECLICK_CONVERSION,DoubleClick Conversion,,,{}
THE_TRADE_DESK,The Trade Desk,,,{}
ADOBE_SHOCKWAVE_PLAYER,Adobe Shockwave Player,,,{}
AVAYA_SITE_ADMINISTRATION,Avaya Site Administration,,,{}
FACEBOOK_WORKPLACE,Facebook Workplace,,,{}
QUALTRICS_SITE_INTERCEPT,Qualtrics Site Intercept,,,{}
ADOBE_IDOL,Adobe IDOL,,,{}
CITRIX_NETSCALER,Citrix NetScaler,,,{}
SOASTA,SOASTA,,,{}
SAVVIS_SYMPHONY,Savvis Symphony,,,{}
AVAYA_TELEPHONY_HARDWARE,Avaya Telephony Hardware,,,{}
BROCADE,Brocade,,,{}
ADP_ENTERPRISE_ETIME,ADP Enterprise eTime,,,{}
CENTURYLINK,CenturyLink,,,{}
CENTURYLINK_CLOUD,CenturyLink Cloud,,,{}
CITRIX_CLOUD,Citrix Cloud,,,{}
IBM_WEBSPHERE_APPLICATION_SERVER,IBM WebSphere Application Server,,,{}
MICROSOFT_IIS,Microsoft IIS,,,{}
RIVERBED,Riverbed,,,{}
SOASTA_MPULSE,SOASTA mPulse,,,{}
MONIKER_DNS,Moniker DNS,,,{}
IPV6,IPv6,,,{}
ADOBE_SITECATALYST_OMNITURE,Adobe SiteCatalyst (Omniture),,,{}
BING_UNIVERSAL_EVENT_TRACKING,Bing Universal Event Tracking,,,{}
GOOGLE_GLOBAL_SITE_TAG,Google Global Site Tag,,,{}
SAP_CRYSTAL_REPORTS,SAP Crystal Reports,,,{}
TAG_INSPECTOR,Tag Inspector,,,{}
PINTEREST_FOR_BUSINESS,Pinterest for Business,,,{}
ASP,ASP,,,{}
MICROSOFT_NET_FRAMEWORK,Microsoft .NET Framework,,,{}
XML_EXPLORER,XML Explorer,,,{}
INTEL_SECURITY_MCAFEE_SAAS_EMAIL_PROTECTION,Intel Security McAfee SaaS Email Protection,,,{}
MCAFEE_VIRUSSCAN,McAfee VirusScan,,,{}
PROVE,Prove,,,{}
VERISIGN_SSL,VeriSign SSL,,,{}
KEY_SYSTEMS,Key-Systems,,,{}
FACEBOOK_SDK,Facebook SDK,,,{}
TWITTER_FOR_WEBSITE,Twitter for Website,,,{}
CASCADE,Cascade,,,{}
ENGAGE_ATS,Engage ATS,,,{}
EPLO,Eplo,,,{}
MPZMAIL,MPZMail,,,{}
FLUTTER_DART,Flutter/Dart,,,{}
TYPESCRIPT_VUE,Typescript Vue,,,{}
TYPESCRIPT_NODE,Typescript Node,,,{}
CONVERT_EXPERIENCES,Convert Experiences,,,{}
BILLINGPLATFORM,BillingPlatform,,,{}
SALESDESK,SalesDesk,,,{}
GRAVITY_FORMS,Gravity Forms,,,{}
ROSLYN,Roslyn,,,{}
NODEJS_EXPRESS,NodeJS/Express,,,{}
AURORA_POSTGRES,Aurora Postgres,,,{}
SNS_SQS,SNS/SQS,,,{}
VANILLAJS,vanillaJS,,,{}
TIN_CAN_API,Tin Can API,,,{}
DEEPL_API,DeepL API,,,{}
SALESFORCE_EXPERIENCE_CLOUD,Salesforce Experience Cloud,,,{}
CLIENTJS,ClientJS,,,{}
REACH_ENGINE,Reach Engine,,,{}
DATOMIC,Datomic,,,{}
POWERREVIEWS,PowerReviews,,,{}
SMARTLOGIC,SmartLogic,,,{}
AMP_FRAMEWORKS,AMP Frameworks,,,{}
PACLEASE,PacLease,,,{}
BLOTOUT_EDGETAG,Blotout EdgeTag,,,{}
USERWAY,UserWay,,,{}
LIAZON,Liazon,,,{}
TRIAL_INTERACTIVE,Trial Interactive,,,{}
SKILLSBOARD,SkillsBoard,,,{}
IBM_WATSON_HEALTH,IBM Watson Health,,,{}
XTRACTA,Xtracta,,,{}
ZOHO_CREATOR,Zoho Creator,,,{}
PATHMATICS,Pathmatics,,,{}
PAPAYA_GLOBAL,Papaya Global,,,{}
ABBYY_VANTAGE,ABBYY Vantage,,,{}
ORACLE_EXALOGIC,Oracle Exalogic,,,{}
ONENAV,OneNav,,,{}
POMSNET_AQUILA_MES_SYSTEM,POMSnet Aquila MES System,,,{}
SEEQ_ORGANIZER,Seeq Organizer,,,{}
NUVOLO_PLATFORM,Nuvolo Platform,,,{}
GEA_S_CONSIGMA,GEA's ConsiGma,,,{}
DOTTRACE,dotTrace,,,{}
ZETA_PROGRAMMATIC_FORMERLY_SIZMEK,Zeta Programmatic (formerly Sizmek),,,{}
INTEGRATE_IO,Integrate.io,,,{}
HEALTHSHARE_HEALTH_CONNECT,HealthShare Health Connect,,,{}
SPICEWORKS_HELP_DESK,Spiceworks Help Desk,,,{}
MENTORCLIQ,MentorcliQ,,,{}
PRODUCTVISION,ProductVision,,,{}
FINLY,Finly,,,{}
MANAGEENGINE_SERVICEDESK_PLUS,ManageEngine ServiceDesk Plus,,,{}
QUALYS_WAS,Qualys WAS,,,{}
PROFESSIONAL_EMPLOYER_ORGANIZATION,Professional Employer Organization,,,{}
DELTA_LAKE,Delta Lake,,,{}
UNITY_CATALOG,Unity Catalog,,,{}
MICROSOFT_365_ACTIVE_DIRECTORY,Microsoft 365 Active Directory,,,{}
EKTRON,Ektron,,,{}
NASTEL,Nastel,,,{}
IBM_COREMETRICS,IBM Coremetrics,,,{}
APACHE_HUDI,Apache Hudi,,,{}
FIREMON_SECURITY_MANAGER,FireMon Security Manager,,,{}
ASPECT_UNIFIED_IP_CONTACT_CENTER,Aspect Unified IP Contact Center,,,{}
FOX_TRAINING_MANAGEMENT_SYSTEM,Fox Training Management System,,,{}
COMPLY365,Comply365,,,{}
AMOBEE,Amobee,,,{}
GEMFIRE,Gemfire,,,{}
JUMPSEAT,JumpSeat,,,{}
KISSMETRICS,KISSmetrics,,,{}
EVEREST_FORMERLY_250OK_AND_RETURN_PATH,Everest (formerly 250ok and Return Path),,,{}
EARTH_NETWORKS_SFERIC_MAPS,Earth Networks Sferic Maps,,,{}
AB_INITIO,Ab Initio,,,{}
TIBCO_BUSINESSEVENTS,TIBCO BusinessEvents,,,{}
SHAPR3D,Shapr3D,,,{}
ALERTOPS,AlertOps,,,{}
GENEXUS,GeneXus,,,{}
PTC_ARBORTEXT,PTC Arbortext,,,{}
WINBATCH,WinBatch,,,{}
PROJECTPRO,ProjectPro,,,{}
ECOLANE,Ecolane,,,{}
RISKRECON,RiskRecon,,,{}
THREADFIX,Threadfix,,,{}
VERACODE_APPLICATION_SECURITY_PLATFORM,Veracode Application Security Platform,,,{}
MICRO_FOCUS_NETIQ_IDENTITY_MANAGER,Micro Focus NetIQ Identity Manager,,,{}
RSA_IDENTITY_GOVERNANCE_AND_LIFECYCLE,RSA Identity Governance and Lifecycle,,,{}
DEFECTDOJO,DefectDojo,,,{}
OPENTEXT_TEAMSITE,OpenText TeamSite,,,{}
SITEIMPROVE,Siteimprove,,,{}
SMARTSUITE,SmartSuite,,,{}
AURORA,Aurora,,,{}
EVENT_BRIDGE,Event Bridge,,,{}
CONFLUENT_KAFKA,Confluent Kafka,,,{}
SWR,SWR,,,{}
OPENSEARCH,OpenSearch,,,{}
GOOGLE_GEMINI,Google Gemini,,,{}
NET_CORE_API,.NET Core API,,,{}
AMAZON_DATA_LAKE_FORMATION,Amazon Data Lake Formation,,,{}
SAGEMAKER_STUDIO,SageMaker Studio,,,{}
BEDROCK,Bedrock,,,{}
PAYMENT_ORCHESTRATION_PLATFORM,Payment Orchestration Platform,,,{}
ANSIBLE_AUTOMATION_PLATFORM,Ansible Automation Platform,,,{}
AIRCREW_EXCHANGE_SERVER_ACES,Aircrew Exchange Server (ACES),,,{}
AMADEUS_ALTEA_PASSENGER_SERVICE_SYSTEM_PSS,Amadeus Altea Passenger Service System (PSS),,,{}
VELOCITY_3_0,Velocity 3.0.,,,{}
TEMENOS_MULTIFONDS,Temenos Multifonds,,,{}
ORACLE_ACCOUNTING_HUB_CLOUD,Oracle Accounting Hub Cloud,,,{}
INTELLICHECK,Intellicheck,,,{}
MICRO_FOCUS_CONNECT,Micro Focus Connect,,,{}
TABLEAU_SERVER,Tableau Server,,,{}
AML_RISK_MANAGER,AML Risk Manager,,,{}
DDIQ,DDIQ,,,{}
SAS_ANTI_MONEY_LAUNDERING,SAS Anti-Money Laundering,,,{}
KONG_ENTERPRISE,Kong Enterprise,,,{}
RAPIDAPI,RapidAPI,,,{}
SAP_POWERBUILDER,SAP PowerBuilder,,,{}
SPLUNK_APM,Splunk APM,,,{}
YOUMONITOR,YouMonitor,,,{}
BMC_RELEASE_PROCESS_MANAGEMENT,BMC Release Process Management,,,{}
APACHE_GERONIMO,Apache Geronimo,,,{}
IBM_CICS,IBM CICS,,,{}
ORACLE_HTTP_SERVER,Oracle HTTP Server,,,{}
CHARGEAFTER,Chargeafter,,,{}
TZERO,tZERO,,,{}
DEALER_SOLUTIONS,Dealer Solutions,,,{}
POSTGRES_PRO,Postgres Pro,,,{}
SAS_DATA_MANAGEMENT,SAS Data Management,,,{}
BLUEDATA,BlueData,,,{}
ORACLE_BIG_DATA_CLOUD_SERVICE,Oracle Big Data Cloud Service,,,{}
PAYPANTHER,PayPanther,,,{}
CHAINALYSIS_KYT,Chainalysis KYT,,,{}
TCS_BANCS,TCS BaNCS,,,{}
TT_PLATFORM,TT Platform,,,{}
CALYPSO_PLATFORM,Calypso Platform,,,{}
CHARLES_RIVER_INVESTMENT_MANAGEMENT_SOLUTION_IMS,Charles River Investment Management Solution (IMS),,,{}
ION_TRADING,Ion Trading,,,{}
MARKETAXESS,MarketAxess,,,{}
OFFICE_CHAT,Office Chat,,,{}
ULTIMUS_BPM,Ultimus BPM,,,{}
IBM_BLUEWORKSLIVE,IBM BlueworksLive,,,{}
DEALER_FX,Dealer-FX,,,{}
RED_HAT_CLOUD,Red Hat Cloud,,,{}
ZSCALER_SECURE_ACCESS_SERVICE_EDGE_SASE,Zscaler Secure Access Service Edge (SASE),,,{}
IRONSCALES,IRONSCALES,,,{}
POWERPATH,PowerPath,,,{}
OBJECTIVEFS,ObjectiveFS,,,{}
DENSIFY,Densify,,,{}
SPOT_CLOUD_ANALYZER,Spot Cloud Analyzer,,,{}
MIGRATIONWIZ,MigrationWiz,,,{}
QUEST_MIGRATION_MANAGER,Quest Migration Manager,,,{}
ENTERPRISE_SCALR,Enterprise Scalr,,,{}
BEAUTIFULSOUP4,beautifulsoup4,,,{}
NEXIDIA_ANALYTICS,Nexidia Analytics,,,{}
AVAYA_AURA_CONTACT_CENTER,Avaya Aura Contact Center,,,{}
CANONICAL_MICROK8S,Canonical MicroK8s,,,{}
MICRO_FOCUS_DESKTOP_CONTAINERS,Micro Focus Desktop Containers,,,{}
EX_CO,EX.CO,,,{}
FLEXCUBE,Flexcube,,,{}
COREBANK,CoreBank,,,{}
PEGA_CUSTOMER_RELATIONSHIP_MANAGEMENT,Pega Customer Relationship Management,,,{}
CONFIRMO,Confirmo,,,{}
BRYTERCX_JOURNEY_MANAGEMENT_SUITE,BryterCX Journey Management Suite,,,{}
PHISHLABS,PhishLabs,,,{}
GLOBAL_IDS_DATA_ECOSYSTEM_SUITE,Global IDs Data Ecosystem Suite,,,{}
VERITAS_DATA_INSIGHT,Veritas Data Insight,,,{}
APACHE_ATLAS,Apache Atlas,,,{}
TRIFACTA,Trifacta,,,{}
ORACLE_DATA_QUALITY,Oracle Data Quality,,,{}
TALEND_DATA_QUALITY,Talend Data Quality,,,{}
ANACONDA_ENTERPRISE,Anaconda Enterprise,,,{}
IBM_WATSONX_AI,IBM watsonx.ai,,,{}
WIPRO_HOLMES,Wipro Holmes,,,{}
IBM_INFOSPHERE_DATA_REPLICATION,IBM InfoSphere Data Replication,,,{}
ITRS_GENEOS,ITRS Geneos,,,{}
DATAFOLD,Datafold,,,{}
RED_HAT_DECISION_MANAGER,Red Hat Decision Manager,,,{}
LENOVO_DESKTOP,Lenovo Desktop,,,{}
ORACLE_INFINITY,Oracle Infinity,,,{}
ANALYTICS_TOOLKIT,Analytics Toolkit,,,{}
NETX,NetX,,,{}
FINTECH_GROUP,Fintech Group,,,{}
DEPOSIT_SOLUTIONS,Deposit Solutions,,,{}
LIVEBANK,LiveBank,,,{}
NCINO_BANK_OPERATING_SYSTEM,nCino Bank Operating System,,,{}
KNOW_YOUR_CUSTOMER,Know Your Customer,,,{}
QUEST_RECOVERY_MANAGER,Quest Recovery Manager,,,{}
INVENIO,Invenio,,,{}
WEALTH_X_INTEGRATION,Wealth-X Integration,,,{}
PARTICULAR_AUDIENCE,Particular Audience,,,{}
COFENSE_VISION,Cofense Vision,,,{}
PROOFPOINT_TARGETED_ATTACK_PROTECTION_FOR_PERSONAL_WEBMAIL,Proofpoint Targeted Attack Protection For Personal Webmail,,,{}
BLACKBERRY_WORK,BlackBerry Work,,,{}
YAHOO_MAIL,Yahoo Mail,,,{}
STENSUL,stensul,,,{}
CRISIS_COMMANDER,Crisis Commander,,,{}
EMERGENCY_MANAGEMENT_SOFTWARE_SYSTEM,Emergency Management Software System,,,{}
HIFIVES,HiFives,,,{}
PERCEPTYX,Perceptyx,,,{}
ENGAGEWITH,EngageWith,,,{}
COUNTERACT,CounterACT,,,{}
HCL_BIGFIX,HCL BigFix,,,{}
KASPERSKY_ENDPOINT_SECURITY,Kaspersky Endpoint Security,,,{}
HOPEX,Hopex,,,{}
CONTENT_MANAGER_ONDEMAND_CMOD,Content Manager OnDemand (CMOD),,,{}
BLOOMBERG_VAULT,Bloomberg Vault,,,{}
WOLTERS_KLUWER_PASSPORT,Wolters Kluwer Passport,,,{}
GITBOOK,Gitbook,,,{}
CONNECT_SPACE,Connect Space,,,{}
QUEST_SOFTWARE,Quest Software,,,{}
ADOBE_ACROBAT_READER,Adobe Acrobat Reader,,,{}
SMART_AUDIT,Smart Audit,,,{}
RECONART,ReconArt,,,{}
ACTIMIZE,Actimize,,,{}
LINKURIOUS,Linkurious,,,{}
OUTSEER_FRAUD_MANAGER,Outseer Fraud Manager,,,{}
ENDUR,Endur,,,{}
SAS_FRAUD_MANAGEMENT,SAS Fraud Management,,,{}
COHESIVE,Cohesive,,,{}
IBM_HARDWARE_MANAGEMENT_CONSOLE_HMC,IBM Hardware Management Console (HMC),,,{}
PARASCRIPT,Parascript,,,{}
CITI_PROGRAM,CITI Program,,,{}
ONTAP_SELECT,ONTAP Select,,,{}
RED_HAT_FUSE,Red Hat Fuse,,,{}
OMADA_IDENTITY_SUITE,Omada Identity Suite,,,{}
SILVERFORT,Silverfort,,,{}
TRANSMIT_SECURITY,Transmit Security,,,{}
SOCURE,Socure,,,{}
CALLACTION,CallAction,,,{}
AMELIA,Amelia,,,{}
MORNINGSTAR_DIRECT,Morningstar Direct,,,{}
FIDESSA,Fidessa,,,{}
GENEVA,Geneva,,,{}
INVESTOR_INTELLIGENCE,Investor Intelligence,,,{}
ADAPTRIS,Adaptris,,,{}
FINSEMBLE,Finsemble,,,{}
DATA_PLATFORM,Data Platform,,,{}
OPCON,OpCon,,,{}
VFUNCTION,vFunction,,,{}
APACHE_ARROW,Apache Arrow,,,{}
CODENVY,Codenvy,,,{}
APACHE_TEZ,Apache Tez,,,{}
JAVASERVER_FACES_JSF,JavaServer Faces (JSF),,,{}
IBM_LAPTOP,IBM Laptop,,,{}
LENOVO_LAPTOP,Lenovo Laptop,,,{}
EMINER,eMiner,,,{}
FIRM_CENTRAL,Firm Central,,,{}
LASERPRO,LaserPro,,,{}
LENDING_HOME,Lending Home,,,{}
MERIDIANLINK_MORTGAGE,MeridianLink Mortgage,,,{}
SOLIDATUS,Solidatus,,,{}
RUN_AI,Run.AI,,,{}
SAS_PRODUCTION_QUALITY_ANALYTICS,SAS Production Quality Analytics,,,{}
VOYADO,Voyado,,,{}
TALEND_MDM,Talend MDM,,,{}
CERNER,Cerner,,,{}
NASTEL_NAVIGATOR,Nastel Navigator,,,{}
PUBSUB,PubSub+,,,{}
TIBCO_FTL,TIBCO FTL,,,{}
TIBCO_MESSAGING,Tibco Messaging,,,{}
TIBCO_RENDEZVOUS,TIBCO Rendezvous,,,{}
APPDOME,Appdome,,,{}
BLACKBERRY_DYNAMICS_PLATFORM,BlackBerry Dynamics Platform,,,{}
AZURE_SERVICE_HEALTH,Azure Service Health,,,{}
DX_NETOPS,DX NetOps,,,{}
VREALIZE_NETWORK_INSIGHT,vRealize Network Insight,,,{}
SKYBOX_FIREWALL_ASSURANCE,Skybox Firewall Assurance,,,{}
IBM_CLOUD_OBJECT_STORAGE,IBM Cloud Object Storage,,,{}
QC_DATABASE,QC Database,,,{}
MICRO_FOCUS_VERASTREAM,Micro Focus Verastream,,,{}
HONEYBOOK,HoneyBook,,,{}
CORPORATE_FINANCE_INSTITUTE,Corporate Finance Institute,,,{}
KEEPA,Keepa,,,{}
PALM_OS,Palm OS,,,{}
MODELRISK,ModelRisk,,,{}
FICO_DECISION_MANAGEMENT_PLATFORM,FICO Decision Management Platform,,,{}
IBM_DB2_ANALYTICS_ACCELERATOR,IBM Db2 Analytics Accelerator,,,{}
GRUNTJS,Gruntjs,,,{}
PIP_PYTHON,pip python,,,{}
PYTHON_CELERY,python celery,,,{}
TREESIZE,Treesize,,,{}
OPTIMISE_MEDIA,Optimise Media,,,{}
IDENTITY_RESOLUTION_ENGINE,Identity Resolution Engine,,,{}
NAVIPLAN,Naviplan,,,{}
HEDGETEK,HedgeTek,,,{}
TLM_PLATFORM,TLM Platform,,,{}
APACHE_ZEPPELIN,Apache Zeppelin,,,{}
ONTRACK_POWERCONTROLS,Ontrack PowerControls,,,{}
LEXIS_DILIGENCE_R,Lexis Diligence(r),,,{}
IGRAPH,igraph,,,{}
IMS,IMS,,,{}
DPLAYER,DPlayer,,,{}
ORACLE_UTILITIES_APPLICATION_FRAMEWORK_OUAF,Oracle Utilities Application Framework (OUAF),,,{}
PHOENIX_FRAMEWORK,Phoenix Framework,,,{}
SENDSUITE,SendSuite,,,{}
ARCON_SECURE_COMPLIANCE_MANAGEMENT_SCM,ARCON | Secure Compliance Management (SCM,,,{}
PAYMENT_EXPRESS,Payment Express,,,{}
WIRECARD,Wirecard,,,{}
PAYCHEX_FLEX,Paychex Flex,,,{}
KOHANA,Kohana,,,{}
HIRE_SUCCESS,Hire Success,,,{}
THINPRINT,ThinPrint,,,{}
PROCESSPRO,ProcessPro,,,{}
GEP_SMART,GEP SMART,,,{}
PPM_EXPRESS,PPM Express,,,{}
PLANVIEW_LEANKIT,Planview LeanKit,,,{}
PROJECTPLACE,Projectplace,,,{}
FINEOS,FINEOS,,,{}
INTERACTA,Interacta,,,{}
ORACLE_SECURE_GLOBAL_DESKTOP,Oracle Secure Global Desktop,,,{}
PROGNOSIS,PROGNOSIS,,,{}
IBM_RATIONAL_REQUISITEPRO,IBM Rational RequisitePro,,,{}
FREEDOM_PAY,Freedom Pay,,,{}
ORACLE_REVENUE_MANAGEMENT_AND_BILLING_ORMB,Oracle Revenue Management and Billing (ORMB),,,{}
APACHE_SENTRY,Apache Sentry,,,{}
RSA_ADAPTIVE_AUTHENTICATION,RSA Adaptive Authentication,,,{}
SKYBOX_VULNERABILITY_CONTROL,Skybox Vulnerability Control,,,{}
BLUE_PRISM_RPA_ROBOTIC_PROCESS_AUTOMATION,Blue Prism - RPA | Robotic Process Automation,,,{}
ROCKETBOT,Rocketbot,,,{}
CISCO_ROUTER_ISR_2900,Cisco Router ISR 2900,,,{}
NEW_VELOCITY,New Velocity,,,{}
CENTRICAL,Centrical,,,{}
RELPRO,RelPro,,,{}
NTENT,NTENT,,,{}
BLACKBERRY_ACCESS,BlackBerry Access,,,{}
RSA_NETWITNESS,RSA NetWitness,,,{}
QUALYS_CLOUD_PLATFORM,Qualys Cloud Platform.,,,{}
IBM_POWER_VM,IBM Power VM,,,{}
ORACLE_SSO,Oracle SSO,,,{}
SYMANTEC_SITEMINDER,Symantec SiteMinder,,,{}
SCRIPTRUNNER_FOR_JIRA,ScriptRunner for Jira,,,{}
ZEPHYR_ENTEPRISE,Zephyr Enteprise,,,{}
P_O_C_SYSTEM,P.O.C. System,,,{}
VISA_INTELLILINK,Visa IntelliLink,,,{}
HANDSONTABLE,Handsontable,,,{}
ERECRUIT,erecruit,,,{}
NOWSECURE,NowSecure,,,{}
LUME,Lume,,,{}
SAS_ODS,SAS ODS,,,{}
DELL_EMC_POWERSCALE_ISILON,Dell EMC PowerScale/Isilon,,,{}
VERITAS_NETBACKUP_APPLIANCES,Veritas NetBackup Appliances,,,{}
LOGSTREAM,LogStream,,,{}
LIVEVOX,LiveVox,,,{}
DIFFBLUE_COVER,Diffblue Cover,,,{}
MONACO_EDITOR,Monaco Editor,,,{}
IBM_LINUXONE,IBM LinuxONE,,,{}
GLOBALLINK,GlobalLink,,,{}
MONEX,Monex,,,{}
BLACKBERRY_UEM,BlackBerry UEM,,,{}
ONESITE,OneSite,,,{}
URBANFOOTPRINT,UrbanFootprint,,,{}
VERIS,Veris,,,{}
IBM_WATSON_SPEECH_TO_TEXT,IBM Watson Speech to Text,,,{}
GOOGLE_CLOUD_SPEECH_TO_TEXT,Google Cloud Speech-to-Text,,,{}
BUSINESS_CATALYST,Business Catalyst,,,{}
REDIS_ENTERPRISE,Redis Enterprise,,,{}
NCACHE,NCache,,,{}
ORACLE_IDENTITY_ANALYTICS_OIA,Oracle Identity Analytics (OIA),,,{}
NICE_WORKFORCE_MANAGEMENT,NICE Workforce Management,,,{}
SAVIOM,Saviom,,,{}
CA_WORKLOAD_AUTOMATION,CA Workload Automation,,,{}
IBM_WEBSPHERE_AUTOMATION,IBM Websphere Automation,,,{}
IBM_WORKLOAD_AUTOMATION,IBM Workload Automation,,,{}
CKEDITOR_5,CKEditor 5,,,{}
FORESCOUT_PLATFORM,Forescout Platform,,,{}
GOOGLE_VERTEXAI,Google VertexAI,,,{}
PGVECTOR,pgvector,,,{}
PYDANTIC,Pydantic,,,{}
AUTOSYS,Autosys,,,{}
UDEPLOY,UDeploy,,,{}
BIG_DATA,Big Data,,,{}
SPRING_DATA_JPA,Spring data JPA,,,{}
METACO_HARMONIZE,Metaco Harmonize,,,{}
AMAZON_CODEWHISPERER,Amazon CodeWhisperer,,,{}
INTERLINK_SOFTWARE_AIOPS_PLATFORM,Interlink Software AIOps Platform,,,{}
CODEBEAMER,codeBeamer,,,{}
HABITAT,Habitat,,,{}
EASTNETS,EastNets,,,{}
ROUTABLE,Routable,,,{}
APIMATIC,APIMatic,,,{}
DYNATRACE_ONEAGENT,Dynatrace OneAgent,,,{}
FIREBLOCKS,Fireblocks,,,{}
APACHE_MXNET_INCUBATING_CONTAINER_SOLUTION,Apache MXNet (Incubating) Container Solution,,,{}
PUPPETEER_BROWSER_AUTOMATION_ON_WINDOWS,Puppeteer Browser Automation on Windows,,,{}
EPAM_SYSTEMS,Epam systems,,,{}
TRANSACTIS,Transactis,,,{}
MASTERCARD_BLOCKCHAIN,Mastercard Blockchain,,,{}
CONSENSYS_QUORUM,Consensys Quorum,,,{}
AZURE_BOT_SERVICE,Azure Bot Service,,,{}
HYTRUST_CLOUD_CONTROL,HyTrust Cloud Control,,,{}
AZURE_COST_MANAGEMENT,Azure Cost Management,,,{}
CYBERDUCK,Cyberduck,,,{}
VMWARE_INTEGRATED_OPENSTACK,VMware Integrated OpenStack,,,{}
CROWNPEAK_UNIVERSAL_CONSENT_PLATFORM_FORMERLY_EVIDON,Crownpeak Universal Consent Platform (formerly Evidon),,,{}
MASTT,Mastt,,,{}
RUNSCOPE,Runscope,,,{}
ORACLE_TAX_REPORTING_CLOUD,Oracle Tax Reporting Cloud,,,{}
SESSIONM,SessionM,,,{}
TRUATA,Truata,,,{}
ORACLE_VIRTUALIZATION,Oracle Virtualization,,,{}
IDASHBOARDS,iDashboards,,,{}
ENTIRE_ACCESS,Entire Access,,,{}
DIGIFY,Digify,,,{}
DRAWPLUS,DrawPlus,,,{}
VIABILL,ViaBill,,,{}
ZID,Zid,,,{}
RELATIVITY,Relativity,,,{}
AWS_CLOUDHSM,AWS CloudHSM,,,{}
SENTINELONE_SINGULARITY,SentinelOne Singularity,,,{}
VMWARE_CARBON_BLACK_APP_CONTROL,VMware Carbon Black App Control,,,{}
BILLDESK,Billdesk,,,{}
GLOBAL_SHARES,Global Shares,,,{}
SCHOLAR,Scholar,,,{}
FRAME_AI,Frame AI,,,{}
MASTERCARD_API,MasterCard API,,,{}
PALO_ALTO_NETWORKS_CLOUD_NGFW,Palo Alto Networks Cloud NGFW,,,{}
EKATA,Ekata,,,{}
ETHOCA,Ethoca,,,{}
DATACLOUD,DataCloud,,,{}
METRICSTREAM,MetricStream,,,{}
HYPR,HYPR,,,{}
LAYBUY,Laybuy,,,{}
SPLITIT,Splitit,,,{}
APACHE_WICKET,Apache Wicket,,,{}
JAKARTA_PERSISTENCE,Jakarta Persistence,,,{}
ALERTIFY_JS,Alertify Js,,,{}
AZURE_REDIS_CACHE,Azure Redis Cache,,,{}
LEADPAGES,Leadpages,,,{}
KICKFIRE,KickFire,,,{}
WEB_SCRAPER,Web Scraper,,,{}
F5_NGINX,F5 NGINX,,,{}
DEFI_SOLUTIONS,defi SOLUTIONS,,,{}
SOPRA_BANKING_SUITE,Sopra Banking Suite,,,{}
CUCKOO_SANDBOX,Cuckoo Sandbox,,,{}
RED_CANARY,Red Canary,,,{}
GLOBALSCAPE_ENHANCED_FILE_TRANSFER,GlobalSCAPE Enhanced File Transfer,,,{}
INAP_CLOUD,INAP Cloud,,,{}
CLEVERTAP,CleverTap,,,{}
DEXGUARD,DexGuard,,,{}
SCRUTINIZER,Scrutinizer,,,{}
TALMUNDO,Talmundo,,,{}
GEOIP,GeoIP,,,{}
SIDEKIQ,Sidekiq,,,{}
SHODAN,Shodan,,,{}
YOUGOV_BRANDINDEX,YouGov BrandIndex,,,{}
PAYCARGO,PayCargo,,,{}
IMPARTNER_PRM,Impartner PRM,,,{}
PAYMENT_SERVICE_MANAGEMENT,Payment Service Management,,,{}
WAY4,Way4,,,{}
AMAZON_PAYMENT_SERVICES,Amazon Payment Services,,,{}
PINELABS,PineLabs,,,{}
OMISE,Omise,,,{}
Q_RESEARCH_SOFTWARE,Q Research Software,,,{}
SOLIDITY,Solidity,,,{}
CITRIX_PODIO,Citrix Podio,,,{}
RFP360,RFP360,,,{}
IZETTLE,iZettle,,,{}
RECORDED_FUTURE_SECURITY_INTELLIGENCE_PLATFORM,Recorded Future Security Intelligence Platform,,,{}
THE_CYBERMANIACS,The Cybermaniacs,,,{}
TRIPWIRE_ENTERPRISE,Tripwire Enterprise,,,{}
VELERO,Velero,,,{}
VIRTUOZZO,Virtuozzo,,,{}
VMWARE_DISTRIBUTED_RESOURCE_SCHEDULER_DRS,VMware Distributed Resource Scheduler (DRS),,,{}
FEATURE_FM,feature.fm,,,{}
FLEXNET_MANAGER_SUITE,FlexNet Manager Suite,,,{}
LINQPAD,LINQPad,,,{}
QACOMPLETE,QAComplete,,,{}
QA_TOUCH,QA Touch,,,{}
ROBOBAI,robobai,,,{}
HOOKIT,Hookit,,,{}
APIIRO,Apiiro,,,{}
DATA_AND_STATISTICS,Data and Statistics,,,{}
YDATA,YData,,,{}
RANOREX_STUDIO,Ranorex Studio,,,{}
TREASURY_INTELLIGENCE_SOLUTIONS,Treasury Intelligence Solutions,,,{}
DISCUSS_IO,Discuss.io,,,{}
FLYWAY,Flyway,,,{}
EASYLOBBY,EasyLobby,,,{}
CRAFTER_CMS,Crafter CMS,,,{}
VAULT_PLATFORM,Vault Platform,,,{}
DAILYKARMA,DailyKarma,,,{}
JUSTINMIND,justinmind,,,{}
CLOUDVISION,CloudVision,,,{}
IBM_WORKLOAD_SCHEDULER_TWS,IBM Workload Scheduler (TWS),,,{}
RALLY,Rally,,,{}
AHA,Aha,,,{}
AZURE_DEVOPS_ADO,Azure DevOps (ADO),,,{}
POSTGRESQL_FLEXIBLE_SERVER,PostgreSQL Flexible Server,,,{}
CISCO_DUO,Cisco DUO,,,{}
IBM_FLRT,IBM FLRT,,,{}
JCL,JCL,,,{}
NETSCALER,NetScaler,,,{}
ZIA_ZSCALER_INTERNET_ACCESS,ZIA (Zscaler Internet Access),,,{}
ZPA_ZSCALER_PRIVATE_ACCESS,ZPA (Zscaler Private Access),,,{}
CISCO_ASA_FIREPOWER,Cisco ASA Firepower,,,{}
CHECKPOINT_CLUSTERXL,Checkpoint ClusterXL,,,{}
SQL_MANAGEMENT_STUDIO,SQL Management Studio,,,{}
AUTO_CAD,Auto CAD,,,{}
CREO,Creo,,,{}
ORACLE_HYPERION_FINANCIAL_MANAGEMENT,Oracle Hyperion Financial Management,,,{}
SAP_FI_CO,SAP FI/CO,,,{}
AMPLA,Ampla,,,{}
ORIEN,Orien,,,{}
ASSETWISE,AssetWise,,,{}
ASPENTECH,AspenTech,,,{}
SANDVIK_OPTIMINE,Sandvik OptiMine,,,{}
CLARIZEN,Clarizen,,,{}
PINGBACK_SUPPORT,Pingback Support,,,{}
ZOHO_CHAT,Zoho Chat,,,{}
SYBASE,Sybase,,,{}
EPIANCE,Epiance,,,{}
MICROSOFT_TEAM_FOUNDATION_SERVER_TFS,Microsoft Team Foundation Server (TFS),,,{}
ATLANTIS,Atlantis,,,{}
ACXIOM,Acxiom,,,{}
INMAGIC,Inmagic,,,{}
INVESTIS,Investis,,,{}
MARIN,Marin,,,{}
SYTE,Syte,,,{}
VALONA,Valona,,,{}
FROTCOM,Frotcom,,,{}
SAP_PM,SAP PM,,,{}
OPTIMINE,OptiMine,,,{}
SAP_EXTENDED_WAREHOUSE_MANAGEMENT,SAP Extended Warehouse Management,,,{}
RPCONNECT,RPConnect,,,{}
SALESFORCE_CUSTOMER_360,Salesforce Customer 360,,,{}
ADAPTIVEWORK,AdaptiveWork,,,{}
