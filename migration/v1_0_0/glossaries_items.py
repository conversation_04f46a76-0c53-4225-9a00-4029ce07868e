from quanthub.structures import MigrationArtefacts as Artefacts

from .glossaries import *

Artefacts.add_terms_from_csv(epam_awards_and_recognitions_csr, "assets/epam_awards_and_recognitions_csr.csv")

Artefacts.add_terms_from_csv(epam_business_unit_sr, "assets/epam_business_unit_sr.csv")

Artefacts.add_terms_from_csv(epam_company_names_sr, "assets/epam_company_names_sr.csv")

Artefacts.add_terms_from_csv(epam_company_type_sr, "assets/epam_company_type_sr.csv")

Artefacts.add_terms_from_csv(epam_current_engagement_status, "assets/epam_current_engagement_status.csv")

Artefacts.add_terms_from_csv(epam_customer_code_sr, "assets/epam_customer_code_sr.csv")

Artefacts.add_terms_from_csv(epam_events_subcategory_sr, "assets/epam_events_subcategory_sr.csv")

Artefacts.add_terms_from_csv(epam_events_title_sr, "assets/epam_events_title_sr.csv")

Artefacts.add_terms_from_csv(epam_executive_movement_type_sr, "assets/epam_executive_movement_type_sr.csv")

Artefacts.add_terms_from_csv(epam_financial_metrics_sr, "assets/epam_financial_metrics_sr.csv")

Artefacts.add_terms_from_csv(
    epam_funding_rounds_transaction_name_sr, "assets/epam_funding_rounds_transaction_name_sr.csv"
)

Artefacts.add_terms_from_csv(epam_gbu, "assets/epam_gbu.csv")

Artefacts.add_terms_from_csv(epam_industries_sr, "assets/epam_industries_sr.csv")

Artefacts.add_terms_from_csv(epam_it_initiatives, "assets/epam_it_initiatives.csv")

Artefacts.add_terms_from_csv(epam_it_workforce_details_name_sr, "assets/epam_it_workforce_details_name_sr.csv")

Artefacts.add_terms_from_csv(epam_it_workforce_details_sr, "assets/epam_it_workforce_details_sr.csv")

Artefacts.add_terms_from_csv(epam_job_titles_sr, "assets/epam_job_titles_sr.csv")

Artefacts.add_terms_from_csv(epam_news_category_sr, "assets/epam_news_category_sr.csv")

Artefacts.add_terms_from_csv(epam_offerings_by_category, "assets/epam_offerings_by_category.csv")

Artefacts.add_terms_from_csv(epam_offerings_by_category_hierarchy, "assets/epam_offerings_by_category_hierarchy.csv")

Artefacts.add_terms_from_csv(epam_offerings_by_industry, "assets/epam_offerings_by_industry.csv")

Artefacts.add_terms_from_csv(epam_offerings_by_services, "assets/epam_offerings_by_services.csv")

Artefacts.add_terms_from_csv(epam_outsourcing_workloads_sr, "assets/epam_outsourcing_workloads_sr.csv")

Artefacts.add_terms_from_csv(
    epam_partnerships_and_acquisitions_category, "assets/epam_partnerships_and_acquisitions_category.csv"
)

Artefacts.add_terms_from_csv(epam_products_and_services_sr, "assets/epam_products_and_services_sr.csv")

Artefacts.add_terms_from_csv(epam_project_category_sr, "assets/epam_project_category_sr.csv")

Artefacts.add_terms_from_csv(epam_project_type_sr, "assets/epam_project_type_sr.csv")

Artefacts.add_terms_from_csv(epam_record_type_sr, "assets/epam_record_type_sr.csv")

Artefacts.add_terms_from_csv(epam_risks_and_challenges_sr, "assets/epam_risks_and_challenges_sr.csv")

Artefacts.add_terms_from_csv(epam_skills_sr, "assets/epam_skills_sr.csv")

Artefacts.add_terms_from_csv(epam_solutions___capabilities, "assets/epam_solutions___capabilities.csv")

Artefacts.add_terms_from_csv(
    epam_strategy_risks_and_initiatives_category_sr, "assets/epam_strategy_risks_and_initiatives_category_sr.csv"
)

Artefacts.add_terms_from_csv(epam_strategy_sr, "assets/epam_strategy_sr.csv")

Artefacts.add_terms_from_csv(
    epam_strategy__risks_and_initiatives_subcategory_sr,
    "assets/epam_strategy__risks_and_initiatives_subcategory_sr.csv",
)

Artefacts.add_terms_from_csv(epam_target_account_list_sr, "assets/epam_target_account_list_sr.csv")

Artefacts.add_terms_from_csv(epam_technology_tools_sr, "assets/epam_technology_tools_sr.csv")

Artefacts.add_terms_from_csv(epam_tech_stack_category_sr, "assets/epam_tech_stack_category_sr.csv")

Artefacts.add_terms_from_csv(sr_job_titles_id, "assets/sr_job_titles_id.csv")

Artefacts.add_terms_from_csv(epam_top_metrics_breakdown_sr, "assets/epam_top_metrics_breakdown_sr.csv")

Artefacts.add_terms_from_csv(sr_events_hierarchy_test, "assets/sr_events_hierarchy_test.csv")

Artefacts.add_terms_from_csv(sr_industries_hierarchy, "assets/sr_industries_hierarchy.csv")

Artefacts.add_terms_from_csv(sr_location_hierarchy, "assets/sr_locations_hierarachy.csv")

# Artefacts.add_terms_from_csv(sr_location_hierarchy, "assets/sr_locations_hierarachy.csv")

Artefacts.add_terms_from_csv(sr_problem_statement_driver, "assets/sr_problem_statement_driver.csv")

Artefacts.add_terms_from_csv(sr_project_type, "assets/sr_project_type.csv")

Artefacts.add_terms_from_csv(sr_status_of_the_research_sr_account, "assets/sr_status_of_the_research_sr_account.csv")

Artefacts.add_terms_from_csv(sr_tabs_sr_account, "assets/sr_tabs_sr_account.csv")

Artefacts.add_terms_from_csv(
    epam_financial_metrics_business_category_sr, "assets/epam_financial_metrics_business_category_sr.csv"
)

Artefacts.add_terms_from_csv(
    epam_financial_metrics_region_category_sr, "assets/epam_financial_metrics_region_category_sr.csv"
)

Artefacts.add_terms_from_csv(epam_financial_metrics_attribute_sr, "assets/epam_financial_metrics_attribute_sr.csv")

Artefacts.add_terms_from_csv(epam_currency_code_sr, "assets/se-a_currency_code.csv")

Artefacts.add_terms_from_csv(sr_cached_item_review, "assets/se-a_location_review.csv")
