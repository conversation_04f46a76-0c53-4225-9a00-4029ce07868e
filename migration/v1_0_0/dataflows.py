from quanthub.structures import MigrationArtefacts as Artefacts

from . import dsds

it_workforce_locations = Artefacts.create_dataflow_from_dsd(dsds.it_workforce_locations)
general_overview = Artefacts.create_dataflow_from_dsd(dsds.general_overview)
funding_rounds = Artefacts.create_dataflow_from_dsd(dsds.funding_rounds)
current_engagements = Artefacts.create_dataflow_from_dsd(dsds.current_engagements)
company_entities = Artefacts.create_dataflow_from_dsd(dsds.company_entities)
financials_raw = Artefacts.create_dataflow_from_dsd(dsds.financials_raw)
financials_by_company = Artefacts.create_dataflow_from_dsd(dsds.financials_by_company)
financials_by_company_and_location = Artefacts.create_dataflow_from_dsd(dsds.financials_by_company_and_location)
financials_by_company_and_vertical = Artefacts.create_dataflow_from_dsd(dsds.financials_by_company_and_vertical)
business_struct = Artefacts.create_dataflow_from_dsd(dsds.business_struct)
featured_clients = Artefacts.create_dataflow_from_dsd(dsds.featured_clients)
events = Artefacts.create_dataflow_from_dsd(dsds.events)
tech_stack = Artefacts.create_dataflow_from_dsd(dsds.tech_stack)
projects = Artefacts.create_dataflow_from_dsd(dsds.projects)
it_job_postings = Artefacts.create_dataflow_from_dsd(dsds.it_job_postings)
job_titles_demand = Artefacts.create_dataflow_from_dsd(dsds.job_titles_demand)
it_workforce_skills = Artefacts.create_dataflow_from_dsd(dsds.it_workforce_skills)
competitors = Artefacts.create_dataflow_from_dsd(dsds.competitors)
key_people = Artefacts.create_dataflow_from_dsd(dsds.key_people)
management_changes = Artefacts.create_dataflow_from_dsd(dsds.management_changes)
top_outsourcing_raw = Artefacts.create_dataflow_from_dsd(dsds.top_outsourcing_raw)
top_outsourcing_locations = Artefacts.create_dataflow_from_dsd(dsds.top_outsourcing_locations)
top_outsourcing_verticals = Artefacts.create_dataflow_from_dsd(dsds.top_outsourcing_providers)
additional_info = Artefacts.create_dataflow_from_dsd(dsds.additional_info)
swot_analysis = Artefacts.create_dataflow_from_dsd(dsds.swot_analysis)
infongen = Artefacts.create_dataflow_from_dsd(dsds.infongen)
drivers_priorities = Artefacts.create_dataflow_from_dsd(dsds.drivers_priorities)
data_availability = Artefacts.create_dataflow_from_dsd(dsds.data_availability)
