from quanthub.structures import Mi<PERSON><PERSON><PERSON>facts as Artefacts
from quanthub.structures import QuantHubDataBridge
from quanthub.structures import ValueType as Type
from quanthub.structures import create_version_increment_client
from quanthub_pipeline.system import data_api as __qh
from quanthub_pipeline.system.interface.runner_config import the_runner_config

from ..constants import EPAM
from . import glossaries as gl

__workspace = the_runner_config.workspace_id
__structure_client = __qh.create_sdmxplus_structure_client()
__version_client = create_version_increment_client()
__data_bridge = QuantHubDataBridge(__structure_client, __version_client)

sales_research = Artefacts.create_concept_scheme(
    agency=EPAM,
    artefact_id="SALES_ENABLEMENT",
    name="Sales Enablement",
    description="",
    concepts=[
        Artefacts.create_concept(
            "ABOUT",
            "About",
            "General information or overview of a topic or entity.",
            Type.String,
        ),
        Artefacts.create_concept(
            "ACCOUNT_MANAGER",
            "Account Manager",
            "The individual responsible for managing client accounts and relationships.",
            Type.String,
        ),
        Artefacts.create_concept(
            "ADDITIONAL_INFORMATION",
            "Additional Information",
            "Extra details or data that provide further context or insight.",
            Type.String,
        ),
        Artefacts.create_concept(
            "ADDITIONAL_MATERIALS",
            "Additional Materials",
            "Supplementary resources or documents related to the main content.",
            Type.String,
        ),
        Artefacts.create_concept(
            "BUSINESS_UNIT",
            "Business Unit",
            "A specific division or segment within a company.",
            Type.Glossary,
            gl.epam_business_unit_sr,
        ),
        Artefacts.create_concept(
            "COMMENTS",
            "Comments",
            "",
            Type.String,
        ),
        Artefacts.create_concept(
            "COMPANY_NAME",
            "Company name",
            "The official name of a business entity.",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "COMPANY_TYPE",
            "Company Type",
            "The classification of a company based on its ownership, such as public or private.",
            Type.Glossary,
            gl.epam_company_type_sr,
        ),
        Artefacts.create_concept(
            "COMPETITOR_NAME",
            "Competitor Name",
            "The name of a business competing in the same market.",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "CRM_LINK",
            "CRM Link",
            "A hyperlink to a customer relationship management system entry.",
            Type.String,
        ),
        Artefacts.create_concept(
            "CUSTOMER_CODE",
            "Customer code",
            "A unique identifier assigned to a customer.",
            Type.Glossary,
            gl.epam_customer_code_sr,
        ),
        Artefacts.create_concept(
            "CURRENCY_CODE",
            "Currency Code",
            "A unique identifier assigned to a currency.",
            Type.Glossary,
            gl.epam_currency_code_sr,
        ),
        Artefacts.create_concept(
            "DATA_AS_OF",
            "Data as of",
            "The date when the provided information was last updated.",
            Type.DateTime,
        ),
        Artefacts.create_concept(
            "DATE",
            "Date",
            "The specific day, month, and year of an event.",
            Type.ObservationalTimePeriod,
        ),
        Artefacts.create_concept(
            "DEPARTURES",
            "Departures",
            "The number of individuals who have left the organization within a certain period.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "DESCRIPTION",
            "Description",
            "A brief explanation or overview of an item or concept.",
            Type.String,
        ),
        Artefacts.create_concept(
            "EBITDA",
            "EBITDA",
            "Earnings Before Interest, Taxes, Depreciation, and Amortization.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "EMPLOYEES",
            "Employees",
            "The total number of individuals employed by the company.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "END_DATE",
            "End Date",
            "The date on which an event or period concludes.",
            Type.ObservationalTimePeriod,
        ),
        Artefacts.create_concept(
            "EPAM_CLIENTS",
            "EPAM Clients",
            "Clients associated with EPAM Systems.",
            Type.String,
        ),
        Artefacts.create_concept(
            "EVENTS_CATEGORIES",
            "Events Categories",
            "Different classifications or types of events.",
            Type.Glossary,
            gl.sr_events_hierarchy_test,
        ),
        Artefacts.create_concept(
            "EVENT_TITLES",
            "Events Titles",
            "The names or titles given to specific events.",
            Type.String,
        ),
        Artefacts.create_concept(
            "EXECUTIVE_NAME",
            "Executive Name",
            "The name of an individual holding an executive position.",
            Type.String,
        ),
        Artefacts.create_concept(
            "FINANCIAL_METRICS",
            "Financial Metrics",
            "Key financial indicators of a company, including revenue by region, by business unit, and yearly total.",
            Type.Glossary,
            gl.epam_financial_metrics_sr,
        ),
        Artefacts.create_concept(
            "FINANCIAL_METRICS_ATTRIBUTE",
            "Financial Attribute",
            "The segmentation of financial metrics by specific attribute such as revenue, EBITDA.",
            Type.Glossary,
            gl.epam_financial_metrics_attribute_sr,
        ),
        Artefacts.create_concept(
            "FINANCIAL_METRICS_BUSINESS_CATEGORY",
            "Business Category",
            "The segmentation of financial metrics by specific business vertical.",
            Type.Glossary,
            gl.epam_financial_metrics_business_category_sr,
        ),
        Artefacts.create_concept(
            "FINANCIAL_METRICS_REGION_CATEGORY",
            "Region Category",
            "The segmentation of financial metrics by geographic region defined by particular business.",
            Type.Glossary,
            gl.epam_financial_metrics_region_category_sr,
        ),
        Artefacts.create_concept(
            "FINANCIAL_METRICS_VALUE",
            "Value",
            "The numerical value of a financial metric.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "FIRST_LEVEL_CONNECTIONS_NAME",
            "1st level connections / Name",
            "Names of direct contacts or connections in a professional network.",
            Type.String,
        ),
        Artefacts.create_concept(
            "GBU",
            "GBU",
            "Stands for Global Business Unit, representing a specific business segment within a larger corporation.",
            Type.Glossary,
            gl.epam_gbu,
        ),
        Artefacts.create_concept(
            "HIRES",
            "Hires",
            "The number of individuals recently employed by the company.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "INDUSTRIES_HIERARCHICAL",
            "Industries Hierarchical",
            "A structured categorization of industries from broad to specific.",
            Type.Glossary,
            gl.sr_industries_hierarchy,
        ),
        Artefacts.create_concept(
            "INDUSTRY",
            "Industry",
            "The category representing the primary business activity of a company.",
            Type.Glossary,
            gl.epam_industries_sr,
        ),
        Artefacts.create_concept(
            "INVESTORS",
            "Investors",
            "Individuals or entities that provide capital to a company in exchange for equity or debt.",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "IT_SPEND",
            "IT Spend",
            "The amount of money a company spends on information technology.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "JOB_TITLE_NAME",
            "Job Title Name",
            "The name of a specific job or position.",
            Type.Glossary,
            gl.epam_job_titles_sr,
        ),
        Artefacts.create_concept(
            "JOB_TITLE_LEVEL",
            "The seniority level of a job title.",
            "",
            Type.Glossary,
            gl.sr_job_titles_id,
        ),
        Artefacts.create_concept(
            "JOINING_COMPANY",
            "Joining Company",
            "The company an individual is newly employed by or associated with.",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "JUSTIFICATION",
            "Justification",
            "",
            Type.String,
        ),
        Artefacts.create_concept(
            "LINKEDIN_URL",
            "LinkedIn URL",
            "The web address of a LinkedIn profile or page.",
            Type.String,
        ),
        Artefacts.create_concept(
            "LINK_TO_THE_REPORT",
            "Link to the Report",
            "",
            Type.String,
        ),
        Artefacts.create_concept(
            "LOCATIONS_HIERARCHICAL",
            "Locations Hierarchical",
            "The hierarchical organization of locations.",
            Type.Glossary,
            gl.sr_location_hierarchy,
        ),
        Artefacts.create_concept(
            "LOGO_URL",
            "Logo URL",
            "The web address where the company's logo can be found.",
            Type.String,
        ),
        Artefacts.create_concept(
            "MONEY_RAISED",
            "Money Raised",
            "The amount of capital raised by the company.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "MOVEMENT_TYPE",
            "Movement Type",
            "The type of change or transition within the company, such as promotions, exits, or hires.",
            Type.Glossary,
            gl.epam_executive_movement_type_sr,
        ),
        Artefacts.create_concept(
            "NET_CHANGE",
            "Net change",
            "The total difference resulting from additions and subtractions in a specific metric, such as employee count.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "NUMBER_OF_ACTIVE_WORKFLOWS",
            "# of Active Workflows",
            "",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "NUMBER_OF_EMPLOYEES",
            "Number of Employees",
            "The total count of individuals employed by the company.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "NUMBER_OF_FUNDING_ROUNDS",
            "# Of Funding Rounds",
            "The total number of times the company has raised funds through investment rounds.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS",
            "# of Job Postings (Last 12 Months)",
            "The total number of job listings posted by the company in the last 12 months.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "ONE_YEAR_GROWTH_PERCENT",
            "1y growth, %",
            "The percentage growth of the company over the past year.",
            Type.Decimal,
        ),
        Artefacts.create_concept(
            "PARENT_COMPANY",
            "Parent Company",
            "",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "PREVIOUS_COMPANY",
            "Previous Company",
            "The company where an individual worked before their current position.",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "PREVIOUS_POSITION",
            "Previous Position",
            "The specific job title or role an individual held at their previous company.",
            Type.Glossary,
            gl.epam_job_titles_sr,
        ),
        Artefacts.create_concept(
            "PRIORITY_DRIVER",
            "Priority / Driver",
            "",
            Type.Glossary,
            gl.sr_problem_statement_driver,
        ),
        Artefacts.create_concept(
            "PREVIOUS_POSITION_NAME",
            "Previous Position Name",
            "The name or designation of the job position held previously by an individual.",
            Type.Glossary,
            gl.epam_job_titles_sr,
        ),
        Artefacts.create_concept(
            "PRODUCTS_AND_SERVICES",
            "Products and Services",
            "The range of products and services offered by the company.",
            Type.Glossary,
            gl.epam_products_and_services_sr,
        ),
        Artefacts.create_concept(
            "PROJECT_CATEGORY",
            "Project Category",
            "An identifier that classifies projects as either internal or outsourcing.",
            Type.Glossary,
            gl.epam_project_category_sr,
        ),
        Artefacts.create_concept(
            "PROJECT_DESCRIPTION",
            "Project Description",
            "A detailed explanation or overview of a specific project.",
            Type.String,
        ),
        Artefacts.create_concept(
            "PROJECT_TYPE",
            "Project Type",
            "The kind of project, indicating its scope, purpose, or methodology.",
            Type.Glossary,
            gl.sr_project_type,
        ),
        Artefacts.create_concept(
            "RATIO",
            "Ratio",
            "A quantitative relationship between two numbers, showing how many times one value contains or is contained within the other.",
            Type.Decimal,
        ),
        Artefacts.create_concept(
            "RECORD_TYPE",
            "Record Type",
            "Categorization of records as identifiers for key people or connections.",
            Type.Glossary,
            gl.epam_record_type_sr,
        ),
        Artefacts.create_concept(
            "REVENUE",
            "Revenue",
            "The total income generated by the company.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "ROW_ID",
            "Row ID",
            "A unique identifier for a specific row in a database or table.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "SALES_MANAGER",
            "Sales Manager",
            "The individual responsible for directing and overseeing the sales activities of the company.",
            Type.String,
        ),
        Artefacts.create_concept(
            "SKILLS",
            "Skills",
            "The abilities or expertise possessed by an individual or required for a specific job or task.",
            Type.Glossary,
            gl.epam_skills_sr,
        ),
        Artefacts.create_concept(
            "SOURCE",
            "Source",
            "The origin or provider of the information or data.",
            Type.String,
        ),
        Artefacts.create_concept(
            "START_DATE",
            "Start Date",
            "The date when a particular event or activity commenced.",
            Type.ObservationalTimePeriod,
        ),
        Artefacts.create_concept(
            "STATUS",
            "Status",
            "The current condition or progress state of a task, project, or entity.",
            Type.Glossary,
            gl.epam_current_engagement_status,
        ),
        Artefacts.create_concept(
            "STATUS_OF_THE_RESEARCH",
            "Status of the research",
            "The current phase or progress level of a specific research project.",
            Type.Glossary,
            gl.sr_status_of_the_research_sr_account,
        ),
        Artefacts.create_concept(
            "SUBSIDIARY_NAME",
            "Subsidiary Name",
            "",
            Type.Glossary,
            gl.epam_company_names_sr,
        ),
        Artefacts.create_concept(
            "TABS_INFORMATION",
            "Tabs Information",
            "Details all tabs mentioned in the research to outline data availability across specific sections.",
            Type.Glossary,
            gl.sr_tabs_sr_account,
        ),
        Artefacts.create_concept(
            "TARGET_ACCOUNT",
            "Target Account",
            "The specific customer or company of interest for business dealings or analysis.",
            Type.Glossary,
            gl.epam_target_account_list_sr,
        ),
        Artefacts.create_concept(
            "TECHNOLOGY_TOOLS",
            "Technology Tools",
            "The software, platforms, and technologies used by the company or required for specific tasks.",
            Type.Glossary,
            gl.epam_technology_tools_sr,
        ),
        Artefacts.create_concept(
            "TECH_STACK_CATEGORY",
            "Tech Stack Category",
            "The classification of technology tools and platforms based on their function or use in the company's operations.",
            Type.Glossary,
            gl.epam_tech_stack_category_sr,
        ),
        Artefacts.create_concept(
            "TELESCOPE_LINK",
            "Telescope Link",
            "A hyperlink that provides a detailed or expanded view, possibly of data or a specific online resource.",
            Type.String,
        ),
        Artefacts.create_concept(
            "TOP_METRICS_BREAKDOWN",
            "Top Metrics Breakdown",
            "An indicator used to highlight and analyze key metrics separately for location and company.",
            Type.Glossary,
            gl.epam_top_metrics_breakdown_sr,
        ),
        Artefacts.create_concept(
            "TOTAL_FUNDING_AMOUNT",
            "Total Funding Amount",
            "The cumulative amount of funding received by the company, expressed in USD.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "TRANSACTION_NAME",
            "Transaction Name",
            "The name or title associated with a specific financial or business transaction.",
            Type.Glossary,
            gl.epam_funding_rounds_transaction_name_sr,
        ),
        Artefacts.create_concept(
            "VALUATION",
            "Valuation",
            "The estimated market value or worth of the company, expressed in USD.",
            Type.Integer,
        ),
        Artefacts.create_concept(
            "WEBSITE",
            "Website",
            "The official online presence or homepage of the company.",
            Type.String,
        ),
        Artefacts.create_concept(
            "YEAR",
            "Year",
            "The calendar year associated with a specific event, data, or report.",
            Type.Integer,
        ),
    ],
)

sdmx = __data_bridge.read_concept_scheme(__workspace, urn="SDMX:CROSS_DOMAIN_CONCEPTS(2.1)")
