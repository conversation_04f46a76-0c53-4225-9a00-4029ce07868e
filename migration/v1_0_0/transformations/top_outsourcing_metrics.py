"""
    This transformation should be copied to Quanthub Transformations.
    Following parameters correspond to dataflow names and should be set in QH Transformations before execution:
    top_outsourcing_metrics_raw_df
    top_outsourcing_metrics_providers_df
    top_outsourcing_metrics_locations_df
"""

# dependency: se-data-pipeline

from input_parameters import *
from quanthub.transformations.runner import Transformation<PERSON><PERSON>ner
from se_data_pipeline.component.dataflow import TopOutsourcingMetricsTransformation

top_outsourcing_metrics_transformation = TransformationRunner(TopOutsourcingMetricsTransformation, commit_datasets=True)
top_outsourcing_metrics_output = top_outsourcing_metrics_transformation.run()

top_outsourcing_metrics_providers_df = top_outsourcing_metrics_output.top_outsourcing_metrics_providers_df
top_outsourcing_metrics_locations_df = top_outsourcing_metrics_output.top_outsourcing_metrics_locations_df
