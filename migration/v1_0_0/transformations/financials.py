"""
    This transformation should be copied to Quanthub Transformations.
    Following parameters correspond to dataflow names and should be set in QH Transformations before execution:
    financials_raw_df
    financials_by_company_df
    financials_by_company_vertical_df
    financials_by_company_and_location_df
"""

# dependency: se-data-pipeline

from input_parameters import *
from quanthub.transformations.runner import Trans<PERSON><PERSON><PERSON><PERSON>
from quanthub_pipeline.pyscripting.utils import configure_pandas_dataset_parameter
from se_data_pipeline.component.dataflow import FinancialsTransformation

configure_pandas_dataset_parameter("financials_raw_df", read_only=True)

financials_transformation = TransformationRunner(FinancialsTransformation)
financials_output = financials_transformation.run()

financials_by_company_df = financials_output.financials_by_company_df
financials_by_company_vertical_df = financials_output.financials_by_company_vertical_df
financials_by_company_and_location_df = financials_output.financials_by_company_and_location_df
