from quanthub.structures import GlossaryAttributeType as Type
from quanthub.structures import MigrationArtefacts as Artefacts

from ..constants import SALES_ENABLEMENT_ACCOUNT

epam_awards_and_recognitions_csr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="AWARDS_AND_RECOGNITIONS_CSR",
    name="Awards & Recognitions CSR",
    description="",
)

epam_business_unit_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="BUSINESS_UNIT",
    name="Business Unit",
    description="",
)

epam_company_names_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="COMPANY_NAMES",
    name="Company Names",
    description="",
)

epam_company_type_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="COMPANY_TYPE",
    name="Company Type",
    description="",
)

epam_current_engagement_status = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="CURRENT_ENGAGEMENT_STATUS",
    name="Current Engagement Status",
    description="",
)

epam_customer_code_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="CUSTOMER_CODE",
    name="Customer Code",
    description="",
)

epam_currency_code_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="CURRENCY_CODE",
    name="Currency Code",
    description="",
)

epam_events_subcategory_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="EVENTS_SUBCATEGORY",
    name="Events Subcategory",
    description="",
)

epam_events_title_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="EVENTS_TITLE",
    name="Events Title",
    description="",
)

epam_executive_movement_type_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="EXECUTIVE_MOVEMENT_TYPE",
    name="Executive Movement Type",
    description="",
)

epam_financial_metrics_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="FINANCIAL_METRICS",
    name="Financial Metrics",
    description="",
)

epam_financial_metrics_business_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="FINANCIAL_METRICS_BUSINESS_CATEGORY",
    name="Category of Financial Metrics by Business",
    description="",
)

epam_financial_metrics_region_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="FINANCIAL_METRICS_REGION_CATEGORY",
    name="Category of Financial Metrics by Region",
    description="",
)

epam_financial_metrics_attribute_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="FINANCIAL_METRICS_ATTRIBUTE",
    name="Financial Metrics Attributes",
    description="",
)

epam_funding_rounds_transaction_name_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TRANSACTION_NAME",
    name="Funding Rounds Transaction Name",
    description="",
)

epam_gbu = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="GBU",
    name="GBU",
    description="",
)

epam_industries_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="INDUSTRIES",
    name="Industries",
    description="",
)

epam_it_initiatives = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="IT_INITIATIVES",
    name="IT Initiatives",
    description="",
)

epam_it_workforce_details_name_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="IT_WORKFORCE_DETAILS_NAME",
    name="IT Workforce Details Name",
    description="",
)

epam_it_workforce_details_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="IT_WORKFORCE_DETAILS",
    name="IT Workforce Details",
    description="",
)

epam_job_titles_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="JOB_TITLES",
    name="Job Titles",
    description="",
)


sr_problem_statement_driver = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PROBLEM_STATEMENT_DRIVER",
    name="Problem Statement/Driver",
    description="",
)

epam_news_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="NEWS_CATEGORY",
    name="News Category",
    description="",
)


epam_offerings_by_category = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="OFFERINGS_BY_CATEGORY",
    name="Offerings by Category CSR",
    description="",
)

epam_offerings_by_category_hierarchy = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="OFFERINGS_BY_CATEGORY_HIERARCHY",
    name="Offerings by Category hierarchy",
    description="",
)

epam_offerings_by_industry = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="OFFERINGS_BY_INDUSTRY",
    name="Offerings by Industry CSR",
    description="",
)

epam_offerings_by_services = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="OFFERINGS_BY_SERVICES",
    name="Offerings by Services CSR",
    description="",
)

epam_outsourcing_workloads_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="OUTSOURCING_WORKLOADS",
    name="Outsourcing Workloads",
    description="",
)

epam_partnerships_and_acquisitions_category = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PARTNERSHIPS_AND_ACQUISITIONS_CATEGORY",
    name="Partnerships and Acquisitions Category",
    description="",
)

epam_products_and_services_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PRODUCTS_AND_SERVICES",
    name="Products and Services",
    description="",
)

epam_project_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PROJECT_CATEGORY",
    name="Project Category",
    description="",
)

epam_project_type_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PROJECT_TYPE",
    name="Project Type SR deactivated",
    description="",
)

epam_record_type_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="RECORD_TYPE",
    name="Record Type",
    description="",
)

epam_risks_and_challenges_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="RISKS_AND_CHALLENGES",
    name="Risks and Challenges",
    description="",
)

epam_skills_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="SKILLS",
    name="Skills",
    description="",
)

epam_solutions___capabilities = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="SOLUTIONS___CAPABILITIES",
    name="Solutions & Capabilities CSR",
    description="",
)

epam_strategy_risks_and_initiatives_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="STRATEGY_RISKS_AND_INITIATIVES_CATEGORY",
    name="Strategy Risks and Initiatives Category",
    description="",
)

epam_strategy_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="STRATEGY",
    name="Strategy",
    description="",
)

epam_strategy__risks_and_initiatives_subcategory_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="STRATEGY__RISKS_AND_INITIATIVES_SUBCATEGORY",
    name="Strategy, Risks and Initiatives Subcategory",
    description="",
)

epam_target_account_list_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TARGET_ACCOUNT_LIST",
    name="Target Account List",
    description="",
)

epam_technology_tools_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TECHNOLOGY_TOOLS",
    name="Technology Tools",
    description="",
)

epam_tech_stack_category_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TECH_STACK_CATEGORY",
    name="Tech Stack Category",
    description="",
)

epam_top_metrics_breakdown_sr = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TOP_METRICS_BREAKDOWN",
    name="Top Metrics Breakdown",
    description="",
)

sr_events_hierarchy_test = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="EVENTS_HIERARCHY",
    name="Events Hierarchy",
    description="",
)

sr_industries_hierarchy = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="INDUSTRIES_HIERARCHY",
    name="Industries Hierarchy",
    description="The hierarchy of Industry consists of industries and sub-industries in which companies operate.",
)

sr_job_titles_id = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="JOB_TITLE_LEVEL",
    name="Job Title Level",
    description="Definitions for job title classification",
)

sr_location_hierarchy = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="LOCATION_HIERARCHY",
    name="Location Hierarchy",
    description="Hierarchy of locations, that includes Country, Region & City",
    attributes=[
        Artefacts.create_glossary_attribute("type", Type.Text),
        Artefacts.create_glossary_attribute("name_original", Type.Text),
        Artefacts.create_glossary_attribute("iso2", Type.Text),
        Artefacts.create_glossary_attribute("iso3", Type.Text),
    ],
)


sr_project_type = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="PROJECT_TYPE",
    name="Project Type",
    description="",
)

sr_status_of_the_research_sr_account = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="STATUS_OF_THE_RESEARCH",
    name="Status of the Research",
    description="",
)

sr_tabs_sr_account = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="TABS",
    name="Tabs",
    description="",
)

sr_cached_item_review = Artefacts.create_glossary(
    agency=SALES_ENABLEMENT_ACCOUNT,
    artefact_id="CACHED_ITEM_REVIEW",
    name="Cached Item Review",
    description="Status of the representation map item review",
)
