import re
import os
import pandas as pd
from se_data_pipeline.component.utils import init_data_bridge
from quanthub.transformations.utils import init_local_config
from quanthub_pipeline.system import data_api as qh
from typing import Dict

from se_data_pipeline.component.llm import LLMDataHandler

data_bridge = init_data_bridge()

WORKSPACE = "Presales:SHADOW_TEST"
source_path = "migration/source_datasets/"
processed_path = "migration/processed_datasets/"

init_local_config(WORKSPACE)
pandas_reader = qh.create_pandas_reader()
pandas_writer = qh.create_pandas_writer()
llm_data_handler = LLMDataHandler()


def attach_data_from_files(new_to_old_dataset_ids: Dict[str, str]) -> Dict[str, pd.DataFrame]:
    df_map = {}
    for new, old in new_to_old_dataset_ids.items():
        df_old = pd.read_csv(f"{source_path}{old}.csv")
        df_map[new] = df_old
    return df_map


new_to_old_dataset_ids = {
    "SE_A:ADDITIONAL_INFORMATION(1.0.0)": "SR-ADDITIONAL_INFORMATION(3.0.0)",
    "SE_A:BUSINESS_STRUCTURE(1.0.0)": "SR-BUSINESS_STRUCTURE(7.0.0)",
    "SE_A:COMPANY_ENTITIES(1.0.0)": "SR-COMPANY_ENTITIES(1.0.0)",
    "SE_A:COMPETITORS(1.0.0)": "SR-COMPETITORS(9.1.0)",
    "SE_A:CURRENT_ENGAGEMENTS(1.0.0)": "SR-CURRENT_ENGAGEMENTS(9.0.0)",
    "SE_A:DATA_AVAILABILITY(1.0.0)": "SR-DATA_AVAILABILITY(2.0.0)",
    "SE_A:DRIVERS_AND_PRIORITIES(1.0.0)": "SR-DRIVERS_AND_PRIORITIES(3.0.0)",
    "SE_A:EVENTS(1.0.0)": "SR-EVENTS(9.0.0)",
    # "SE_A:FINANCIALS_BY_COMPANY(1.0.0)": "SR-FINANCIALS(8.0.2)",
    # "SE_A:FINANCIALS_BY_COMPANY_AND_BUSINESS_VERTICAL(1.0.0)": "SR-FINANCIALS(8.0.2)",
    # "SE_A:FINANCIALS_BY_COMPANY_AND_LOCATION(1.0.0)": "SR-FINANCIALS(8.0.2)",
    "SE_A:FINANCIALS_RAW(1.0.0)": "SR-FINANCIALS(8.0.2)",
    "SE_A:FEATURED_CLIENTS(1.0.0)": "SR-FEATURED_CLIENTS(3.0.0)",
    "SE_A:FUNDING_ROUNDS(1.0.0)": "SR-FUNDINGS(3.0.0)",
    "SE_A:GENERAL_OVERVIEW(1.0.0)": "SR-GENERAL_OVERVIEW(15.0.0)",
    "SE_A:INFONGEN(1.0.0)": "SR-INFONGEN(1.0.0)",
    "SE_A:IT_JOB_POSTINGS(1.0.0)": "SR-IT_JOB_POSTINGS(10.0.0)",
    "SE_A:IT_WORKFORCE_LOCATIONS(1.0.0)": "SR-IT_WORKFORCE_LOCATIONS(10.0.0)",
    "SE_A:IT_WORKFORCE_SKILLS(1.0.0)": "SR-IT_WORKFORCE_SKILLS(7.0.0)",
    "SE_A:JOB_TITLES_IN_DEMAND(1.0.0)": "SR-JOB_TITLES_IN_DEMAND(6.0.0)",
    "SE_A:KEY_PEOPLE(1.0.0)": "SR-KEY_PEOPLE(11.0.0)",
    "SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)": "SR-MANAGEMENT_TEAM_CHANGES(8.0.0)",
    "SE_A:PROJECTS(1.0.0)": "SR-PROJECTS(11.0.0)",
    "SE_A:SWOT_ANALYSIS(1.0.0)": "SR-SWOT_ANALYSIS(1.0.0)",
    "SE_A:TECHNOLOGY_STACK(1.0.0)": "SR-TECHNOLOGY_STACK(9.0.0)",
    # "SE_A:TOP_OUTSOURCING_METRICS_LOCATIONS(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
    # "SE_A:TOP_OUTSOURCING_METRICS_PROVIDERS(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
    "SE_A:TOP_OUTSOURCING_METRICS_RAW(1.0.0)": "SR-TOP_OUTSOURCING_METRCIS(2.0.0)",
}

df_map = attach_data_from_files(new_to_old_dataset_ids)


def build_location_name(row, df):
    if pd.isna(row["__parent_id"]):
        return row["name"]
    else:
        parent_row = df[df["id"] == row["__parent_id"]].iloc[0]
        return row["name"] + ", " + build_location_name(parent_row, df)


def _remove_patterns(text):
    text = re.sub(r"\bGreater\b|\bArea\b", "", text, flags=re.IGNORECASE)
    return text.replace("  ", " ").replace(" ,", ",")


def _remap_loc_ids() -> dict:
    old_loc_df = pd.read_csv("migration/v1_0_0/assets/epam_locations_hierarchy_sr.csv")
    llm = llm_data_handler
    old_loc_df["LOCATION_ID"] = old_loc_df.apply(lambda row: build_location_name(row, old_loc_df), axis=1)

    location_id_map = {"Location": old_loc_df}
    llm.replace_location_to_ids(
        location_id_map,
        target_column="LOCATION_ID",
        formatter_func=_remove_patterns,
    )

    return old_loc_df.set_index("id")["LOCATION_ID"].to_dict()


location_dict = _remap_loc_ids()
location_dict.update(
    {
        "UNITED_STATES_US": "USA",
        "UNITED_KINGDOM_UK": "GBR",
        "EUROPE_MIDDLE_EAST_AFRICA": "EMEA",
        "ASIA_PACIFIC": "APAC",
        "APAC": "APAC",
        "EMEA": "EMEA",
        "EUROPE": "EUROPE",
        "ASIA": "ASIA",
        "AFRICA_MIDDLE_EAST_AND_ASIA_AMEA": "AMEA",
        "USA": "USA",
        "EUROPE_MIDDLE_EAST_AND_AFRICA": "EMEA",
        "EUROPE_MIDDLE_EAST_AND_AFRICA_EMEA": "EMEA",
        "LATIN_AMERICA": "LATAM",
        "ASIA_PACIFIC_JAPAN_AND_CHINA_APJC": "APJC",
        "EUROPEAN_REGIONS": "EUROPE",
        "LATAM": "LATAM",
        "AMEA": "AMEA",
        "KINGDOM_OF_SAUDI_ARABIA": "SAU",
        "ASIA_PACIFIC_APAC": "APAC",
    }
)


def set_datetime():
    for dataset_id, df in df_map.items():
        if "DATA_AS_OF" in df.columns:
            df["DATA_AS_OF"] = pd.to_datetime(df["DATA_AS_OF"]).dt.strftime("%Y-%m-%dT%H:%M:%S")
    return None


def check_columns(df_old, df_new):
    result = set(df_old.columns).symmetric_difference(set(df_new.columns))
    return not bool(result), result


def validate_columns(target: str):
    df_old = df_map[target]
    df_new = pandas_reader.read(WORKSPACE, target)
    check = check_columns(df_old, df_new)
    assert check[0] is True


def format_events():
    target = "SE_A:EVENTS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "EVENTS_CATEGORY": "EVENTS_CATEGORIES",
            "EVENTS_TITLE_S": "EVENT_TITLES",
        },
        inplace=True,
    )
    categories = {
        "INVESTMENT1": "INVESTMENT",
        "INVESTMENTS": "INVESTMENT",
        "MERGER1": "MERGER",
        "MERGERS": "MERGER",
        "INVESTMENTS1": "INVESTMENTS",
        "CLIENT1": "CLIENT",
    }
    for old_cat, new_cat in categories.items():
        df_old.loc[df_old["EVENTS_CATEGORIES"] == old_cat, "EVENTS_CATEGORIES"] = new_cat

    validate_columns(target)


def format_financials_raw():
    target = "SE_A:FINANCIALS_RAW(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "REVENUE_USD": "REVENUE",
            "IT_SPEND_USD": "IT_SPEND",
            "EBITDA_USD": "EBITDA",
            "CATEGORY_OF_FINANCIAL_METRICS": "BUSINESS_CATEGORY",
        },
        inplace=True,
    )
    df_melted = pd.melt(
        df_old,
        id_vars=[
            "FREQ",
            "TARGET_ACCOUNT",
            "FINANCIAL_METRICS",
            "BUSINESS_CATEGORY",
            "TIME_PERIOD",
            "SOURCE",
            "DATA_AS_OF",
        ],
        value_vars=["REVENUE", "IT_SPEND", "EBITDA"],
        var_name="FINANCIAL_ATTRIBUTE",
        value_name="FINANCIAL_METRICS_VALUE",
    )
    df_melted["CURRENCY_CODE"] = "USD"
    df_melted["LOCATIONS_HIERARCHICAL"] = "NOT_AVAILABLE"
    df_melted["REGION_CATEGORY"] = "_"

    business = df_melted.loc[df_melted["FINANCIAL_METRICS"] == "REVENUE_BY_BUSINESS"]
    region = df_melted.loc[df_melted["FINANCIAL_METRICS"] == "REVENUE_BY_REGION"].copy()
    region["LOCATIONS_HIERARCHICAL"] = region["BUSINESS_CATEGORY"].map(location_dict)
    region["LOCATIONS_HIERARCHICAL"].fillna("NOT_AVAILABLE", inplace=True)
    region.loc[region["LOCATIONS_HIERARCHICAL"] == "NOT_AVAILABLE", "REGION_CATEGORY"] = region["BUSINESS_CATEGORY"]
    region["BUSINESS_CATEGORY"] = "_"
    yearly_total = df_melted.loc[df_melted["FINANCIAL_METRICS"] == "YEARLY_TOTAL"]

    result = pd.concat([business, region, yearly_total], ignore_index=True)
    df_map[target] = result

    validate_columns(target)


def format_general_overview():
    target = "SE_A:GENERAL_OVERVIEW(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "REVENUE_USD": "REVENUE",
            "IT_SPEND_USD": "IT_SPEND",
            "EBITDA_USD": "EBITDA",
            "VALUATION_USD": "VALUATION",
            "TOTAL_FUNDING_AMOUNT_USD": "TOTAL_FUNDING_AMOUNT",
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
        },
        inplace=True,
    )
    df_old["CURRENCY_CODE"] = "USD"
    df_old["NUMBER_OF_EMPLOYEES"] = df_old["NUMBER_OF_EMPLOYEES"].fillna(0).round().astype(int)
    df_map[target] = df_old
    validate_columns(target)


def format_it_job_postings():
    target = "SE_A:IT_JOB_POSTINGS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "JOB_TITLE_S": "JOB_TITLE_NAME",
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
        },
        inplace=True,
    )
    df_map[target] = df_old
    validate_columns(target)


def format_it_workforce():
    target = "SE_A:IT_WORKFORCE_LOCATIONS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
        },
        inplace=True,
    )
    df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"] = (
        df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"].fillna(0).round().astype(int)
    )

    df_map[target] = df_old
    validate_columns(target)


def format_it_skills():
    target = "SE_A:IT_WORKFORCE_SKILLS(1.0.0)"
    df_old = df_map[target]
    df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"] = (
        df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"].fillna(0).round().astype(int)
    )

    df_map[target] = df_old
    validate_columns(target)


def format_job_titles():
    target = "SE_A:JOB_TITLES_IN_DEMAND(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "JOB_TITLE_S": "JOB_TITLE_NAME",
        },
        inplace=True,
    )
    df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"] = (
        df_old["NUMBER_OF_JOB_POSTINGS_LAST_12_MONTHS"].round().astype(int)
    )
    df_map[target] = df_old
    validate_columns(target)


def format_key_people():
    target = "SE_A:KEY_PEOPLE(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "JOB_TITLE_S": "JOB_TITLE_NAME",
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
            "JOB_TITLE_ID": "JOB_TITLE_LEVEL",
        },
        inplace=True,
    )

    new_title_ids = pd.read_csv("migration/source_datasets/remaped_titles.csv")
    name_to_id = new_title_ids.set_index("JOB_TITLE_S")["NEW_JOB_TITLE_ID"].to_dict()
    df_old["JOB_TITLE_LEVEL"] = df_old["JOB_TITLE_NAME"].map(name_to_id)

    df_map[target] = df_old
    validate_columns(target)


def format_management_team_changes():
    target = "SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "JOB_TITLE_S": "JOB_TITLE_NAME",
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
            "PREVIOUS_POSITION_S": "PREVIOUS_POSITION_NAME",
        },
        inplace=True,
    )

    df_map[target] = df_old
    validate_columns(target)


def format_projects():
    target = "SE_A:PROJECTS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "PROJECT_TYPE1": "PROJECT_TYPE",
            "LOCATIONS_HIERARCHY_NEW2": "LOCATIONS_HIERARCHICAL",
        },
        inplace=True,
    )

    df_map[target] = df_old
    validate_columns(target)


def format_swot():
    target = "SE_A:SWOT_ANALYSIS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "EVENTS_CATEGORY": "EVENTS_CATEGORIES",
        },
        inplace=True,
    )

    df_map[target] = df_old
    validate_columns(target)


def format_outsourcing():
    target = "SE_A:TOP_OUTSOURCING_METRICS_RAW(1.0.0)"
    df_old = df_map[target]
    df_old["LOCATIONS_HIERARCHICAL"] = "NOT_AVAILABLE"
    df_old["COMPANY_NAME"] = "_"
    df_old.loc[df_old["TOP_METRICS_BREAKDOWN"] == "PROVIDER", "COMPANY_NAME"] = df_old["METRCICS"]
    df_old.loc[df_old["TOP_METRICS_BREAKDOWN"] == "LOCATION", "LOCATIONS_HIERARCHICAL"] = df_old["METRCICS"].map(
        location_dict
    )
    df_old["LOCATIONS_HIERARCHICAL"].fillna("NOT_AVAILABLE", inplace=True)
    df_old["NUMBER_OF_ACTIVE_WORKFLOWS"] = df_old["NUMBER_OF_ACTIVE_WORKFLOWS"].fillna(0).round().astype(int)
    df_old.drop(columns=["METRCICS"], inplace=True)

    df_map[target] = df_old
    validate_columns(target)


def format_fundings():
    target = "SE_A:FUNDING_ROUNDS(1.0.0)"
    df_old = df_map[target]
    df_old.rename(
        columns={
            "VALUATION_USD": "VALUATION",
            "MONEY_RAISED_USD": "MONEY_RAISED",
        },
        inplace=True,
    )
    df_old["CURRENCY_CODE"] = "USD"

    df_map[target] = df_old
    validate_columns(target)


def format_current_engagements():
    target = "SE_A:CURRENT_ENGAGEMENTS(1.0.0)"
    df_old = df_map[target]
    gbu_ids = {
        "GBU_NA_MidAtlantic_GEN": "GBU_NA_MIDATLANTIC_GEN",
        "GBU_NA_SoCal_OTH": "GBU_NA_SOCAL_OTH",
        "GBU_NA_HCLSWest_West1": "GBU_NA_HCLSWEST_WEST1",
        "GBU_EU_West1_UK": "GBU_EU_WEST1_UK",
        "GBU_EU_UKFS_Insurance": "GBU_EU_UKFS_INSURANCE",
        "GBU_NA_Insurance_INS": "GBU_NA_INSURANCE_INS",
        "GBU_NA_Central_C_CL": "GBU_NA_CENTRAL_C_CL",
    }
    for old_gbu, new_gbu in gbu_ids.items():
        df_old.loc[df_old["GBU"] == old_gbu, "GBU"] = new_gbu

    df_map[target] = df_old
    validate_columns(target)


def save_to_csv():
    for dataset_id, df in df_map.items():
        file_name = new_to_old_dataset_ids[dataset_id]
        if not os.path.exists(processed_path):
            os.makedirs(processed_path)
        df.to_csv(f"{processed_path}{file_name}.csv", index=False)
        print(f"Saved {file_name}")
    print("All datasets written to csv successfully")


if __name__ == "__main__":
    set_datetime()
    format_general_overview()
    format_fundings()
    format_current_engagements()
    format_financials_raw()
    format_events()
    format_it_job_postings()
    format_it_workforce()
    format_it_skills()
    format_job_titles()
    format_key_people()
    format_management_team_changes()
    format_projects()
    format_swot()
    format_outsourcing()
    save_to_csv()
