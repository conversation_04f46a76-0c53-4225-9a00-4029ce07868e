import os

import pandas as pd
from quanthub_pipeline.system import data_api as qh
from se_data_pipeline.component.base import Normaliser, NormalizationMixin
from typing import Dict

from se_data_pipeline.component.llm import LLMDataHandler
from se_data_pipeline.component.glossary import CodeListManager
from se_data_pipeline.component.logger import logger

processed_path = "migration/processed_datasets/"
titles_codelist_path = "migration/v1_0_0/assets/epam_job_titles_sr.csv"

pandas_reader = qh.create_pandas_reader()
pandas_writer = qh.create_pandas_writer()
llm_data_handler = LLMDataHandler()

title_datasets = {
    "SE_A:IT_JOB_POSTINGS(1.0.0)": "SR-IT_JOB_POSTINGS(10.0.0)",
    "SE_A:JOB_TITLES_IN_DEMAND(1.0.0)": "SR-JOB_TITLES_IN_DEMAND(6.0.0)",
    "SE_A:KEY_PEOPLE(1.0.0)": "SR-KEY_PEOPLE(11.0.0)",
    "SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)": "SR-MANAGEMENT_TEAM_CHANGES(8.0.0)",
}

title_columns = {
    ("SE_A:KEY_PEOPLE(1.0.0)", "JOB_TITLE_NAME"),
    ("SE_A:JOB_TITLES_IN_DEMAND(1.0.0)", "JOB_TITLE_NAME"),
    ("SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)", "JOB_TITLE_NAME"),
    ("SE_A:MANAGEMENT_TEAM_CHANGES(1.0.0)", "PREVIOUS_POSITION_NAME"),
    ("SE_A:IT_JOB_POSTINGS(1.0.0)", "JOB_TITLE_NAME"),
}


class TitlesNormaliser(Normaliser, NormalizationMixin):
    def __init__(self, sheet_df_map: Dict[str, pd.DataFrame], llm_data_handler: LLMDataHandler):
        self.sheet_df_map = sheet_df_map
        self.llm_data_handler = llm_data_handler

    def normalise(self) -> Dict[str, pd.DataFrame]:
        self._translate_text(title_columns)
        return self.sheet_df_map


def attach_data_from_files(new_to_old_dataset_ids: Dict[str, str]) -> Dict[str, pd.DataFrame]:
    df_map = {}
    for new, old in new_to_old_dataset_ids.items():
        df_old = pd.read_csv(f"{processed_path}{old}.csv")
        df_map[new] = df_old
    return df_map


df_map = attach_data_from_files(title_datasets)


def normalize_job_titles():
    normaliser = TitlesNormaliser(df_map, llm_data_handler)
    normaliser.normalise()


def create_titles_codelist():
    df = pd.read_csv(titles_codelist_path)
    df = df[:0].copy()
    unique_titles = set()
    for dataset_id, column in title_columns:
        unique_titles.update(df_map[dataset_id][column].unique())
    df["name"] = list(unique_titles)
    df["id"] = CodeListManager._generate_ids(df["name"])
    duplicates = df.loc[df.id.duplicated(keep=False)].copy()
    replacement_map = {}
    for id_, group in duplicates.groupby("id"):
        titles = group["name"].tolist()
        selected_title = titles[0]
        replacement_map.update({title: selected_title for title in titles})

    df["name"] = df["name"].replace(replacement_map)
    df["__annotations"] = "{}"
    df = df.drop_duplicates(subset="id").reset_index(drop=True)
    for dataset_id, column in title_columns:
        df_map[dataset_id][column] = df_map[dataset_id][column].replace(replacement_map)

    non_ascii = df[df["name"].apply(lambda x: not x.isascii())]
    if not non_ascii.empty:
        logger.warning(f"Non-ASCII characters in the codelist {non_ascii['name'].tolist()}")
    df.to_csv(titles_codelist_path, index=False)


def remap_titles():
    title_df = pd.read_csv(titles_codelist_path)
    title_id_map = title_df.set_index("name")["id"].to_dict()
    for dataset_id, column in title_columns:
        df_map[dataset_id][column] = df_map[dataset_id][column].replace(title_id_map)


def create_business_category_codelist():
    financials = pd.read_csv(f"{processed_path}SR-FINANCIALS(8.0.2).csv")
    category_fin_metrics = pd.read_csv("migration/v1_0_0/assets/epam_category_of_financial_metrics_sr.csv")
    new_rows = category_fin_metrics.loc[category_fin_metrics["id"].isin(financials["BUSINESS_CATEGORY"])].copy()
    new_rows.to_csv("migration/v1_0_0/assets/epam_financial_metrics_business_category_sr.csv", index=False)


def create_region_category_codelist():
    financials = pd.read_csv(f"{processed_path}SR-FINANCIALS(8.0.2).csv")
    category_fin_metrics = pd.read_csv("migration/v1_0_0/assets/epam_category_of_financial_metrics_sr.csv")
    new_rows = category_fin_metrics.loc[category_fin_metrics["id"].isin(financials["REGION_CATEGORY"])].copy()
    new_rows.to_csv("migration/v1_0_0/assets/epam_financial_metrics_region_category_sr.csv", index=False)


def save_to_csv():
    for dataset_id, df in df_map.items():
        file_name = title_datasets[dataset_id]
        if not os.path.exists(processed_path):
            os.makedirs(processed_path)
        df.to_csv(f"{processed_path}{file_name}.csv", index=False)
        logger.info(f"Saved {file_name}")
    logger.info("All datasets written to csv successfully")


if __name__ == "__main__":
    create_business_category_codelist()
    create_region_category_codelist()
    normalize_job_titles()
    create_titles_codelist()
    remap_titles()
    save_to_csv()
