{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["## It's a notebook created to help developers retrain the \"USSR File Processing\" provider predictor model and encoder when new providers come in or maybe training data becomes irrelevant. It'll be a step-by-step guide in order to cover different scenarios and situations."], "metadata": {"id": "cU_xyk2xpAXj"}}, {"cell_type": "markdown", "source": ["## Scenario 1: We don't have any pickled data to use, or we want to create a new one"], "metadata": {"id": "rPDrVCdNq5P4"}}, {"cell_type": "markdown", "source": ["In this case, we need to create a local folder (in this case called `providers_data`).\n", "\n", "The hierarchy of files and folders should be:\n", "\n", "\n", "     providers_data/{name of provider like it's declared in `ProviderNames`, example: MANUAL_RESEARCH}/\n", "\n", "         {raw files of that provider — ideally 5 or 10 per provider}\n", "\n", "Step 1: Then we need to zip that folder and load in our notebook"], "metadata": {"id": "KQun-rh4rcJH"}}, {"cell_type": "code", "source": ["import os\n", "import pandas as pd\n", "import zipfile\n", "\n", "def unzip_folder(zip_filepath, extract_dir):\n", "    try:\n", "        with zipfile.ZipFile(zip_filepath, 'r') as zip_ref:\n", "            zip_ref.extractall(extract_dir)\n", "        print(f\"Successfully unzipped {zip_filepath} to {extract_dir}\")\n", "    except FileNotFoundError:\n", "        print(f\"Error: Zip file not found at {zip_filepath}\")\n", "    except zipfile.BadZipFile:\n", "        print(f\"Error: Invalid zip file at {zip_filepath}\")\n", "    except Exception as e:\n", "        print(f\"An unexpected error occurred: {e}\")\n", "\n", "extract_dir = \"/content\"\n", "zip_filepath = \"/content/providers_data.zip\"\n", "\n", "unzip_folder(zip_filepath, extract_dir)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pbkLuU5f4DLd", "outputId": "5035c599-7673-45a8-9c56-69c5e6875124"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Successfully unzipped /content/providers_data.zip to /content\n"]}]}, {"cell_type": "code", "source": ["def build_folder_file_dict(root_dir):\n", "    folder_file_dict = {}\n", "\n", "    for folder_name in os.listdir(root_dir):\n", "        folder_path = os.path.join(root_dir, folder_name)\n", "\n", "        if os.path.isdir(folder_path):\n", "            files = []\n", "            for file_name in os.listdir(folder_path):\n", "                file_path = os.path.join(folder_path, file_name)\n", "                if os.path.isfile(file_path):\n", "                    files.append(file_path)\n", "\n", "            folder_file_dict[folder_name] = files\n", "\n", "    return folder_file_dict\n", "\n", "root_directory = \"/content/providers_data/\"\n", "folder_files = build_folder_file_dict(root_directory)\n", "\n", "for folder, files in folder_files.items():\n", "    print(f\"Folder: {folder}\")\n", "    for file in files:\n", "        print(f\"  - {file}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xGIiGCmy5Ibs", "outputId": "329c4173-9c20-4c2b-93f9-ae0e610d9877"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Folder: COMPLIMENTARY_FINDINGS\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Teva Pharma - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/QIAGEN - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Merck Complementary Findings to Draup 2.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/S&P - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Ascend Learning - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/The New York Times Co - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Cisco - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Fresenius Kabi_Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Grammarly - Account Complementary Findings.xlsx\n", "  - /content/providers_data/providers_data/COMPLIMENTARY_FINDINGS/Böhringer Ingelheim Complementary Findings to Draup.xlsx\n", "Folder: MANUAL_RESEARCH\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Disprz - Digital Report (1).xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Converse - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Daiichi Sankyo Inc - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Daiichi Sankyo Europe - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Marathon Health - Digital Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Johns Hopkins University Press - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/The Stepstone Group - Digital Account Intelligence.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/CRH Americas Materials - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/HAVI - Digital Account Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH/Business Insider - Digital Account Report.xlsx\n", "Folder: MANUAL_RESEARCH_COMPETITORS\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH_COMPETITORS/Hexaware - Competitor Intelligence Report.xlsx\n", "  - /content/providers_data/providers_data/MANUAL_RESEARCH_COMPETITORS/Accenture - Manual Report.xlsx\n", "Folder: TALENT_DEMAND\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/The Stepstone Group GmbH - Talent Demand Visualization.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Talent Demand Visualization 03-02-25 02_54.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Business Insider - Talent Demand Visualization.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Intellum Talent Demand Visualization 04-09-24 05_20 (1).xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/well.ca Talent Demand Visualization 09-08-24 11_43 (1).xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/HAVI - Talent Demand Visualization.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Hachette Book Group - Draup Talent Demand Visualization 21-11-24 03_29.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Tokio Marine HCC Talent Demand Visualization 04-09-24 01_11 (1).xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Converse - Talent Demand.xlsx\n", "  - /content/providers_data/providers_data/TALENT_DEMAND/Talent Demand Visualization 27-01-25 10_48.xlsx\n", "Folder: HIRE_EZ\n", "  - /content/providers_data/providers_data/HIRE_EZ/Premium Credit Ltd - HireEZ (1).xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Hachette Book Group (IT workforce).xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Accor - HireEZ report.xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Intellum - HireEZ report (1).xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Wolters Kluwer HireEZ.xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Tokio Marine HCC HireEZ (1).xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Hexaware - HireEz.xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Well.ca - HireEZ (1).xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Questex - HireEZ report.xlsx\n", "  - /content/providers_data/providers_data/HIRE_EZ/Everest HireEZ.xlsx\n", "Folder: PRIORITIES\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report - Copy (3).xlsx\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report - Copy.xlsx\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report - Copy (2).xlsx\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report - Copy (4).xlsx\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report - Copy (5).xlsx\n", "  - /content/providers_data/providers_data/PRIORITIES/Nike - Priorities report.xlsx\n", "Folder: DRAUP\n", "  - /content/providers_data/providers_data/DRAUP/S&P Global Inc _Profile Data1.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Merck KGaA_Profile Data 2.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Ascend Learning_Profile Data.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Cisco Systems, Inc _Profile Data - Raw.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Fresenius Kabi_Profile Data.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/The New York Times Company_Profile Data_raw (1).xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Teva Pharmaceutical Industries Ltd _Profile Data raw.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Grammarly Inc _Profile Data Original.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/C  H  <PERSON>ringer Sohn AG & Co  KG_Profile Data.xlsx\n", "  - /content/providers_data/providers_data/DRAUP/Honeywell International_Profile Data.xlsx\n"]}]}, {"cell_type": "markdown", "source": ["Now we have a dictionary:\n", "`Provider name : path to Excel files`"], "metadata": {"id": "yuLeUjGMvoQK"}}, {"cell_type": "markdown", "source": ["### Step 2: we need to build features from our excel files. What are the features? In our case, features will be the unique sheet names from our providers."], "metadata": {"id": "YVcnuAkXwYry"}}, {"cell_type": "code", "source": ["all_sheet_names = []\n", "unique_sheet_names = set()\n", "\n", "def get_sheet_names(excel_file_path):\n", "    try:\n", "        excel_file = pd.ExcelFile(excel_file_path)\n", "        return excel_file.sheet_names\n", "    except Exception as e:\n", "        print(f\"Error reading {excel_file_path}\")\n", "        raise e\n", "\n", "for target, paths in folder_files.items():\n", "  for path in paths:\n", "    sheet_names = get_sheet_names(path)\n", "    if path.endswith(('.xls', '.xlsx')):\n", "        sheet_names = get_sheet_names(path)\n", "        if sheet_names:\n", "            sheet_names = ['About' if name.startswith('About') else name for name in sheet_names]\n", "            all_sheet_names.append((target, sheet_names))\n", "            unique_sheet_names.update(sheet_names)"], "metadata": {"id": "Zmz4Al_bQv-t"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["### Now we have a set of unique sheet names and all sheet names from each provider."], "metadata": {"id": "gpCPxl_ex2QA"}}, {"cell_type": "code", "source": ["list(unique_sheet_names)[:10]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h0hpCEr7yO83", "outputId": "13711c77-3289-4f37-cee4-05df0ea8773c"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['Revenue by Region',\n", " 'Attrition_by_diversities',\n", " 'Job Type in Demand',\n", " 'InfoNgen',\n", " 'Service Provider Partners',\n", " 'Management Team Promo',\n", " 'Digital Initiatives',\n", " 'General Overview',\n", " 'hiddenSheet',\n", " 'Titles distribution']"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["len(unique_sheet_names)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_Hb__tquSLxt", "outputId": "bb3ffa23-4132-4cb6-81ed-9120e2b025c2"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["105"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "markdown", "source": ["### Here's what we're doing - we're turning our sheet names into features that our model can actually work with.\n", "### So we start with an empty list to collect our data. Then for each provider, we create a row where every unique sheet name becomes a column with either 0 or 1. If that provider has a particular sheet name, we mark it as 1, otherwise it stays 0. This is basically one-hot encoding - we're converting categorical data into numbers.\n", "### We also add the target column which is our provider name - that's what we want to predict. Then we stick all these rows into a DataFrame.\n", "### The last bit just reorders the columns so 'target' comes first, then all the feature columns. Makes it cleaner to look at.\n", "### What we end up with is a nice structured dataset where each row is a provider and each column shows whether they have that specific sheet name or not. Now our machine learning model can actually understand this data"], "metadata": {"id": "9OxQHHazz6FM"}}, {"cell_type": "code", "source": ["data_list = []\n", "\n", "for target, values in all_sheet_names:\n", "    row_data = {feature: 0 for feature in unique_sheet_names}\n", "    for feature in values:\n", "        if feature in unique_sheet_names:\n", "            row_data[feature] = 1\n", "    row_data['target'] = target\n", "    data_list.append(row_data)\n", "\n", "df = pd.DataFrame(data_list)\n", "\n", "# Reorder columns with target first, then sorted feature columns\n", "target_col = df['target']\n", "sorted_cols = sorted([col for col in df.columns if col != 'target'])\n", "df = df[['target'] + sorted_cols]\n", "\n", "df.head()"], "metadata": {"id": "DATPVtnAUvYy", "colab": {"base_uri": "https://localhost:8080/", "height": 290}, "outputId": "06876daa-582f-4ef0-dbb6-fc85d0afd585"}, "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                   target  About  Acquisitions  Additional Information  \\\n", "0  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "1  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "2  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "3  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "4  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "\n", "   Attrition_by_diversities  Attrition_by_functions  Attrition_by_locations  \\\n", "0                         0                       0                       0   \n", "1                         0                       0                       0   \n", "2                         0                       0                       0   \n", "3                         0                       0                       0   \n", "4                         0                       0                       0   \n", "\n", "   Average market salary(USD)  Awards & Recognitions  Benefits  ...  Summary  \\\n", "0                           0                      0         0  ...        0   \n", "1                           0                      0         0  ...        0   \n", "2                           0                      0         0  ...        0   \n", "3                           0                      0         0  ...        0   \n", "4                           0                      0         0  ...        0   \n", "\n", "   Talent move_by_companies  Talent move_by_locations  Talent move_by_time  \\\n", "0                         0                         0                    0   \n", "1                         0                         0                    0   \n", "2                         0                         0                    0   \n", "3                         0                         0                    0   \n", "4                         0                         0                    0   \n", "\n", "   Tech Stack  Titles distribution  Workloads  Workloads in Demand  \\\n", "0           0                    0          0                    0   \n", "1           0                    0          0                    0   \n", "2           0                    0          0                    0   \n", "3           0                    0          0                    0   \n", "4           0                    0          0                    0   \n", "\n", "   Years of experience  hiddenSheet  \n", "0                    0            0  \n", "1                    0            0  \n", "2                    0            0  \n", "3                    0            0  \n", "4                    0            0  \n", "\n", "[5 rows x 106 columns]"], "text/html": ["\n", "  <div id=\"df-99d74d6a-52f3-486b-9ba4-20b0262156df\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target</th>\n", "      <th>About</th>\n", "      <th>Acquisitions</th>\n", "      <th>Additional Information</th>\n", "      <th>Attrition_by_diversities</th>\n", "      <th>Attrition_by_functions</th>\n", "      <th>Attrition_by_locations</th>\n", "      <th>Average market salary(USD)</th>\n", "      <th>Awards &amp; Recognitions</th>\n", "      <th>Benefits</th>\n", "      <th>...</th>\n", "      <th>Summary</th>\n", "      <th>Talent move_by_companies</th>\n", "      <th>Talent move_by_locations</th>\n", "      <th>Talent move_by_time</th>\n", "      <th><PERSON></th>\n", "      <th>Titles distribution</th>\n", "      <th>Workloads</th>\n", "      <th>Workloads in Demand</th>\n", "      <th>Years of experience</th>\n", "      <th>hiddenSheet</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 106 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-99d74d6a-52f3-486b-9ba4-20b0262156df')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-99d74d6a-52f3-486b-9ba4-20b0262156df button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-99d74d6a-52f3-486b-9ba4-20b0262156df');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-7a52bbe0-bb8e-4ed4-a208-86eb24a49588\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-7a52bbe0-bb8e-4ed4-a208-86eb24a49588')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-7a52bbe0-bb8e-4ed4-a208-86eb24a49588 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "markdown", "source": ["### Step 3: The next very important step is to encode our target class. Some models like CatBoost can encode them by themselves in the training process, but we're going to use a simple light model and we also need an encoder model to reuse it in our processing script."], "metadata": {"id": "Qg3A_Ejk04KD"}}, {"cell_type": "code", "source": ["from sklearn.preprocessing import LabelEncoder\n", "\n", "le = LabelEncoder()\n", "\n", "df['target'] = le.fit_transform(df['target'])\n", "\n", "df.head()"], "metadata": {"id": "w6MpAq8ObNid", "colab": {"base_uri": "https://localhost:8080/", "height": 290}, "outputId": "2b9a53ea-9137-445e-cdbc-8d22bcf6fdd3"}, "execution_count": 8, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   target  About  Acquisitions  Additional Information  \\\n", "0       0      0             1                       1   \n", "1       0      0             1                       1   \n", "2       0      0             1                       1   \n", "3       0      0             1                       1   \n", "4       0      0             1                       1   \n", "\n", "   Attrition_by_diversities  Attrition_by_functions  Attrition_by_locations  \\\n", "0                         0                       0                       0   \n", "1                         0                       0                       0   \n", "2                         0                       0                       0   \n", "3                         0                       0                       0   \n", "4                         0                       0                       0   \n", "\n", "   Average market salary(USD)  Awards & Recognitions  Benefits  ...  Summary  \\\n", "0                           0                      0         0  ...        0   \n", "1                           0                      0         0  ...        0   \n", "2                           0                      0         0  ...        0   \n", "3                           0                      0         0  ...        0   \n", "4                           0                      0         0  ...        0   \n", "\n", "   Talent move_by_companies  Talent move_by_locations  Talent move_by_time  \\\n", "0                         0                         0                    0   \n", "1                         0                         0                    0   \n", "2                         0                         0                    0   \n", "3                         0                         0                    0   \n", "4                         0                         0                    0   \n", "\n", "   Tech Stack  Titles distribution  Workloads  Workloads in Demand  \\\n", "0           0                    0          0                    0   \n", "1           0                    0          0                    0   \n", "2           0                    0          0                    0   \n", "3           0                    0          0                    0   \n", "4           0                    0          0                    0   \n", "\n", "   Years of experience  hiddenSheet  \n", "0                    0            0  \n", "1                    0            0  \n", "2                    0            0  \n", "3                    0            0  \n", "4                    0            0  \n", "\n", "[5 rows x 106 columns]"], "text/html": ["\n", "  <div id=\"df-7b3deb3f-6ce0-48d4-a687-78043a8e1759\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target</th>\n", "      <th>About</th>\n", "      <th>Acquisitions</th>\n", "      <th>Additional Information</th>\n", "      <th>Attrition_by_diversities</th>\n", "      <th>Attrition_by_functions</th>\n", "      <th>Attrition_by_locations</th>\n", "      <th>Average market salary(USD)</th>\n", "      <th>Awards &amp; Recognitions</th>\n", "      <th>Benefits</th>\n", "      <th>...</th>\n", "      <th>Summary</th>\n", "      <th>Talent move_by_companies</th>\n", "      <th>Talent move_by_locations</th>\n", "      <th>Talent move_by_time</th>\n", "      <th><PERSON></th>\n", "      <th>Titles distribution</th>\n", "      <th>Workloads</th>\n", "      <th>Workloads in Demand</th>\n", "      <th>Years of experience</th>\n", "      <th>hiddenSheet</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 106 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7b3deb3f-6ce0-48d4-a687-78043a8e1759')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7b3deb3f-6ce0-48d4-a687-78043a8e1759 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7b3deb3f-6ce0-48d4-a687-78043a8e1759');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-6f282dc2-ea5d-4f9b-ae05-8c007527fe29\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6f282dc2-ea5d-4f9b-ae05-8c007527fe29')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-6f282dc2-ea5d-4f9b-ae05-8c007527fe29 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "markdown", "source": ["### Now we can see that our target class is encoded"], "metadata": {"id": "0it1Bht-1wj6"}}, {"cell_type": "code", "source": ["le.classes_"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X0TY3ssRfmKA", "outputId": "8bcede35-9790-48fd-d1f6-7a5402613135"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array(['COMPLIMENTARY_FINDINGS', 'DRAUP', 'HIRE_EZ', 'MANUAL_RESEARCH',\n", "       'MANUAL_RESEARCH_COMPETITORS', 'PRIORITIES', 'TALENT_DEMAND'],\n", "      dtype=object)"]}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "code", "source": ["le.transform(le.classes_)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "klUxDnehcJDB", "outputId": "c9be7c6e-2856-42d3-fcb4-c870422af7a9"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0, 1, 2, 3, 4, 5, 6])"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "markdown", "source": ["### Next step is optional, that's why I commented it.\n", "### We only need to use it if we don't have enough files per provider, for example we have only 2-3 files per provider. SMOTE gonna generate similar synthetic data just in order to increase our training sample."], "metadata": {"id": "DcZ_Tywf2DQK"}}, {"cell_type": "code", "source": ["# from imblearn.over_sampling import SMOTE\n", "\n", "# X = df.drop('target', axis=1)\n", "# y = df['target']\n", "\n", "# smote = SMOTE(random_state=42, k_neighbors=1)\n", "\n", "# X_resampled, y_resampled = smote.fit_resample(X, y)\n", "\n", "# df = pd.DataFrame(X_resampled, columns=X.columns)\n", "# df['target'] = y_resampled\n", "\n", "# print(df['target'].value_counts())"], "metadata": {"id": "sw4wwwsnxhUQ"}, "execution_count": 11, "outputs": []}, {"cell_type": "markdown", "source": ["### Now it's time for the final step — training and evaluating our model.\n", "\n", "I chose <PERSON><PERSON><PERSON> as the model, since it is based on a statistical probability model\n", "\n", "`𝑃(target∣sheet_names)`\n", "\n", "I believe it's a good choice because our features are binary, and this model is both simple and lightweight."], "metadata": {"id": "nSqUi4Ul3CJp"}}, {"cell_type": "code", "source": ["from sklearn.naive_bayes import BernoulliNB\n", "from sklearn.model_selection import cross_val_score\n", "import numpy as np\n", "\n", "X = df.drop('target', axis=1)\n", "y = df['target']\n", "\n", "bnb = BernoulliNB()\n", "\n", "from sklearn.model_selection import StratifiedKFold\n", "\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "cv_scores = cross_val_score(bnb, X, y, cv=cv)\n", "\n", "print(\"Cross-validation scores:\", cv_scores*100)\n", "print(\"Mean cross-validation score:\", np.mean(cv_scores)*100)"], "metadata": {"id": "8Mpv4EBYbtpo", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4c4ac4aa-cf27-47bb-d2b6-dc915989cfbb"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cross-validation scores: [100. 100. 100. 100. 100.]\n", "Mean cross-validation score: 100.0\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/model_selection/_split.py:805: UserWarning: The least populated class in y has only 2 members, which is less than n_splits=5.\n", "  warnings.warn(\n"]}]}, {"cell_type": "markdown", "source": ["#### As we can see, accuracy of our model is 100%. It's expected, because all our target providers have different sheet names. But if we face issues in the future, we can create additional deep features like column names in each sheet and etc. But at the moment we don't need it."], "metadata": {"id": "5Zc8jGN16RSW"}}, {"cell_type": "markdown", "source": ["### Now let's train our model using whole training set"], "metadata": {"id": "lcsLgY1_7I6U"}}, {"cell_type": "code", "source": ["bnb.fit(X, y)"], "metadata": {"id": "sBW7s6O5xJoL", "colab": {"base_uri": "https://localhost:8080/", "height": 80}, "outputId": "b8f2c9bc-6230-4370-8b0d-2f48193acdf5"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["BernoulliNB()"], "text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>BernoulliNB()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>BernoulliNB</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.naive_bayes.BernoulliNB.html\">?<span>Documentation for BernoulliNB</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>BernoulliNB()</pre></div> </div></div></div></div>"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "markdown", "source": ["### The final step is to save our model, encoder and base data which is:\n", "`list of (target, [sheet names])`"], "metadata": {"id": "fvvWZ1ie7s6r"}}, {"cell_type": "code", "source": ["import pickle\n", "\n", "with open('bnb_model.pkl', 'wb') as file:\n", "    pickle.dump(bnb, file)\n", "\n", "with open('base_data.pkl', 'wb') as f:\n", "    pickle.dump(all_sheet_names, f)\n", "\n", "with open('label_encoder.pkl', 'wb') as file:\n", "    pickle.dump(le, file)"], "metadata": {"id": "fKRcllNkcfKw"}, "execution_count": 14, "outputs": []}, {"cell_type": "markdown", "source": ["## Scenario 2: We have pickled data to train model"], "metadata": {"id": "-tOHl8vR8wxy"}}, {"cell_type": "markdown", "source": ["### Let's load our saved historical data"], "metadata": {"id": "jHQozp8F-enH"}}, {"cell_type": "code", "source": ["loaded_data = pickle.load(open('base_data.pkl', 'rb'))"], "metadata": {"id": "Ff5_Z2MN9BEZ"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["loaded_data[:1]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YVVIhlBa-U9h", "outputId": "5a1ec640-fc73-4f43-d9c0-b06d0303c231"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[('COMPLIMENTARY_FINDINGS',\n", "  ['Miscellaneous',\n", "   'Funding Rounds',\n", "   'Current Engagements',\n", "   'Financial Trend',\n", "   'Categorized Revenue',\n", "   'Company Entities',\n", "   'Business Structure',\n", "   'Featured Clients',\n", "   'Strategy, Risks and Initiatives',\n", "   'Partnerships',\n", "   'Acquisitions',\n", "   'Mergers',\n", "   'Investments',\n", "   'Internal Projects',\n", "   'Key People',\n", "   'Connections',\n", "   'Additional Information',\n", "   'SWOT Analysis',\n", "   'InfoNgen'])]"]}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["def get_sheet_names(excel_file_path):\n", "    try:\n", "        excel_file = pd.ExcelFile(excel_file_path)\n", "        return excel_file.sheet_names\n", "    except Exception as e:\n", "        print(f\"Error reading {excel_file_path}\")\n", "        raise e"], "metadata": {"id": "m-ssdTpgF45f"}, "execution_count": 21, "outputs": []}, {"cell_type": "markdown", "source": ["### If we need to add a new Provider or data to that training set:"], "metadata": {"id": "E_dVQS6kF8Nm"}}, {"cell_type": "code", "source": ["def add_provider(provider_name, excel_file_path):\n", "  sheet_names = get_sheet_names(excel_file_path)\n", "  loaded_data.append((provider_name, sheet_names))\n", "  return loaded_data\n", "\n", "#Example: loaded_data = add_provider(\"DRAUP\", link to draup excel)"], "metadata": {"id": "exu3X75aF6mv"}, "execution_count": 22, "outputs": []}, {"cell_type": "markdown", "source": ["### generating features set"], "metadata": {"id": "X5RuUNhhHI6N"}}, {"cell_type": "code", "source": ["unique_sheet_names = set()\n", "for _, values in loaded_data:\n", "    unique_sheet_names.update(values)"], "metadata": {"id": "mFrWWWhtHDZ-"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["data_list = []\n", "\n", "for target, values in loaded_data:\n", "    row_data = {feature: 0 for feature in unique_sheet_names}\n", "    for feature in values:\n", "        if feature in unique_sheet_names:\n", "            row_data[feature] = 1\n", "    row_data['target'] = target\n", "    data_list.append(row_data)\n", "\n", "df = pd.DataFrame(data_list)\n", "\n", "# Reorder columns with target first, then sorted feature columns\n", "target_col = df['target']\n", "sorted_cols = sorted([col for col in df.columns if col != 'target'])\n", "df = df[['target'] + sorted_cols]\n", "\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 290}, "id": "LgylM5N0-V_w", "outputId": "3b487325-669f-4bb0-8017-c8ebb9b33600"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                   target  About  Acquisitions  Additional Information  \\\n", "0  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "1  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "2  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "3  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "4  COMPLIMENTARY_FINDINGS      0             1                       1   \n", "\n", "   Attrition_by_diversities  Attrition_by_functions  Attrition_by_locations  \\\n", "0                         0                       0                       0   \n", "1                         0                       0                       0   \n", "2                         0                       0                       0   \n", "3                         0                       0                       0   \n", "4                         0                       0                       0   \n", "\n", "   Average market salary(USD)  Awards & Recognitions  Benefits  ...  Summary  \\\n", "0                           0                      0         0  ...        0   \n", "1                           0                      0         0  ...        0   \n", "2                           0                      0         0  ...        0   \n", "3                           0                      0         0  ...        0   \n", "4                           0                      0         0  ...        0   \n", "\n", "   Talent move_by_companies  Talent move_by_locations  Talent move_by_time  \\\n", "0                         0                         0                    0   \n", "1                         0                         0                    0   \n", "2                         0                         0                    0   \n", "3                         0                         0                    0   \n", "4                         0                         0                    0   \n", "\n", "   Tech Stack  Titles distribution  Workloads  Workloads in Demand  \\\n", "0           0                    0          0                    0   \n", "1           0                    0          0                    0   \n", "2           0                    0          0                    0   \n", "3           0                    0          0                    0   \n", "4           0                    0          0                    0   \n", "\n", "   Years of experience  hiddenSheet  \n", "0                    0            0  \n", "1                    0            0  \n", "2                    0            0  \n", "3                    0            0  \n", "4                    0            0  \n", "\n", "[5 rows x 106 columns]"], "text/html": ["\n", "  <div id=\"df-9b835a1e-9382-4211-861c-07837280e0c1\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target</th>\n", "      <th>About</th>\n", "      <th>Acquisitions</th>\n", "      <th>Additional Information</th>\n", "      <th>Attrition_by_diversities</th>\n", "      <th>Attrition_by_functions</th>\n", "      <th>Attrition_by_locations</th>\n", "      <th>Average market salary(USD)</th>\n", "      <th>Awards &amp; Recognitions</th>\n", "      <th>Benefits</th>\n", "      <th>...</th>\n", "      <th>Summary</th>\n", "      <th>Talent move_by_companies</th>\n", "      <th>Talent move_by_locations</th>\n", "      <th>Talent move_by_time</th>\n", "      <th><PERSON></th>\n", "      <th>Titles distribution</th>\n", "      <th>Workloads</th>\n", "      <th>Workloads in Demand</th>\n", "      <th>Years of experience</th>\n", "      <th>hiddenSheet</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>COMPLIMENTARY_FINDINGS</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 106 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9b835a1e-9382-4211-861c-07837280e0c1')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-9b835a1e-9382-4211-861c-07837280e0c1 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-9b835a1e-9382-4211-861c-07837280e0c1');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-acca3ae7-a2a0-4088-a4f1-d2e254edf0b9\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-acca3ae7-a2a0-4088-a4f1-d2e254edf0b9')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-acca3ae7-a2a0-4088-a4f1-d2e254edf0b9 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "markdown", "source": ["### Now we can continue from step 3 in the scenario above"], "metadata": {"id": "spcBGNjoFt3X"}}]}