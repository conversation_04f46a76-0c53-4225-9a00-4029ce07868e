# Azure Container App Job

1. Run `poetry build` to build the package locally.
2. Build and run the container to debug locally. Project folder will be mounted on /app directory, .env file should contain proper credentials for the environment. The container build will try to pull the package from the PIP repository if it's not found locally in the `/dist` folder.

```bash
docker build \
  --build-arg PIP_EXTRA_INDEX_URL=$PIP_EXTRA_INDEX_URL \
  --build-arg PIPELINE_VERSION=$PIPELINE_VERSION \
  -t se-data-pipeline:$PIPELINE_VERSION .

docker run --rm -it --name c-se-data-pipeline -v $(pwd):/app --env-file .env se-data-pipeline:$PIPELINE_VERSION

#windows CMD

docker build ^
  --build-arg PIP_EXTRA_INDEX_URL=%PIP_EXTRA_INDEX_URL% ^
  --build-arg PIPELINE_VERSION=%PIPELINE_VERSION% ^
  -t se-data-pipeline:%PIPELINE_VERSION%

docker run --rm -it --name c-se-data-pipeline -v %cd%:/app --env-file .env se-data-pipeline:%PIPELINE_VERSION%
```

Building and pushing container to ACR.

```bash
az acr login --name $ACR_NAME

docker build \
  --build-arg PIP_EXTRA_INDEX_URL=$PIP_EXTRA_INDEX_URL \
  --build-arg PIPELINE_VERSION=$PIPELINE_VERSION \
  -t se-data-pipeline:$PIPELINE_VERSION .

docker tag se-data-pipeline:$PIPELINE_VERSION $ACR_NAME.azurecr.io/se-data-pipeline:latest

docker push $ACR_NAME.azurecr.io/se-data-pipeline:latest

#windows CMD

docker build ^
  --build-arg PIP_EXTRA_INDEX_URL=%PIP_EXTRA_INDEX_URL% ^
  --build-arg PIPELINE_VERSION=%PIPELINE_VERSION% ^
  -t se-data-pipeline:%PIPELINE_VERSION%

docker tag se-data-pipeline:%PIPELINE_VERSION% %ACR_NAME%.azurecr.io/se-data-pipeline:latest

docker push %ACR_NAME%.azurecr.io/se-data-pipeline:latest
```
